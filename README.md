# Road Server

一个基于 Spring Boot 的服务器项目，包含云存储代理模块。

## 项目结构

```
roadserver/
├── roadserver-app/    # 主应用模块
└── s3-proxy/          # S3 云存储代理模块
```

## 模块说明

### roadserver-app

主应用模块，包含业务逻辑和 Web 接口。

### s3-proxy

S3 云存储代理模块，提供统一的云存储操作接口，支持多种云存储提供商：

- AWS S3
- MinIO
- 阿里云 OSS

## 构建与运行

### 构建整个项目

```bash
mvn clean package
```

### 运行应用

```bash
java -jar roadserver-app/target/roadserver-app.jar
```

## S3-Proxy 模块使用示例

```java
// 创建 S3 配置
S3Config config = S3Config.builder()
    .provider("minio")
    .endpoint("http://minio.example.com")
    .accessKey("accessKey")
    .secretKey("secretKey")
    .bucket("images")
    .build();

// 注入服务
@Autowired
private S3ProxyService s3ProxyService;

// 上传文件
File file = new File("/path/to/file.jpg");
UploadResult result = s3ProxyService.uploadFile(config, file, null);

if (result.isSuccess()) {
    System.out.println("上传成功: " + result.getUrl());
} else {
    System.out.println("上传失败: " + result.getErrorMessage());
}

// 下载网络资源并上传
String imageUrl = "https://example.com/image.jpg";
UploadResult result = s3ProxyService.downloadAndUpload(config, imageUrl, null);
``` 