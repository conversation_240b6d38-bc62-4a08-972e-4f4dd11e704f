package com.maixi.road.s3proxy.api.impl;

import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.api.AbstractS3Api;
import com.maixi.road.s3proxy.dto.ExistRes;
import com.maixi.road.s3proxy.exception.S3Exception;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.io.File;
import java.net.URI;

@Slf4j
public class AwsS3Client extends AbstractS3Api {

    private final S3Client s3Client;

    public AwsS3Client(S3Config config) {
        super(config);
        AwsBasicCredentials credentials = AwsBasicCredentials.create(
                config.getAccessKey(),
                config.getSecretKey());
        S3Configuration serviceConfiguration = S3Configuration.builder()
                .pathStyleAccessEnabled(false)
                .build();

        this.s3Client = S3Client.builder()
                .endpointOverride(URI.create(config.getEndpoint()))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.of(config.getRegion()))
                .serviceConfiguration(serviceConfiguration)
                .build();
    }

    @Override
    public String upload(File file, String objectKey) {
        try {
            // 构建请求体
            RequestBody requestBody = RequestBody.fromFile(file);

            // 构建上传请求
            PutObjectRequest.Builder requestBuilder = PutObjectRequest.builder()
                    .bucket(config.getBucket())
                    .key(getFullObjectName(objectKey));

            // 设置内容类型（如果提供）
            // requestBuilder.contentType(FileUtils.getContentType(file.getName()));

            // 执行上传
            PutObjectRequest putObjectRequest = requestBuilder.build();
            s3Client.putObject(putObjectRequest, requestBody);

            // 返回URL
            return getObjectUrl(objectKey);
        } catch (Exception e) {
            log.error("Aws S3 上传文件失败: ", e);
            throw new S3Exception(e.getMessage());
        }
    }

    @Override
    public String getObjectUrl(String objectKey) {
        if (StringUtils.isNotBlank(config.getExtraParam("customDomain"))) {
            return String.format("%s/%s",
                    config.getExtraParam("customDomain"),
                    getFullObjectName(objectKey));
        }
        return String.format("https://%s.%s/%s",
                config.getBucket(),
                config.getEndpoint().replace("https://", ""),
                getFullObjectName(objectKey));
    }

    @Override
    public ExistRes doesObjectExist(String objectKey) {
        try {
            // 使用HeadObject操作检查对象是否存在
            s3Client.headObject(HeadObjectRequest.builder()
                    .bucket(config.getBucket())
                    .key(getFullObjectName(objectKey))
                    .build());

            return ExistRes.builder().exist(true).url(getObjectUrl(objectKey)).build();
        } catch (Exception e) {
            return ExistRes.builder().exist(false).url(null).build();
        }
    }

}
