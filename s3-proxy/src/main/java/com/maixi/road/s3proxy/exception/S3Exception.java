package com.maixi.road.s3proxy.exception;

/**
 * S3操作异常
 * 封装S3操作过程中发生的异常
 */
public class S3Exception extends RuntimeException {
    
    /**
     * 构造函数
     * 
     * @param message 错误信息
     */
    public S3Exception(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误信息
     * @param cause 原始异常
     */
    public S3Exception(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param cause 原始异常
     */
    public S3Exception(Throwable cause) {
        super(cause);
    }
} 