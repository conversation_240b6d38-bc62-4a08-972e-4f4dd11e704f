package com.maixi.road.s3proxy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 上传结果对象
 * 包含上传操作的结果信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadResult {
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 对象名称
     */
    private String objectName;
    
    /**
     * 访问URL
     */
    private String url;
    
    /**
     * 文件大小
     */
    private long size;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 创建成功结果
     * 
     * @param objectName 对象名称
     * @param url 访问URL
     * @param size 文件大小
     * @param contentType 内容类型
     * @return 上传结果
     */
    public static UploadResult success(String objectName, String url, 
                                      long size, String contentType) {
        return UploadResult.builder()
                .success(true)
                .objectName(objectName)
                .url(url)
                .size(size)
                .contentType(contentType)
                .build();
    }
    
    /**
     * 创建失败结果
     * 
     * @param errorMessage 错误信息
     * @return 上传结果
     */
    public static UploadResult failure(String errorMessage) {
        return UploadResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
} 