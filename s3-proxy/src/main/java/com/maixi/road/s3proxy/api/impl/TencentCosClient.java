package com.maixi.road.s3proxy.api.impl;

import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.api.AbstractS3Api;
import com.maixi.road.s3proxy.dto.ExistRes;
import com.maixi.road.s3proxy.exception.S3Exception;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

@Slf4j
public class TencentCosClient extends AbstractS3Api {

    private final COSClient cosClient;

    public TencentCosClient(S3Config config) {
        super(config);
        COSCredentials cred = new BasicCOSCredentials(config.getAccessKey(), config.getSecretKey());
        ClientConfig clientConfig = new ClientConfig(new Region(config.getRegion()));
        cosClient = new COSClient(cred, clientConfig);
    }

    @Override
    public String upload(File file, String objectKey) {
        try {
            cosClient.putObject(config.getBucket(), getFullObjectName(objectKey), file);
            return getObjectUrl(objectKey);
        } catch (Exception e) {
            log.error("Tencent COS 上传文件失败: ", e);
            throw new S3Exception(e.getMessage());
        }
    }

    @Override
    public String getObjectUrl(String objectKey) {
        return String.format("https://%s.%s/%s", config.getBucket(), config.getEndpoint().replace("https://", ""),
                getFullObjectName(objectKey));
    }

    @Override
    public ExistRes doesObjectExist(String objectKey) {
        try {
            cosClient.getObjectMetadata(config.getBucket(), getFullObjectName(objectKey));
            return ExistRes.builder().exist(true).url(getObjectUrl(objectKey)).build();
        } catch (Exception e) {
            return ExistRes.builder().exist(false).url(null).build();
        }
    }
}
