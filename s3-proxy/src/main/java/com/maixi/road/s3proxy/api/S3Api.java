package com.maixi.road.s3proxy.api;

import com.maixi.road.s3proxy.dto.ExistRes;

import java.io.File;

/**
 * S3 API接口
 * 定义与S3兼容的云存储交互的基本操作
 */
public interface S3Api {
    
    /**
     * 上传对象
     *
     * @param file 文件
     * @param objectKey 对象名称
     * @return 对象的URL
     */
    String upload(File file, String objectKey);
    
    /**
     * 获取对象的URL
     *
     * @param objectKey 对象名称
     * @return 对象的URL
     */
    String getObjectUrl(String objectKey);
    
    
    /**
     * 检查对象是否存在
     *
     * @param objectKey 对象名称
     * @return 是否存在
     */
    ExistRes doesObjectExist(String objectKey);
} 