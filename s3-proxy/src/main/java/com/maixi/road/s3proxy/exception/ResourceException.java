package com.maixi.road.s3proxy.exception;

/**
 * 资源操作异常
 * 封装资源操作过程中发生的异常
 */
public class ResourceException extends RuntimeException {
    
    /**
     * 构造函数
     * 
     * @param message 错误信息
     */
    public ResourceException(String message) {
        super(message);
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误信息
     * @param cause 原始异常
     */
    public ResourceException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param cause 原始异常
     */
    public ResourceException(Throwable cause) {
        super(cause);
    }
} 