package com.maixi.road.s3proxy.core;

import com.maixi.road.common.core.utils.UrlUtils;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.exception.ResourceException;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.TimeUnit;

/**
 * 网络资源下载器
 * 负责下载网络资源并上传到云存储
 */
@Slf4j
@Component
public class ResourceDownloader {

    /**
     * OkHttpClient 实例，用于复用连接
     */
    private OkHttpClient okHttpClient;

    /**
     * 初始化 OkHttpClient
     */
    @PostConstruct
    public void init() {
        // 配置连接池
        ConnectionPool connectionPool = new ConnectionPool(10, 5, TimeUnit.MINUTES);

        // 构建 OkHttpClient
        okHttpClient = new OkHttpClient.Builder()
                .connectionPool(connectionPool)
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .followRedirects(true)
                .followSslRedirects(true)
                .build();
    }

    /**
     * 关闭 OkHttpClient
     */
    @PreDestroy
    public void destroy() {
        if (okHttpClient != null) {
            // 关闭连接池中的空闲连接
            okHttpClient.connectionPool().evictAll();

            // 关闭分发器
            okHttpClient.dispatcher().executorService().shutdown();
        }
    }

    private static Request buildDownloadRequest(S3Config s3Config, String url) {
        Request downloadRequest;
        if (url.contains("https://cdnfile.sspai.com")) {
            downloadRequest = new Request.Builder().url(url)
                    .addHeader("Referer", "https://sspai.com")
                    .addHeader("accept", "*/*")
                    .addHeader("Content-Type", "image/jpeg")
                    .addHeader("user-agent",
                            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36")
                    .build();
        } else if (StringUtils.isNotBlank(s3Config.getExtraParam("Referer"))) {
            downloadRequest = new Request.Builder().url(url)
                    .addHeader("Referer", s3Config.getExtraParam("Referer"))
                    .addHeader("accept", "*/*")
                    .addHeader("Content-Type", "image/jpeg")
                    .addHeader("user-agent",
                            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36")
                    .build();
        } else {
            downloadRequest = new Request.Builder().url(url)
                    .addHeader("accept", "*/*")
                    .addHeader("Content-Type", "image/jpeg")
                    .addHeader("user-agent",
                            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36")
                    .build();
        }
        return downloadRequest;
    }

    /**
     * 下载网络资源
     *
     * @param config     云存储配置
     * @param url        网络资源URL
     * @param objectName 对象名称（可选，为空时自动生成）
     * @return 上传结果
     */
    public File download(S3Config config, String url) {
        // 创建请求
        Request request = buildDownloadRequest(config, url);

        try (Response response = okHttpClient.newCall(request).execute()) {
            // 检查响应状态
            if (!response.isSuccessful()) {
                throw new ResourceException("Failed to download resource: HTTP error code " + response.code());
            }
            String fileName = UrlUtils.getFileNameFromUrl(url);

            // 创建临时文件
            Path tempFile = Files.createTempFile("temp_", fileName);

            // 将响应内容写入临时文件
            Files.copy(response.body().byteStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);

            // 上传到云存储
            return tempFile.toFile();

        } catch (IOException e) {
            throw new ResourceException("Failed to download resource: " + e.getMessage(), e);
        }
    }
}