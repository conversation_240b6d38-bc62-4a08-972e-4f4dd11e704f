package com.maixi.road.s3proxy.api.impl;

import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.api.AbstractS3Api;
import com.maixi.road.s3proxy.dto.ExistRes;
import com.maixi.road.s3proxy.exception.S3Exception;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.HeadObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.io.File;
import java.net.URI;

@Slf4j
public class CloudflareR2Client extends AbstractS3Api {

    private final S3Client s3Client;

    public CloudflareR2Client(S3Config config) {
        super(config);
        AwsBasicCredentials credentials = AwsBasicCredentials.create(
                config.getAccessKey(),
                config.getSecretKey());

        this.s3Client = S3Client.builder()
                .endpointOverride(URI.create(config.getEndpoint()))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.US_EAST_1)
                .build();
    }

    @Override
    public String getObjectUrl(String objectKey) {
        // 检查是否配置了自定义域名
        String customDomain = config.getExtraParam("customDomain");
        if (StringUtils.isNotEmpty(customDomain)) {
            if (customDomain.endsWith("/")) {
                customDomain = customDomain.substring(0, customDomain.length() - 1);
            }
            return customDomain + "/" + getFullObjectName(objectKey);
        }

        // 默认返回API访问URL（不可公开访问）
        String endpoint = config.getEndpoint();
        if (endpoint.endsWith("/")) {
            endpoint = endpoint.substring(0, endpoint.length() - 1);
        }
        return String.format("%s/%s/%s",
                endpoint,
                config.getBucket(),
                getFullObjectName(objectKey));
    }

    @Override
    public ExistRes doesObjectExist(String objectKey) {
        try {
            // 使用HeadObject操作检查对象是否存在
            s3Client.headObject(HeadObjectRequest.builder()
                    .bucket(config.getBucket())
                    .key(getFullObjectName(objectKey))
                    .build());

            // 如果没有抛出异常，则对象存在
            return ExistRes.builder().exist(true).url(getObjectUrl(objectKey)).build();
        } catch (Exception e) {
            return ExistRes.builder().exist(false).url(null).build();
        }
    }

    @Override
    public String upload(File file, String objectKey) {
        try {
            // 构建请求体
            RequestBody requestBody = RequestBody.fromFile(file);

            // 构建上传请求
            PutObjectRequest.Builder requestBuilder = PutObjectRequest.builder()
                    .bucket(config.getBucket())
                    .key(getFullObjectName(objectKey));

            // 设置内容类型（如果提供）
            // requestBuilder.contentType(FileUtils.getContentType(file.getName()));

            // 执行上传
            PutObjectRequest putObjectRequest = requestBuilder.build();
            log.info("r2 准备上传-------------");
            s3Client.putObject(putObjectRequest, requestBody);
            log.info("r2 上传成功-------------");

            // 返回URL
            return getObjectUrl(objectKey);
        } catch (Exception e) {
            log.error("Cloudflare R2 上传文件失败: ", e);
            throw new S3Exception(e.getMessage());
        }
    }
}
