package com.maixi.road.s3proxy.api.impl;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.api.AbstractS3Api;
import com.maixi.road.s3proxy.dto.ExistRes;
import com.maixi.road.s3proxy.exception.S3Exception;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

@Slf4j
public class AliyunOssClient extends AbstractS3Api {

    private final OSS ossClient;

    public AliyunOssClient(S3Config config) {
        super(config);
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(config.getAccessKey(), config.getSecretKey());
        // 使用credentialsProvider初始化客户端
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        // 显式声明使用 V4 签名算法
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4); 
        ossClient = OSSClientBuilder.create()
                .endpoint(config.getEndpoint())
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(config.getRegion())
                .build();
    }

    @Override
    public String upload(File file, String objectKey) {
        try {
            ossClient.putObject(config.getBucket(), getFullObjectName(objectKey), file);
            return getObjectUrl(objectKey);
        } catch (Exception e) {
            log.error("Aliyun OSS 上传文件失败: ", e);
            throw new S3Exception(e.getMessage());
        }
    }

    @Override
    public String getObjectUrl(String objectKey) {
        return String.format("https://%s.%s/%s", config.getBucket(), config.getEndpoint().replace("https://", ""), getFullObjectName(objectKey));
    }

    @Override
    public ExistRes doesObjectExist(String objectKey) {
        try {
            boolean exist = ossClient.doesObjectExist(config.getBucket(), getFullObjectName(objectKey));
            if (exist) {
                return ExistRes.builder().exist(true).url(getObjectUrl(objectKey)).build();
            }
            return ExistRes.builder().exist(false).url(null).build();
        } catch (Exception e) {
            return ExistRes.builder().exist(false).url(null).build();
        }
    }

}
