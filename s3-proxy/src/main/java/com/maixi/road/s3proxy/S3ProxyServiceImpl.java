package com.maixi.road.s3proxy;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.maixi.road.common.core.utils.UrlUtils;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.core.ResourceDownloader;
import com.maixi.road.s3proxy.core.ResourceUploader;
import com.maixi.road.s3proxy.core.S3ClientFactory;
import com.maixi.road.s3proxy.dto.ExistRes;
import com.maixi.road.s3proxy.dto.UploadResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDate;
import java.util.Date;

/**
 * S3代理服务实现类
 */
@Slf4j
@Service
public class S3ProxyServiceImpl implements S3ProxyService {

    @Autowired
    private S3ClientFactory clientManager;

    @Autowired
    private ResourceUploader resourceUploader;

    @Autowired
    private ResourceDownloader resourceDownloader;

    /**
     * 这个方法不需要判重
     * 如果没有传 objectName 则自动生成
     * 如果传了 objectName 判断是否添加日期路径，没有的话，补上日期路径
     */
    @Override
    public UploadResult uploadFile(S3Config config, File file, String objectName) {
        // 生成对象名称
        if (StringUtils.isBlank(objectName)) {
            objectName = generateObjectName(file.getName());
            // 添加日期路径
            objectName = withDatePath(objectName);
        } else if (!objectName.startsWith(String.valueOf(LocalDate.now().getYear()))) {
            // 标准化objectName,替换处理非法文件路径字符，例如空格,除扩展名之外的点号等等
            objectName = sanitizeObjectName(objectName);
            // 添加日期路径
            objectName = withDatePath(objectName);
        }

        log.info("objectName: {}", objectName);
        return resourceUploader.uploadFile(config, file, objectName);
    }

    @Override
    public UploadResult downloadAndUpload(S3Config config, String url) {
        // 从 URL 中提取文件名
        String objectName = UrlUtils.getFileNameFromUrl(url);
        // 添加日期路径
        objectName = withDatePath(objectName);
        log.info("objectName: {}", objectName);
        // 如果objectName 没有后缀，则自动补齐 .jpg 后缀
        if (!objectName.matches(".*\\..*")) {
            objectName = objectName + ".jpg";
        }

        // 检查对象是否存在
        ExistRes existRes = clientManager.getClient(config).doesObjectExist(objectName);
        if (existRes.isExist()) {
            log.info("文件已存在,返回文件URL");
            return UploadResult.success(objectName, existRes.getUrl(), 0, null);
        }

        // 下载网络资源
        File tempfile = resourceDownloader.download(config, url);
        log.info("fileName: {}", tempfile.getName());

        // 上传到云存储
        return uploadFile(config, tempfile, objectName);
    }

    /**
     * 生成对象名称
     * 
     * @param originalFilename 原始文件名
     * @return 生成的对象名称
     */
    private String generateObjectName(String originalFilename) {
        String nanoid = NanoIdUtils.randomNanoId();
        String extension = "";

        if (StringUtils.isNotEmpty(originalFilename)) {
            int lastDotIndex = originalFilename.lastIndexOf(".");
            if (lastDotIndex > 0) {
                extension = originalFilename.substring(lastDotIndex);
                log.info("extension: {}", extension);
            }
        }

        return nanoid + extension;
    }

    private String withDatePath(String fileName) {
        String datePath = DateFormatUtils.format(new Date(), "yyyy/MM/dd");
        return datePath + "/" + fileName;
    }

    /**
     * 标准化对象名称
     * 
     * @param objectName 原始对象名称
     * @return 标准化后的对象名称
     */
    private static String sanitizeObjectName(String objectName) {
        // 替换空格
        objectName = objectName.replaceAll(" ", "_");
        return objectName;
    }
}