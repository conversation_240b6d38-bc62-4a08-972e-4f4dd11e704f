package com.maixi.road.s3proxy.api.impl;

import com.cloudinary.Cloudinary;
import com.cloudinary.api.ApiResponse;
import com.cloudinary.utils.ObjectUtils;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.api.AbstractS3Api;
import com.maixi.road.s3proxy.dto.ExistRes;
import com.maixi.road.s3proxy.exception.S3Exception;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.Map;

@Slf4j
public class CloudinaryClient extends AbstractS3Api {

    private Cloudinary cloudinary;

    public CloudinaryClient(S3Config config) {
        super(config);
        cloudinary = new Cloudinary(ObjectUtils.asMap("cloud_name", config.getBucket(), "api_key", config.getAccessKey(), "api_secret", config.getSecretKey()));
    }

    @Override
    public String upload(File file, String objectKey) {
        try {
            // objectKey 如果有后缀，则去掉后缀
            objectKey = objectKey.substring(0, objectKey.lastIndexOf("."));
            @SuppressWarnings("rawtypes")
            Map upload = cloudinary.uploader().upload(file,
                    ObjectUtils.asMap("public_id", getFullObjectName(objectKey), "resource_type", "auto"));
            return upload.get("secure_url").toString();
        } catch (IOException e) {
            log.error("Cloudinary 上传文件失败: ", e);
            throw new S3Exception(e.getMessage());
        }
    }

    @Override
    public String getObjectUrl(String objectKey) {
        try {
            // objectKey 如果有后缀，则去掉后缀
            objectKey = objectKey.substring(0, objectKey.lastIndexOf("."));
            ApiResponse resource = cloudinary.api().resource(getFullObjectName(objectKey), ObjectUtils.emptyMap());
            String url = resource.get("secure_url").toString();
            return url;
        } catch (Exception e) {
            throw new S3Exception(e.getMessage());
        }
    }

    @Override
    public ExistRes doesObjectExist(String objectKey) {
        try {
            // objectKey 如果有后缀，则去掉后缀
            objectKey = objectKey.substring(0, objectKey.lastIndexOf("."));
            ApiResponse resource = cloudinary.api().resource(getFullObjectName(objectKey), ObjectUtils.emptyMap());
            return ExistRes.builder().exist(true).url(resource.get("secure_url").toString()).build();
        } catch (Exception e) {
            return ExistRes.builder().exist(false).url(null).build();
        }
    }

}
