package com.maixi.road.s3proxy.core;

import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.api.S3Api;
import com.maixi.road.s3proxy.api.impl.*;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * S3 客户端管理器
 * 负责创建和缓存 S3 客户端
 */
@Slf4j
@Component
public class S3ClientFactory {
    
    /**
     * 客户端缓存，键为配置的唯一标识
     */
    private final Map<String, S3Api> clientCache = new ConcurrentHashMap<>();
    
    /**
     * 获取 S3 客户端
     * 
     * @param config S3配置
     * @return S3客户端
     */
    public S3Api getClient(S3Config config) {
        String cacheKey = generateCacheKey(config);
        log.info("cacheKey: {}", cacheKey);
        return clientCache.computeIfAbsent(cacheKey, k -> createClient(config));
    }
    
    /**
     * 创建 S3 客户端
     * 
     * @param config S3配置
     * @return S3客户端
     */
    private S3Api createClient(S3Config config) {
        switch (config.getProvider().toLowerCase()) {
            case "s3":
                return new AwsS3Client(config);
            case "minio":
                return new MinIOClient(config);
            case "r2":
                return new CloudflareR2Client(config);
            case "cos":
                return new TencentCosClient(config);
            case "oss":
                return new AliyunOssClient(config);
            case "cloudinary":
                return new CloudinaryClient(config);
            default:
                throw new IllegalArgumentException("Unsupported S3 provider: " + config.getProvider());
        }
    }
    
    /**
     * 生成缓存键
     * 
     * @param config S3配置
     * @return 缓存键
     */
    private String generateCacheKey(S3Config config) {
        // 如果没有用户ID，使用配置信息生成唯一标识
        return DigestUtils.md5Hex(config.getProvider() + 
                                 config.getEndpoint() + 
                                 config.getAccessKey() + 
                                 config.getBucket()+
                                 config.getPrefix());
    }
    
    /**
     * 移除客户端缓存
     * 
     * @param config S3配置
     */
    public void removeClient(S3Config config) {
        String cacheKey = generateCacheKey(config);
        clientCache.remove(cacheKey);
    }
    
    /**
     * 清空所有客户端缓存
     */
    public void clearAllClients() {
        clientCache.clear();
    }
} 