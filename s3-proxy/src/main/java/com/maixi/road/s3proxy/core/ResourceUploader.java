package com.maixi.road.s3proxy.core;

import com.maixi.road.common.core.utils.FileUtils;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.api.S3Api;
import com.maixi.road.s3proxy.dto.UploadResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 资源上传器
 * 负责处理各种资源的上传
 */
@Slf4j
@Component
public class ResourceUploader {

    @Autowired
    private S3ClientFactory clientManager;

    /**
     * 上传文件
     *
     * @param config     云存储配置
     * @param file       本地文件
     * @param objectName 对象名称（可选，为空时自动生成）
     * @return 上传结果
     */
    public UploadResult uploadFile(S3Config config, File file, String objectName) {
        try {
            // 获取文件信息
            String fileName = file.getName();
            String contentType = FileUtils.getContentType(fileName);
            long fileSize = file.length();
            log.info("config.unionId={}, config.provider={}, contentType: {}", config.getUserId(), config.getProvider(), contentType);

            // 获取S3客户端并上传
            S3Api s3Client = clientManager.getClient(config);

            String url = s3Client.upload(file, objectName);

            // 构建上传结果
            return UploadResult.success(objectName, url, fileSize, contentType);

        } catch (Exception e) {
            log.error("{}:{} 上传文件失败: {}", config.getUserId(), config.getProvider(), e.getMessage(), e);
            return UploadResult.failure(e.getMessage());
        }
    }

}