package com.maixi.road.s3proxy.api;

import com.maixi.road.common.integration.s3.config.S3Config;

/**
 * S3 API抽象实现
 * 提供一些通用的实现方法
 */
public abstract class AbstractS3Api implements S3Api {
    
    protected final S3Config config;
    
    public AbstractS3Api(S3Config config) {
        this.config = config;
    }
    
    /**
     * 生成存储桶中的完整对象名称
     * 
     * @param objectName 对象名称
     * @return 完整对象名称
     */
    protected String getFullObjectName(String objectName) {
        
        // 否则添加前缀
        String prefix = config.getPrefix();
        if (prefix != null && !prefix.isEmpty()) {
            // 确保前缀以 / 结尾
            if (!prefix.endsWith("/")) {
                prefix = prefix + "/";
            }
            
            // 确保对象名称不以 / 开头
            if (objectName.startsWith("/")) {
                objectName = objectName.substring(1);
            }
            
            return prefix + objectName;
        }
        
        return objectName;
    }
    
    /**
     * 从异常中提取有用的错误信息
     * 
     * @param e 异常
     * @return 错误信息
     */
    protected String extractErrorMessage(Exception e) {
        if (e.getCause() != null && e.getCause().getMessage() != null) {
            return e.getCause().getMessage();
        }
        return e.getMessage();
    }
} 