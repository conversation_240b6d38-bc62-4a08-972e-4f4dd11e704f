package com.maixi.road.s3proxy.api.impl;

import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.api.AbstractS3Api;
import com.maixi.road.s3proxy.dto.ExistRes;
import com.maixi.road.s3proxy.exception.S3Exception;
import io.minio.MinioClient;
import io.minio.StatObjectArgs;
import io.minio.UploadObjectArgs;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

@Slf4j
public class MinIOClient extends AbstractS3Api {

    private final MinioClient minioClient;

    public MinIOClient(S3Config config) {
        super(config);
        minioClient = MinioClient.builder()
              .endpoint(config.getEndpoint())
              .credentials(config.getAccessKey(), config.getSecretKey())
              .build();
    }

    @Override
    public String upload(File file, String objectKey) {
        try {
            minioClient.uploadObject(
                    UploadObjectArgs.builder()
                            .bucket(config.getBucket())
                            .object(getFullObjectName(objectKey))
                            .filename(file.getAbsolutePath())
                            .build());
            return getObjectUrl(objectKey);
        } catch (Exception e) {
            log.error("MinIO 上传文件失败: ", e);
            throw new S3Exception(e.getMessage());
        }
    }

    @Override
    public String getObjectUrl(String objectKey) {
        return String.format("%s/%s/%s", config.getEndpoint(), config.getBucket(), getFullObjectName(objectKey));
    }

    @Override
    public ExistRes doesObjectExist(String objectKey) {
        try {
            minioClient.statObject(StatObjectArgs.builder()
                    .bucket(config.getBucket())
                    .object(getFullObjectName(objectKey))
                    .build());
            return ExistRes.builder().exist(true).url(getObjectUrl(objectKey)).build();
        } catch (Exception e) {
            return ExistRes.builder().exist(false).url(null).build();
        }
    }

}
