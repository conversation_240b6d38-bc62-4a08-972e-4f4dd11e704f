package com.maixi.road.s3proxy;

import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.dto.UploadResult;

import java.io.File;

/**
 * S3代理服务接口
 * 提供云存储操作的统一入口
 */
public interface S3ProxyService {

    /**
     * 上传本地文件
     * 
     * @param config S3配置
     * @param file 本地文件
     * @param objectName 对象名称(带后缀的文件名)（可选，为空时自动生成）
     * @return 上传结果
     */
    UploadResult uploadFile(S3Config config, File file, String objectName);
    
    /**
     * 下载网络资源并上传
     * 
     * @param config S3配置
     * @param url 网络资源URL
     * @return 上传结果
     */
    UploadResult downloadAndUpload(S3Config config, String url);
} 