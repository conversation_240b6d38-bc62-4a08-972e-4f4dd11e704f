package com.maixi.road.s3proxy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.File;
import java.io.InputStream;

/**
 * 上传请求对象
 * 包含上传所需的所有信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadRequest {
    /**
     * 上传类型：FILE, STREAM, URL
     */
    private UploadType type;
    
    /**
     * 本地文件（当type=FILE时使用）
     */
    private File file;
    
    /**
     * 输入流（当type=STREAM时使用）
     */
    private InputStream inputStream;
    
    /**
     * 网络资源URL（当type=URL时使用）
     */
    private String url;
    
    /**
     * 对象名称（可选，为空时自动生成）
     */
    private String objectName;
    
    /**
     * 内容类型（可选，为空时自动检测）
     */
    private String contentType;
    
    /**
     * 内容大小（可选）
     */
    private Long size;
    
    /**
     * 上传类型枚举
     */
    public enum UploadType {
        /**
         * 本地文件
         */
        FILE,
        
        /**
         * 输入流
         */
        STREAM,
        
        /**
         * 网络资源URL
         */
        URL
    }
    
    /**
     * 创建文件上传请求
     * 
     * @param file 本地文件
     * @param objectName 对象名称（可选）
     * @return 上传请求
     */
    public static UploadRequest ofFile(File file, String objectName) {
        return UploadRequest.builder()
                .type(UploadType.FILE)
                .file(file)
                .objectName(objectName)
                .build();
    }
    
    /**
     * 创建输入流上传请求
     * 
     * @param inputStream 输入流
     * @param objectName 对象名称
     * @param contentType 内容类型（可选）
     * @param size 内容大小（可选）
     * @return 上传请求
     */
    public static UploadRequest ofStream(InputStream inputStream, String objectName, 
                                         String contentType, Long size) {
        return UploadRequest.builder()
                .type(UploadType.STREAM)
                .inputStream(inputStream)
                .objectName(objectName)
                .contentType(contentType)
                .size(size)
                .build();
    }
    
    /**
     * 创建URL上传请求
     * 
     * @param url 网络资源URL
     * @param objectName 对象名称（可选）
     * @return 上传请求
     */
    public static UploadRequest ofUrl(String url, String objectName) {
        return UploadRequest.builder()
                .type(UploadType.URL)
                .url(url)
                .objectName(objectName)
                .build();
    }
} 