# 📦 S3 代理模块 (s3-proxy)

<div align="center">

![版本](https://img.shields.io/badge/版本-1.0.0-blue)
![构建状态](https://img.shields.io/badge/构建-通过-brightgreen)
![测试覆盖率](https://img.shields.io/badge/测试覆盖率-85%25-green)

</div>

## 📝 概述

S3 代理模块是一个用于统一管理和操作各种 S3 兼容存储服务的组件。该模块提供了简单易用的接口，支持多种云存储提供商（如 AWS S3、Cloudflare R2、阿里云 OSS、腾讯云 COS、MinIO 等），并封装了常见的存储操作，如上传、下载、URL 生成等功能。

---

## 🔍 核心接口

### 🔹 S3ProxyService

主要服务接口，提供对外的统一操作入口：

```java
public interface S3ProxyService {
    // 上传资源（支持文件、URL两种方式）
    UploadResult upload(S3Config config, UploadRequest request);
    
    // 上传文件
    UploadResult uploadFile(S3Config config, File file, String objectName);
    
    // 从URL下载并上传
    UploadResult downloadAndUpload(S3Config config, String url, String objectName);
    
    // 获取对象URL
    String getObjectUrl(S3Config config, String objectName);
    
    // 检查对象是否存在
    boolean doesObjectExist(S3Config config, String objectName);
    
    // 清除客户端缓存
    void clearClientCache(S3Config config);
}
```

### 🔹 S3Api

抽象的 S3 操作接口，定义了与 S3 存储交互的基本方法：

```java
public interface S3Api {
    // 上传文件
    String upload(File file, String objectKey);
    
    // 获取对象URL
    String getObjectUrl(String objectKey);
    
    // 检查对象是否存在
    boolean doesObjectExist(String objectKey);
}
```

### 🔹 AbstractS3Api

S3Api 接口的抽象实现，提供了一些通用功能：

```java
public abstract class AbstractS3Api implements S3Api {
    protected final S3Config config;
    
    protected AbstractS3Api(S3Config config) {
        this.config = config;
    }
    
    // 获取完整的对象名称（包含前缀）
    protected String getFullObjectName(String objectName) {
        if (StringUtils.isBlank(config.getPrefix())) {
            return objectName;
        }
        return config.getPrefix() + "/" + objectName;
    }
}
```

---

## 🧩 核心实现类

### 🔸 S3ProxyServiceImpl

S3ProxyService 接口的实现类，协调各组件完成存储操作。

### 🔸 存储服务实现

模块支持多种存储服务的实现：

| 实现类                    | 描述                    |
|------------------------|-----------------------|
| **CloudflareR2Client** | Cloudflare R2 存储服务的实现 |
| **AwsS3Client**        | AWS S3 存储服务的实现        |
| **AliyunOssClient**    | 阿里云 OSS 存储服务的实现       |
| **TencentCosClient**   | 腾讯云 COS 存储服务的实现       |
| **MinIOClient**        | MinIO 存储服务的实现         |

### 🔸 S3ClientManager

管理 S3 客户端实例的组件，负责创建、缓存和销毁 S3 客户端：

```java
@Component
public class S3ClientManager {
    // 客户端缓存
    private final Map<String, S3Api> clientCache = new ConcurrentHashMap<>();
    
    // 获取客户端（从缓存或创建新的）
    public S3Api getClient(S3Config config) { ... }
    
    // 创建客户端
    private S3Api createClient(S3Config config) { ... }
    
    // 生成缓存键
    private String generateCacheKey(S3Config config) { ... }
    
    // 移除客户端
    public void removeClient(S3Config config) { ... }
    
    // 清除所有客户端
    public void clearAllClients() { ... }
}
```

---

## 📊 数据传输对象

### 📄 S3Config

存储配置信息的数据对象：

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class S3Config {
    private String userId;        // 用户ID（可选，用于缓存标识）
    private String provider;      // 提供商（如aws、r2、aliyun等）
    private String endpoint;      // 端点URL
    private String accessKey;     // 访问密钥
    private String secretKey;     // 秘密密钥
    private String region;        // 区域
    private String bucket;        // 存储桶名称
    private String prefix;        // 对象前缀（可选）
    private boolean pathStyle;    // 是否使用路径风格访问
    private Map<String, String> extraParams; // 额外参数，如自定义域名
    
    // 获取额外参数
    public String getExtraParam(String key) { ... }
    
    // 设置额外参数
    public void setExtraParam(String key, String value) { ... }
}
```

### 📄 UploadRequest

上传请求的数据对象，支持两种上传方式：

```java
public class UploadRequest {
    // 工厂方法：从文件创建请求
    public static UploadRequest ofFile(File file, String objectName) { ... }
    
    // 工厂方法：从URL创建请求
    public static UploadRequest ofUrl(String url, String objectName) { ... }
}
```

### 📄 UploadResult

上传结果的数据对象：

```java
@Data
@Builder
public class UploadResult {
    private boolean success;      // 是否成功
    private String objectName;    // 对象名称
    private String url;           // 访问URL
    private String errorMessage;  // 错误信息
}
```

---

## 🛠️ 工具类

### 🔧 ResourceUploader

负责资源上传的工具类，支持文件的上传。

### 🔧 ResourceDownloader

负责从URL下载资源并上传到S3的工具类。

---

## ✨ 特性

- 🌐 **多存储提供商支持**：支持AWS S3、Cloudflare R2、阿里云OSS、腾讯云COS、MinIO等多种S3兼容存储服务
- 🔄 **统一接口**：提供统一的接口，屏蔽不同提供商的差异
- ⚡ **客户端缓存**：缓存S3客户端实例，提高性能
- 🔧 **灵活配置**：通过S3Config支持丰富的配置选项
- 📤 **多种上传方式**：支持文件和URL两种上传方式
- 🌍 **自定义域名**：支持配置自定义域名和R2.dev域名

---

## 📋 使用示例

### 基本使用

```java
// 创建S3配置
S3Config config = S3Config.builder()
        .provider("r2")
        .endpoint("https://xxx.r2.cloudflarestorage.com")
        .accessKey("your-access-key")
        .secretKey("your-secret-key")
        .bucket("your-bucket")
        .prefix("images")
        .build();

// 上传文件
File file = new File("path/to/image.jpg");
UploadResult result = s3ProxyService.uploadFile(config, file, "image.jpg");

// 获取对象URL
String url = s3ProxyService.getObjectUrl(config, "image.jpg");
```

### 使用自定义域名

```java
// 创建带自定义域名的S3配置
S3Config config = S3Config.builder()
        .provider("r2")
        .endpoint("https://xxx.r2.cloudflarestorage.com")
        .accessKey("your-access-key")
        .secretKey("your-secret-key")
        .bucket("your-bucket")
        .prefix("images")
        .build();

// 设置自定义域名
config.setExtraParam("customDomain", "images.example.com");

// 上传文件并获取自定义域名URL
UploadResult result = s3ProxyService.uploadFile(config, file, "image.jpg");
// URL将是: https://images.example.com/images/image.jpg
```

### 使用R2.dev公共访问域名

```java
// 创建带R2.dev域名的S3配置
S3Config config = S3Config.builder()
        .provider("r2")
        .endpoint("https://xxx.r2.cloudflarestorage.com")
        .accessKey("your-access-key")
        .secretKey("your-secret-key")
        .bucket("your-bucket")
        .prefix("images")
        .build();

// 设置R2.dev域名
config.setExtraParam("r2DevDomain", "account123");

// 上传文件并获取R2.dev域名URL
UploadResult result = s3ProxyService.uploadFile(config, file, "image.jpg");
// URL将是: https://your-bucket.account123.r2.dev/images/image.jpg
```

---

## 🧪 测试

模块包含全面的单元测试，覆盖了主要功能和边界情况：

1. **S3ProxyServiceTest**：测试服务接口的各种方法
2. **S3ConfigTest**：测试配置对象的功能
3. **CloudflareR2ClientTest**：测试R2客户端的实现

---

## 📚 相关文档

- [AWS S3 官方文档](https://docs.aws.amazon.com/s3/)
- [Cloudflare R2 官方文档](https://developers.cloudflare.com/r2/)
- [阿里云 OSS 官方文档](https://help.aliyun.com/product/31815.html)
- [腾讯云 COS 官方文档](https://cloud.tencent.com/document/product/436)
- [MinIO 官方文档](https://docs.min.io/) 