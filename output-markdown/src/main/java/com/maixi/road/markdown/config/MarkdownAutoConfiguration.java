package com.maixi.road.markdown.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import com.maixi.road.markdown.converter.Block2MarkdownConverter;
import com.maixi.road.markdown.converter.Block2MarkdownConverterImpl;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.markdown.service.MarkdownOutputServiceImpl;

/**
 * Markdown 自动配置类
 * <p>
 * 用于自动配置 Markdown 输出相关的组件
 * </p>
 */
@Configuration
@ComponentScan(basePackages = "com.maixi.road.markdown")
public class MarkdownAutoConfiguration {
    
    /**
     * 配置 Block2MarkdownConverter
     */
    @Bean
    @ConditionalOnMissingBean
    public Block2MarkdownConverter block2MarkdownConverter() {
        return new Block2MarkdownConverterImpl();
    }
    
    /**
     * 配置 MarkdownOutputService
     */
    @Bean
    @ConditionalOnMissingBean
    public MarkdownOutputService markdownOutputService() {
        return new MarkdownOutputServiceImpl();
    }
}