package com.maixi.road.markdown.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * Markdown 配置类
 * <p>
 * 用于配置 Markdown 输出相关的属性
 * </p>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "road.markdown")
public class MarkdownConfig {
    
    /**
     * 默认保存路径
     */
    private String defaultSavePath = System.getProperty("user.home") + "/Downloads";
    
    /**
     * 是否默认创建资源目录
     */
    private boolean createAssetsDir = true;
    
    /**
     * 是否默认下载远程图片
     */
    private boolean downloadImages = false;
    
    /**
     * 是否默认包含元数据
     */
    private boolean includeMetadata = true;
}