package com.maixi.road.markdown.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.markdown.dto.MarkdownSaveRQ;
import com.maixi.road.markdown.dto.MarkdownSaveRS;
import com.maixi.road.markdown.service.MarkdownOutputService;

import lombok.extern.slf4j.Slf4j;

/**
 * Markdown 导出控制器
 * <p>
 * 提供 Markdown 导出相关的 API 接口
 * </p>
 */
@Slf4j
@RestController
@RequestMapping("/api/markdown")
public class MarkdownExportController {
    
    @Autowired
    private MarkdownOutputService markdownOutputService;
    
    /**
     * 保存文章为 Markdown 文件
     * 
     * @param request 保存请求
     * @return 保存结果
     */
    @PostMapping("/save")
    public Result<MarkdownSaveRS> saveArticle(@RequestBody MarkdownSaveRQ request) {
        log.info("收到 Markdown 导出请求, unionId={}, title={}", 
                request.getUnionId(), request.getUserForm() != null ? request.getUserForm().getTitle() : "");
        return markdownOutputService.saveArticle(request);
    }
}