package com.maixi.road.markdown.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Markdown 保存响应对象
 * <p>
 * 包含保存结果的相关信息
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarkdownSaveRS {
    
    /**
     * 文件保存路径
     */
    private String filePath;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 资源目录路径（如果创建了资源目录）
     */
    private String assetsPath;
    
    /**
     * 保存是否成功
     */
    private boolean success;
    
    /**
     * 错误信息（如果保存失败）
     */
    private String errorMessage;
    
    /**
     * S3 存储 URL
     */
    private String s3Url;
    
    /**
     * 是否已保存到 S3
     */
    private boolean savedToS3;
}