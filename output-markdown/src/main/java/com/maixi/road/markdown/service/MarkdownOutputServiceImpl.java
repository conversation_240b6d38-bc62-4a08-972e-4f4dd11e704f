package com.maixi.road.markdown.service;

import java.io.IOException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.utils.NameUtils;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.converter.Block2MarkdownConverter;
import com.maixi.road.markdown.dto.MarkdownSaveRQ;
import com.maixi.road.markdown.dto.MarkdownSaveRS;
import com.maixi.road.markdown.util.MarkdownFileWriter;
import com.maixi.road.markdown.util.MarkdownS3Writer;
import com.maixi.road.s3proxy.S3ProxyService;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Markdown 输出服务实现类
 * <p>
 * 负责将文章内容转换为 Markdown 格式并保存到本地文件或 S3
 * </p>
 */
@Slf4j
@Service
public class MarkdownOutputServiceImpl implements MarkdownOutputService {

    @Autowired
    private Block2MarkdownConverter markdownConverter;

    @Autowired(required = false)
    private S3ProxyService s3ProxyService;

    @Resource
    private ConfigQueryApi configQueryApi;

    /**
     * 将文章保存为 Markdown 文件
     * 
     * @param request 包含文章内容的请求对象
     * @return 保存结果，包含文件路径等信息
     */
    @Override
    public Result<MarkdownSaveRS> saveArticle(MarkdownSaveRQ request) {
        if (request == null || request.getUserForm() == null) {
            return Result.fail("请求参数不能为空");
        }

        ResolveFormRQ article = request.getUserForm();
        if (CollectionUtils.isEmpty(article.getBlocks())) {
            return Result.fail("文章内容不能为空");
        }

        try {
            // 转换为 Markdown
            String markdownContent = markdownConverter.convertToMarkdown(article.getBlocks());

            // 处理文件名
            String fileName = StringUtils.isNotBlank(request.getFileName()) ? request.getFileName()
                    : sanitizeFileName(article.getTitle());

            // 处理保存路径
            String savePath = StringUtils.isNotBlank(request.getSavePath()) ? request.getSavePath()
                    : System.getProperty("user.home") + "/Downloads";

            // 添加元数据
            if (request.isIncludeMetadata()) {
                String metadata = MarkdownFileWriter.generateMetadata(request.getObConfig(),request.isArticle(),article);
                markdownContent = metadata + markdownContent;
            }

            // 构建响应对象构建器
            MarkdownSaveRS.MarkdownSaveRSBuilder responseBuilder = MarkdownSaveRS.builder()
                    .fileName(fileName + ".md")
                    .success(true);

            // 判断是否需要保存到 S3
            if (request.isSaveToS3()) {

                try {
                    // 构建 S3 配置
                    S3Config s3Config = configQueryApi.queryS3Config(request.getUnionId());
                    if (s3Config == null) {
                        log.warn("S3 配置不存在，无法上传到 S3");
                        return Result.fail("S3 配置不存在，无法上传到 S3");
                    }

                    // 上传到 S3
                    String s3Url = MarkdownS3Writer.uploadToS3(
                            markdownContent,
                            fileName + ".md",
                            s3Config,
                            request.getObConfig(),
                            s3ProxyService);

                    if (s3Url != null) {
                        responseBuilder.s3Url(s3Url).savedToS3(true);
                        log.info("文章已上传到 S3, title={}, s3Url={}", article.getTitle(), s3Url);
                        return Result.success(responseBuilder.build());
                    } else {
                        log.error("上传文章到 S3 失败, title={}", article.getTitle());
                        return Result.fail("上传到 S3 失败，可能是用户未配置 S3 或配置有误");
                    }
                } catch (Exception e) {
                    log.error("上传 S3 失败, title={}", article.getTitle(), e);
                    return Result.fail("上传到 S3 失败: " + e.getMessage());
                }
            } else {
                // 写入文件 - 强制设置为false不创建资源目录
                String filePath = MarkdownFileWriter.writeToFile(
                        markdownContent,
                        savePath,
                        fileName + ".md",
                        false);

                // 构建响应
                responseBuilder.filePath(filePath);

                log.info("文章已保存为本地 Markdown 文件, title={}, filePath={}", article.getTitle(), filePath);
                return Result.success(responseBuilder.build());
            }
        } catch (IOException e) {
            log.error("保存 Markdown 文件失败, title={}", article.getTitle(), e);
            return Result.fail("保存文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("转换 Markdown 失败, title={}", article.getTitle(), e);
            return Result.fail("转换失败: " + e.getMessage());
        }
    }

    /**
     * 处理文件名，使用统一的NameUtils工具类
     * 
     * @param fileName 原始文件名
     * @return 处理后的文件名
     */
    private String sanitizeFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return NameUtils.generateFileNameWithCurrentTime("article");
        }

        // 使用工具类清理文件名
        return NameUtils.sanitizeFileName(fileName);
    }
}