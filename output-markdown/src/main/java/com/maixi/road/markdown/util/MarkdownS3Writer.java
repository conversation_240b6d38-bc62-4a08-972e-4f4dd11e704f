package com.maixi.road.markdown.util;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.StringUtils;

import com.maixi.road.common.core.model.dto.ObsidianDTO;
import com.maixi.road.common.core.utils.NameUtils;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.S3ProxyService;
import com.maixi.road.s3proxy.dto.UploadResult;

import lombok.extern.slf4j.Slf4j;

/**
 * Markdown S3 文件上传工具
 * <p>
 * 用于将 Markdown 内容上传到 S3 存储
 * </p>
 */
@Slf4j
public class MarkdownS3Writer {

    /**
     * 将 Markdown 内容上传到 S3
     * 
     * @param content        Markdown 内容
     * @param fileName       文件名
     * @param s3Config       S3 配置
     * @param s3ProxyService S3 代理服务
     * @return S3 文件 URL，如果上传失败则返回 null
     * @throws IOException 如果创建临时文件失败
     */
    public static String uploadToS3(String content, String fileName, S3Config s3Config, ObsidianDTO obConfig,
            S3ProxyService s3ProxyService) throws IOException {
        if (StringUtils.isBlank(content)) {
            throw new IllegalArgumentException("内容不能为空");
        }

        // 处理文件名
        if (StringUtils.isBlank(fileName)) {
            fileName = NameUtils.generateFileNameWithCurrentTime("mpclipper");
        }

        // 确保文件名有 .md 扩展名
        if (!fileName.toLowerCase().endsWith(".md")) {
            fileName = fileName + ".md";
        }

        // 创建临时文件
        Path tempFile = Files.createTempFile("markdown_", ".md");
        Files.write(tempFile, content.getBytes(StandardCharsets.UTF_8));

        String objectName = fileName;

        try {
            // 上传到 S3
            File file = tempFile.toFile();
            s3Config.setPrefix(obConfig.getSaveRoot());
            UploadResult result = s3ProxyService.uploadFile(s3Config, file, objectName);
            if (result != null && result.isSuccess()) {
                log.info("Markdown 文件已上传到 S3: {}", result.getUrl());
                return result.getUrl();
            } else {
                log.error("上传 Markdown 文件到 S3 失败");
                return null;
            }
        } catch (Exception e) {
            log.error("上传 Markdown 文件到 S3 异常", e);
            return null;
        } finally {
            // 删除临时文件
            try {
                Files.deleteIfExists(tempFile);
            } catch (Exception e) {
                log.warn("删除临时文件失败: {}", tempFile, e);
            }
        }
    }
}
