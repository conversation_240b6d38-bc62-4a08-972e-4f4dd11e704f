package com.maixi.road.markdown.service;

import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.markdown.dto.MarkdownSaveRQ;
import com.maixi.road.markdown.dto.MarkdownSaveRS;

/**
 * Markdown 输出服务接口
 * <p>
 * 负责将文章内容转换为 Markdown 格式并保存到本地文件
 * </p>
 */
public interface MarkdownOutputService {
    
    /**
     * 将文章保存为 Markdown 文件
     * 
     * @param request 包含文章内容的请求对象
     * @return 保存结果，包含文件路径等信息
     */
    Result<MarkdownSaveRS> saveArticle(MarkdownSaveRQ request);
}