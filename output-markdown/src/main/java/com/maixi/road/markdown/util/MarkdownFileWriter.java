package com.maixi.road.markdown.util;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import com.maixi.road.common.core.model.dto.ObsidianDTO;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.utils.NameUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * Markdown 文件写入器
 * <p>
 * 用于将 Markdown 内容写入文件
 * </p>
 */
@Slf4j
public class MarkdownFileWriter {

    /**
     * 将 Markdown 内容写入文件
     * 
     * @param content         Markdown 内容
     * @param savePath        保存路径
     * @param fileName        文件名
     * @param createAssetsDir 是否创建资源目录
     * @return 保存的文件路径
     * @throws IOException 如果写入文件失败
     */
    public static String writeToFile(String content, String savePath, String fileName, boolean createAssetsDir)
            throws IOException {
        if (StringUtils.isBlank(content)) {
            throw new IllegalArgumentException("内容不能为空");
        }

        // 处理保存路径
        if (StringUtils.isBlank(savePath)) {
            savePath = System.getProperty("user.home") + "/Downloads";
        }

        // 处理文件名
        if (StringUtils.isBlank(fileName)) {
            fileName = NameUtils.generateFileNameWithCurrentTime("article");
        }

        // 确保文件名有 .md 扩展名
        if (!fileName.toLowerCase().endsWith(".md")) {
            fileName = fileName + ".md";
        }

        // 创建目录
        Path dirPath = Paths.get(savePath);
        if (!Files.exists(dirPath)) {
            Files.createDirectories(dirPath);
        }

        // 创建资源目录
        String assetsDir = null;
        if (createAssetsDir) {
            String baseName = FilenameUtils.getBaseName(fileName);
            assetsDir = savePath + "/" + baseName + "_assets";
            Path assetsDirPath = Paths.get(assetsDir);
            if (!Files.exists(assetsDirPath)) {
                Files.createDirectories(assetsDirPath);
            }
        }

        // 写入文件
        Path filePath = dirPath.resolve(fileName);
        Files.write(filePath, content.getBytes(StandardCharsets.UTF_8),
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

        log.info("Markdown 文件已保存: {}", filePath);
        return filePath.toString();
    }

    /**
     * 生成 Markdown 元数据
     * 
     * @param obConfig    Obsidian 配置
     * @param title       标题
     * @param author      作者
     * @param date        日期
     * @param link        链接
     * @param origin      来源
     * @param tags        标签
     * @param description 描述
     * @return Markdown 元数据
     */
    public static String generateMetadata(ObsidianDTO obConfig, boolean isArticle, ResolveFormRQ article) {
        if (isArticle) {
            return generateArticleMetadata(obConfig,
                    article.getTitle(),
                    article.getAuthor(),
                    article.getPublishTime() != null ? article.getPublishTime().toString()
                            : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    article.getLink(),
                    article.getOrigin(),
                    article.getTags(),
                    article.getRemark());
        } else {
            return generateMessageMetadata(obConfig, article);
        }
    }

    private static String generateMessageMetadata(ObsidianDTO obConfig, ResolveFormRQ message) {
        StringBuilder metadata = new StringBuilder();
        metadata.append("---\n");

        if (StringUtils.isNotBlank(message.getTitle()) && StringUtils.isNotBlank(obConfig.getMsgInfo().getTitle())) {
            metadata.append(obConfig.getMsgInfo().getTitle()).append(": ").append(message.getTitle()).append("\n");
        }

        if (StringUtils.isNotBlank(obConfig.getMsgInfo().getCategory())) {
            metadata.append(obConfig.getMsgInfo().getCategory()).append(": ").append(message.getCategory()).append("\n");
        }

        if (message.getTags() != null && !message.getTags().isEmpty()
                && StringUtils.isNotBlank(obConfig.getMsgInfo().getTags())) {
            metadata.append(obConfig.getMsgInfo().getTags()).append(": \n");
            for (String tag : message.getTags()) {
                metadata.append("  - ").append(tag).append("\n");
            }
        }

        if (StringUtils.isNotBlank(obConfig.getMsgInfo().getCreateTime())) {
            metadata.append(obConfig.getMsgInfo().getCreateTime())
                    .append(": ")
                    .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .append("\n");
        }

        metadata.append("---\n\n");
        return metadata.toString();
    }

    /**
     * 生成 Markdown 元数据
     * 
     * @param obConfig    Obsidian 配置
     * @param title       标题
     * @param author      作者
     * @param date        日期
     * @param link        链接
     * @param origin      来源
     * @param tags        标签
     * @param description 描述
     * @return Markdown 元数据
     */
    public static String generateArticleMetadata(ObsidianDTO obConfig,
            String title,
            String author,
            String publicTime,
            String link,
            String origin,
            List<String> tags,
            String remark) {
        StringBuilder metadata = new StringBuilder();
        metadata.append("---\n");

        if (StringUtils.isNotBlank(title) && StringUtils.isNotBlank(obConfig.getArtInfo().getTitle())) {
            metadata.append(obConfig.getArtInfo().getTitle()).append(": ").append(title).append("\n");
        }

        if (StringUtils.isNotBlank(obConfig.getArtInfo().getCategory())) {
            metadata.append(obConfig.getArtInfo().getCategory()).append(": article").append("\n");
        }

        if (StringUtils.isNotBlank(link) && StringUtils.isNotBlank(obConfig.getArtInfo().getUrl())) {
            metadata.append(obConfig.getArtInfo().getUrl()).append(": ").append(link).append("\n");
        }

        if (StringUtils.isNotBlank(origin) && StringUtils.isNotBlank(obConfig.getArtInfo().getOrigin())) {
            metadata.append(obConfig.getArtInfo().getOrigin()).append(": ").append(origin).append("\n");
        }

        if (StringUtils.isNotBlank(author) && StringUtils.isNotBlank(obConfig.getArtInfo().getAuthor())) {
            metadata.append(obConfig.getArtInfo().getAuthor()).append(": ").append(author).append("\n");
        }

        if (tags != null && !tags.isEmpty() && StringUtils.isNotBlank(obConfig.getArtInfo().getTags())) {
            metadata.append(obConfig.getArtInfo().getTags()).append(": \n");
            for (String tag : tags) {
                metadata.append("  - ").append(tag).append("\n");
            }
        }

        if (StringUtils.isNotBlank(remark) && StringUtils.isNotBlank(obConfig.getArtInfo().getRemark())) {
            metadata.append(obConfig.getArtInfo().getRemark()).append(": ").append(remark).append("\n");
        }

        if (StringUtils.isNotBlank(publicTime) && StringUtils.isNotBlank(obConfig.getArtInfo().getPublishTime())) {
            metadata.append(obConfig.getArtInfo().getPublishTime()).append(": ").append(publicTime).append("\n");
        }

        if (StringUtils.isNotBlank(obConfig.getArtInfo().getCreateTime())) {
            metadata.append(obConfig.getArtInfo().getCreateTime())
                    .append(": ")
                    .append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .append("\n");
        }

        metadata.append("---\n\n");
        return metadata.toString();
    }
}