package com.maixi.road.markdown.converter;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.common.integration.notion.model.block.Audio;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.File;
import com.maixi.road.common.integration.notion.model.block.Heading;
import com.maixi.road.common.integration.notion.model.block.Image;
import com.maixi.road.common.integration.notion.model.block.ListItem;
import com.maixi.road.common.integration.notion.model.block.Pdf;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.block.Video;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.markdown.util.ImagePathHelper;

import lombok.extern.slf4j.Slf4j;

/**
 * Block 到 Markdown 的转换器实现类
 * <p>
 * 负责将 Block 对象转换为 Markdown 格式
 * </p>
 */
@Slf4j
@Component
public class Block2MarkdownConverterImpl implements Block2MarkdownConverter {

    /**
     * 将 Block 对象列表转换为 Markdown 格式
     * 
     * @param blocks Block 对象列表
     * @return Markdown 格式的内容
     */
    @Override
    public String convertToMarkdown(List<Block> blocks) {
        if (CollectionUtils.isEmpty(blocks)) {
            return "";
        }

        StringBuilder markdown = new StringBuilder();
        String prevBlockType = null;
        int numberedListCounter = 1;

        for (int i = 0; i < blocks.size(); i++) {
            Block block = blocks.get(i);
            String blockType = block.getType();
            String blockContent;

            // 处理列表项的特殊情况 - 使用新的递归转换方法
            if ("bulleted_list_item".equals(blockType)) {
                blockContent = convertListItemRecursively(block.getBulleted_list_item(), "- ", 0);
                log.debug("转换无序列表项: {}", blockContent.substring(0, Math.min(50, blockContent.length())));
            } else if ("numbered_list_item".equals(blockType)) {
                // 如果前一个块不是有序列表项，重置计数器
                if (!"numbered_list_item".equals(prevBlockType)) {
                    numberedListCounter = 1;
                }
                blockContent = convertListItemRecursively(block.getNumbered_list_item(), numberedListCounter + ". ", 0);
                numberedListCounter++;
                log.debug("转换有序列表项: {}", blockContent.substring(0, Math.min(50, blockContent.length())));
            } else {
                blockContent = convertBlock(block);
            }

            if (StringUtils.isNotBlank(blockContent)) {
                // 确定是否需要添加额外的换行符
                boolean isCurrentListItem = "bulleted_list_item".equals(blockType)
                        || "numbered_list_item".equals(blockType);
                boolean isPrevListItem = "bulleted_list_item".equals(prevBlockType)
                        || "numbered_list_item".equals(prevBlockType);
                boolean isTableBlock = "table".equals(blockType);

                // 如果当前块和前一个块都是列表项，只添加一个换行符
                if (isCurrentListItem && isPrevListItem) {
                    markdown.append(blockContent).append("\n");
                } else {
                    // 如果不是连续的列表项，添加适当的换行符
                    if (i > 0) {
                        // 对于表格，只添加一个换行符，因为表格本身已经有换行符
                        markdown.append(isTableBlock ? "" : "\n");
                    }
                    markdown.append(blockContent);
                    // 如果不是表格，添加换行符，因为表格本身已经有换行符
                    if (!isTableBlock) {
                        markdown.append("\n");
                    }
                }
            }

            prevBlockType = blockType;
        }

        return markdown.toString();
    }

    /**
     * 将单个 Block 对象转换为 Markdown 格式
     * 
     * @param block Block 对象
     * @return Markdown 格式的内容
     */
    @Override
    public String convertBlock(Block block) {
        if (block == null || StringUtils.isBlank(block.getType())) {
            return "\n";
        }

        switch (block.getType()) {
            case "paragraph":
                return convertParagraph(block.getParagraph());
            case "heading_1":
                return "# " + convertRichTexts(block.getHeading_1());
            case "heading_2":
                return "## " + convertRichTexts(block.getHeading_2());
            case "heading_3":
                return "### " + convertRichTexts(block.getHeading_3());
            case "bulleted_list_item":
                // 使用新的递归转换方法
                return convertListItemRecursively(block.getBulleted_list_item(), "- ", 0);
            case "numbered_list_item":
                // 使用新的递归转换方法
                return convertListItemRecursively(block.getNumbered_list_item(), "1. ", 0);
            case "quote":
                return "> " + convertRichTexts(block.getQuote());
            case "code":
                return convertCode(block);
            case "image":
                return convertImage(block.getImage());
            case "file":
                return convertFile(block.getFile());
            case "pdf":
                return convertPdf(block.getPdf());
            case "audio":
                return convertAudio(block.getAudio());
            case "video":
                return convertVideo(block.getVideo());
            case "divider":
                return "---";
            case "callout":
                return convertCallout(block);
            case "table":
                return convertTable(block);
            case "bookmark":
                return convertBookmark(block);
            case "embed":
                return convertEmbed(block);
            case "link_to_page":
                return convertLinkToPage(block);
            default:
                log.warn("未知的 Block 类型: {}", block.getType());
                return "\n";
        }
    }

    private String convertVideo(Video video) {
        if (video == null || StringUtils.isBlank(video.getExternal().getUrl())) {
            return "";
        }
        return "[" + video.getExternal().getUrl() + "](" + video.getExternal().getUrl() + ")";
    }

    private String convertAudio(Audio audio) {
        if (audio == null || StringUtils.isBlank(audio.getExternal().getUrl())) {
            return "";
        }
        return "[" + audio.getExternal().getUrl() + "](" + audio.getExternal().getUrl() + ")";
    }

    private String convertPdf(Pdf pdf) {
        if (pdf == null || StringUtils.isBlank(pdf.getExternal().getUrl())) {
            return "";
        }
        return "[" + pdf.getExternal().getUrl() + "](" + pdf.getExternal().getUrl() + ")";
    }

    private String convertFile(File file) {
        if (file == null || StringUtils.isBlank(file.getName())) {
            return "";
        }
        return "[" + file.getName() + "](" + file.getExternal().getUrl() + ")";
    }

    private String convertLinkToPage(Block block) {
        if (block.getLink_to_page() == null || StringUtils.isBlank(block.getLink_to_page().getPage_id())) {
            return "";
        }
        String page_id = block.getLink_to_page().getPage_id();
        if (page_id.endsWith(".md")) {
            // 移除.md后缀
            page_id = page_id.substring(0, page_id.length() - 3);
        }
        return "[[" + page_id + "]]";
    }

    /**
     * 转换段落
     * 
     * @param paragraph 段落对象
     * @return Markdown 格式的段落
     */
    private String convertParagraph(RichTexts paragraph) {
        if (paragraph == null || CollectionUtils.isEmpty(paragraph.getRich_text())) {
            return "\n";
        }

        return convertRichTexts(paragraph);
    }

    /**
     * 转换富文本
     * 
     * @param richTexts 富文本对象
     * @return Markdown 格式的文本
     */
    private String convertRichTexts(RichTexts richTexts) {
        if (richTexts == null || CollectionUtils.isEmpty(richTexts.getRich_text())) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (RichText richText : richTexts.getRich_text()) {
            if (richText.getText() != null && StringUtils.isNotBlank(richText.getText().getContent())) {
                String convertRichText = convertRichText(richText);
                sb.append(convertRichText);
            } else {
                sb.append("\n");
            }
        }
        return sb.toString();
    }

    /**
     * 转换列表项的富文本内容，会清除原始内容中的序号和项目符号
     * 
     * @param richTexts 富文本对象
     * @return 清除序号后的 Markdown 格式文本
     */
    private String convertListItemRichTexts(RichTexts richTexts) {
        String content = convertRichTexts(richTexts);

        // 清除开头的数字序号（如 "1. ", "2. " 等）
        content = content.replaceAll("^\\s*\\d+\\.\\s*", "");

        // 清除开头的项目符号（如 "- ", "• ", "* " 等）
        content = content.replaceAll("^\\s*[-•*]\\s*", "");

        // 清除开头多余的空白字符
        content = content.replaceAll("^\\s+", "");

        log.debug("清除列表项序号后的内容: {}", content);

        return content;
    }

    /**
     * 转换富文本
     * 
     * @param heading 标题对象
     * @return Markdown 格式的文本
     */
    private String convertRichTexts(Heading heading) {
        if (heading == null || CollectionUtils.isEmpty(heading.getRich_text())) {
            return "";
        }

        return heading.getRich_text().stream()
                .map(this::convertRichText)
                .collect(Collectors.joining());
    }

    /**
     * 转换单个富文本
     * 
     * @param richText 富文本对象
     * @return Markdown 格式的文本
     */
    private String convertRichText(RichText richText) {
        if (richText == null || richText.getText() == null || StringUtils.isBlank(richText.getText().getContent())) {
            return "";
        }

        String content = richText.getText().getContent();

        // 检测并保存末尾的所有空白字符（包括各种换行符、制表符、空格等）
        String trimmedContent = content.replaceAll("\\s+$", "");
        String trailingWhitespace = content.substring(trimmedContent.length());

        if (!trailingWhitespace.isEmpty()) {
            content = trimmedContent;
            // 为日志显示转义特殊字符，便于调试
            String displayWhitespace = trailingWhitespace
                    .replace("\n", "\\n")
                    .replace("\r", "\\r")
                    .replace("\t", "\\t")
                    .replace("\u00A0", "\\u00A0")
                    .replace("\u2028", "\\u2028")
                    .replace("\u2029", "\\u2029");
            log.debug("检测到内容末尾空白字符: [{}]，临时移除以正确应用样式", displayWhitespace);
        }

        // 处理文本样式 - 确保样式标记紧贴文本内容
        if (richText.getAnnotations() != null) {
            if (Boolean.TRUE.equals(richText.getAnnotations().isBold())) {
                content = "**" + content + "**";
                log.debug("应用加粗样式");
            }
            if (Boolean.TRUE.equals(richText.getAnnotations().isItalic())) {
                content = "*" + content + "*";
                log.debug("应用斜体样式");
            }
            if (Boolean.TRUE.equals(richText.getAnnotations().isStrikethrough())) {
                content = "~~" + content + "~~";
                log.debug("应用删除线样式");
            }
            if (Boolean.TRUE.equals(richText.getAnnotations().isCode())) {
                content = "`" + content + "`";
                log.debug("应用代码样式");
            }
            if (Boolean.TRUE.equals(richText.getAnnotations().isUnderline())) {
                // Markdown 不直接支持下划线，使用 HTML 标签
                content = "<u>" + content + "</u>";
                log.debug("应用下划线样式");
            }
        }

        // 处理链接
        if (richText.getHref() != null && StringUtils.isNotBlank(richText.getHref())) {
            content = "[" + content + "](" + richText.getHref() + ")";
            log.debug("应用链接: {}", richText.getHref());
        }

        // 根据原始内容的结尾情况添加适当的分隔符
        if (!trailingWhitespace.isEmpty()) {
            content = content + trailingWhitespace;
            log.debug("恢复原始空白字符");
        } else {
            content = content + " ";
            log.debug("添加空格分隔符");
        }

        return content;
    }

    /**
     * 转换代码块
     * 
     * @param block 代码块对象
     * @return Markdown 格式的代码块
     */
    private String convertCode(Block block) {
        if (block.getCode() == null || CollectionUtils.isEmpty(block.getCode().getRich_text())) {
            return "";
        }

        String language = block.getCode().getLanguage();
        String code = block.getCode().getRich_text().stream()
                .map(this::convertRichText)
                .collect(Collectors.joining());

        return "```" + language + "\n" + code + "\n```";
    }

    /**
     * 转换图片
     * 
     * @param image 图片对象
     * @return Markdown 格式的图片
     */
    private String convertImage(Image image) {
        if (image == null) {
            return "";
        }

        String imageUrl = "";
        if (image.getExternal() != null && StringUtils.isNotBlank(image.getExternal().getUrl())) {
            imageUrl = image.getExternal().getUrl();
        } else if (image.getExternal() != null && StringUtils.isNotBlank(image.getExternal().getUrl())) {
            imageUrl = image.getExternal().getUrl();
        }

        if (StringUtils.isBlank(imageUrl)) {
            return "";
        }

        String caption = "";
        if (image.getCaption() != null && !CollectionUtils.isEmpty(image.getCaption())) {
            caption = image.getCaption().stream()
                    .map(this::convertRichText)
                    .collect(Collectors.joining());
        }

        // 使用图片路径辅助类处理图片路径
        String processedImageUrl = ImagePathHelper.processImageUrl(imageUrl);

        return "![" + caption + "](" + processedImageUrl + ")";
    }

    /**
     * 转换提示框
     * 
     * @param block 提示框对象
     * @return Markdown 格式的提示框
     */
    private String convertCallout(Block block) {
        if (block.getCallout() == null || CollectionUtils.isEmpty(block.getCallout().getRich_text())) {
            return "";
        }

        String icon = block.getCallout().getIcon() != null ? block.getCallout().getIcon().getEmoji() : "";
        String text = block.getCallout().getRich_text().stream()
                .map(this::convertRichText)
                .collect(Collectors.joining());

        return "> " + icon + " " + text;
    }

    /**
     * 转换表格
     * 
     * @param block 表格对象
     * @return Markdown 格式的表格
     */
    private String convertTable(Block block) {
        if (block.getTable() == null || CollectionUtils.isEmpty(block.getTable().getChildren())) {
            return "";
        }

        // 表格转换比较复杂，这里只做简单实现
        StringBuilder table = new StringBuilder();

        // 获取表格行数据
        List<?> children = block.getTable().getChildren();

        // 表头
        if (!CollectionUtils.isEmpty(children)) {
            // 获取第一行作为表头
            Object firstRowObj = children.get(0);
            if (firstRowObj instanceof JSONObject) {
                JSONObject firstRow = (JSONObject) firstRowObj;
                JSONObject tableRow = firstRow.getJSONObject("table_row");
                if (tableRow != null) {
                    List<?> cellsObj = tableRow.getJSONArray("cells");
                    if (!CollectionUtils.isEmpty(cellsObj)) {
                        // 添加表头内容
                        table.append("|");
                        for (Object cellObj : cellsObj) {
                            if (cellObj instanceof List) {
                                @SuppressWarnings("unchecked")
                                List<RichText> richTexts = (List<RichText>) cellObj;
                                String cellContent = richTexts.stream()
                                        .map(this::convertRichText)
                                        .collect(Collectors.joining());
                                table.append(" ").append(cellContent).append(" |");
                            }
                        }
                        table.append("\n");

                        // 添加表头分隔符
                        table.append("|");
                        for (int i = 0; i < cellsObj.size(); i++) {
                            table.append(" --- |");
                        }
                        table.append("\n");
                    }
                }
            }
        }

        // 处理表格内容（从第二行开始）
        for (int i = 1; i < children.size(); i++) {
            Object rowObj = children.get(i);
            if (rowObj instanceof JSONObject) {
                JSONObject row = (JSONObject) rowObj;
                JSONObject tableRow = row.getJSONObject("table_row");
                if (tableRow != null) {
                    List<?> cellsObj = tableRow.getJSONArray("cells");
                    if (!CollectionUtils.isEmpty(cellsObj)) {
                        table.append("|");
                        for (Object cellObj : cellsObj) {
                            if (cellObj instanceof List) {
                                @SuppressWarnings("unchecked")
                                List<RichText> richTexts = (List<RichText>) cellObj;
                                String cellContent = richTexts.stream()
                                        .map(this::convertRichText)
                                        .collect(Collectors.joining());
                                table.append(" ").append(cellContent).append(" |");
                            }
                        }
                        table.append("\n");
                    }
                }
            }
        }

        // 确保表格末尾没有多余的换行符
        String result = table.toString();
        // 如果末尾有换行符，保留一个换行符
        if (result.endsWith("\n")) {
            return result;
        }
        return result;
    }

    /**
     * 转换书签
     * 
     * @param block 书签对象
     * @return Markdown 格式的书签
     */
    private String convertBookmark(Block block) {
        if (block.getBookmark() == null || StringUtils.isBlank(block.getBookmark().getUrl())) {
            return "";
        }

        String url = block.getBookmark().getUrl();
        String caption = "";
        if (block.getBookmark().getCaption() != null && !CollectionUtils.isEmpty(block.getBookmark().getCaption())) {
            caption = block.getBookmark().getCaption().stream()
                    .map(this::convertRichText)
                    .collect(Collectors.joining());
        }

        if (StringUtils.isNotBlank(caption)) {
            return "[" + caption + "](" + url + ")";
        } else {
            return "[" + url + "](" + url + ")";
        }
    }

    /**
     * 转换嵌入内容
     * 
     * @param block 嵌入内容对象
     * @return Markdown 格式的嵌入内容
     */
    private String convertEmbed(Block block) {
        if (block.getEmbed() == null || StringUtils.isBlank(block.getEmbed().getUrl())) {
            return "";
        }

        String url = block.getEmbed().getUrl();
        String caption = "";
        if (block.getEmbed().getCaption() != null && !CollectionUtils.isEmpty(block.getEmbed().getCaption())) {
            caption = block.getEmbed().getCaption().stream()
                    .collect(Collectors.joining());
        }

        if (StringUtils.isNotBlank(caption)) {
            return caption + ": <" + url + ">";
        } else {
            return "<" + url + ">";
        }
    }

    /**
     * 递归转换列表项，支持多层嵌套和混合内容
     * <p>
     * 该方法能够处理：
     * 1. 列表项的主要文本内容（rich_text）
     * 2. 列表项的子内容（children），包括嵌套列表、图片、段落等
     * 3. 正确的缩进层级控制
     * 4. 序号管理（针对有序列表）
     * </p>
     * 
     * @param listItem    列表项对象
     * @param prefix      列表前缀（如 "- " 或 "1. "）
     * @param indentLevel 缩进层级（0为顶级）
     * @return 完整的 Markdown 格式列表项内容
     */
    private String convertListItemRecursively(ListItem listItem, String prefix, int indentLevel) {
        if (listItem == null) {
            log.warn("列表项为空，返回空字符串");
            return "";
        }

        StringBuilder result = new StringBuilder();

        // 生成当前层级的缩进
        String indent = "  ".repeat(indentLevel);

        // 1. 处理主要文本内容
        String mainContent = "";
        if (!CollectionUtils.isEmpty(listItem.getRich_text())) {
            RichTexts richTexts = RichTexts.build(listItem.getRich_text());
            mainContent = convertListItemRichTexts(richTexts);
            log.debug("提取列表项主要内容: {}", mainContent);
        }

        // 添加列表项主内容
        result.append(indent).append(prefix).append(mainContent);

        // 2. 处理子内容（children）
        if (!CollectionUtils.isEmpty(listItem.getChildren())) {
            log.debug("开始处理列表项的 {} 个子块", listItem.getChildren().size());

            // 用于维护子列表的序号计数器
            int childNumberedCounter = 1;
            String prevChildType = null;

            for (Block childBlock : listItem.getChildren()) {
                if (childBlock == null) {
                    continue;
                }

                String childType = childBlock.getType();
                String childContent = "";

                // 根据子块类型进行不同处理
                switch (childType) {
                    case "bulleted_list_item":
                        // 递归处理无序子列表
                        childContent = convertListItemRecursively(
                                childBlock.getBulleted_list_item(),
                                "- ",
                                indentLevel + 1);
                        log.debug("处理无序子列表项");
                        break;

                    case "numbered_list_item":
                        // 如果前一个子块不是有序列表项，重置计数器
                        if (!"numbered_list_item".equals(prevChildType)) {
                            childNumberedCounter = 1;
                        }
                        // 递归处理有序子列表
                        childContent = convertListItemRecursively(
                                childBlock.getNumbered_list_item(),
                                childNumberedCounter + ". ",
                                indentLevel + 1);
                        childNumberedCounter++;
                        log.debug("处理有序子列表项，序号: {}", childNumberedCounter - 1);
                        break;

                    case "image":
                        // 处理图片内容，添加适当缩进
                        String imageMarkdown = convertImage(childBlock.getImage());
                        if (StringUtils.isNotBlank(imageMarkdown)) {
                            childContent = "  ".repeat(indentLevel + 1) + imageMarkdown;
                            log.debug("处理列表项中的图片内容");
                        }
                        break;

                    case "paragraph":
                        // 处理段落内容，添加适当缩进
                        String paragraphMarkdown = convertParagraph(childBlock.getParagraph());
                        if (StringUtils.isNotBlank(paragraphMarkdown)) {
                            childContent = "  ".repeat(indentLevel + 1) + paragraphMarkdown.trim();
                            log.debug("处理列表项中的段落内容");
                        }
                        break;

                    case "code":
                        // 处理代码块，添加适当缩进
                        String codeMarkdown = convertCode(childBlock);
                        if (StringUtils.isNotBlank(codeMarkdown)) {
                            // 对代码块的每一行都添加缩进
                            String codeIndent = "  ".repeat(indentLevel + 1);
                            childContent = codeIndent + codeMarkdown.replace("\n", "\n" + codeIndent);
                            log.debug("处理列表项中的代码块");
                        }
                        break;

                    default:
                        // 处理其他类型的子块
                        String otherMarkdown = convertBlock(childBlock);
                        if (StringUtils.isNotBlank(otherMarkdown)) {
                            childContent = "  ".repeat(indentLevel + 1) + otherMarkdown.trim();
                            log.debug("处理列表项中的其他类型子块: {}", childType);
                        }
                        break;
                }

                // 添加子内容到结果中
                if (StringUtils.isNotBlank(childContent)) {
                    result.append("\n").append(childContent);
                }

                prevChildType = childType;
            }
        }

        log.debug("列表项递归转换完成，缩进层级: {}", indentLevel);
        return result.toString();
    }
}