package com.maixi.road.markdown.dto;

import com.maixi.road.common.core.model.dto.ObsidianDTO;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Markdown 保存请求对象
 * <p>
 * 包含保存 Markdown 文件所需的所有信息
 * </p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarkdownSaveRQ {
    
    /**
     * 用户唯一标识
     */
    private String unionId;
    
    /**
     * 文章表单信息
     */
    private ResolveFormRQ userForm;
    
    /**
     * 保存路径，如果为空则使用默认路径
     */
    private String savePath;
    
    /**
     * 文件名，如果为空则使用文章标题作为文件名
     */
    private String fileName;
    
    /**
     * 是否创建资源目录，用于保存图片等资源
     */
    @Builder.Default
    private boolean createAssetsDir = true;
    
    /**
     * 是否下载远程图片到本地
     */
    @Builder.Default
    private boolean downloadImages = false;
    
    /**
     * 是否包含元数据（标题、作者、日期等）
     */
    @Builder.Default
    private boolean includeMetadata = true;
    
    /**
     * 是否保存到 S3
     */
    @Builder.Default
    private boolean saveToS3 = true;

    /**
     * Obsidian 配置
     */
    private ObsidianDTO obConfig;

    private boolean isArticle;
}