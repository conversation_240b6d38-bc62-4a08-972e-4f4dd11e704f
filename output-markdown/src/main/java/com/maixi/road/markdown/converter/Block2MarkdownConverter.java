package com.maixi.road.markdown.converter;

import java.util.List;

import com.maixi.road.common.integration.notion.model.block.Block;

/**
 * Block 到 Markdown 的转换器接口
 * <p>
 * 负责将 Block 对象转换为 Markdown 格式
 * </p>
 */
public interface Block2MarkdownConverter {
    
    /**
     * 将 Block 对象转换为 Markdown 格式
     * 
     * @param blocks Block 对象列表
     * @return Markdown 格式的内容
     */
    String convertToMarkdown(List<Block> blocks);
    
    /**
     * 将单个 Block 对象转换为 Markdown 格式
     * 
     * @param block Block 对象
     * @return Markdown 格式的内容
     */
    String convertBlock(Block block);
}