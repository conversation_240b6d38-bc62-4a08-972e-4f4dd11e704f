package com.maixi.road.markdown.util;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 图片路径辅助类
 * <p>
 * 用于处理图片路径和下载图片
 * </p>
 */
@Slf4j
public class ImagePathHelper {
    
    /**
     * 处理图片URL
     * <p>
     * 如果是远程URL，则直接返回；如果是本地路径，则返回相对路径
     * </p>
     * 
     * @param imageUrl 图片URL
     * @return 处理后的图片URL
     */
    public static String processImageUrl(String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            return "";
        }
        
        // 如果是远程URL，则直接返回
        if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
            return imageUrl;
        }
        
        // 如果是本地路径，则返回相对路径
        return imageUrl;
    }
    
    /**
     * 下载图片并保存到本地
     * 
     * @param imageUrl 图片URL
     * @param assetsDir 资源目录
     * @param fileName 文件名（可选，如果为空则使用UUID生成）
     * @return 保存后的本地路径
     */
    public static String downloadAndSaveImage(String imageUrl, String assetsDir, String fileName) {
        if (StringUtils.isBlank(imageUrl) || StringUtils.isBlank(assetsDir)) {
            return "";
        }
        
        try {
            // 创建资源目录
            Path assetsDirPath = Paths.get(assetsDir);
            if (!Files.exists(assetsDirPath)) {
                Files.createDirectories(assetsDirPath);
            }
            
            // 生成文件名
            String extension = FilenameUtils.getExtension(imageUrl);
            if (StringUtils.isBlank(extension)) {
                extension = "jpg"; // 默认扩展名
            }
            
            String finalFileName;
            if (StringUtils.isNotBlank(fileName)) {
                finalFileName = fileName + "." + extension;
            } else {
                finalFileName = UUID.randomUUID().toString() + "." + extension;
            }
            
            // 下载图片
            URL url = new URL(imageUrl);
            Path targetPath = assetsDirPath.resolve(finalFileName);
            
            try (InputStream in = url.openStream()) {
                Files.copy(in, targetPath, StandardCopyOption.REPLACE_EXISTING);
            }
            
            // 返回相对路径
            return "./assets/" + finalFileName;
        } catch (IOException e) {
            log.error("下载图片失败, imageUrl={}", imageUrl, e);
            return imageUrl; // 下载失败则返回原始URL
        }
    }
    
    /**
     * 批量下载图片
     * 
     * @param markdown Markdown内容
     * @param assetsDir 资源目录
     * @return 替换图片URL后的Markdown内容
     */
    public static String downloadAllImages(String markdown, String assetsDir) {
        if (StringUtils.isBlank(markdown) || StringUtils.isBlank(assetsDir)) {
            return markdown;
        }
        
        // 创建资源目录
        Path assetsDirPath = Paths.get(assetsDir);
        try {
            if (!Files.exists(assetsDirPath)) {
                Files.createDirectories(assetsDirPath);
            }
        } catch (IOException e) {
            log.error("创建资源目录失败, assetsDir={}", assetsDir, e);
            return markdown;
        }
        
        // 使用正则表达式匹配Markdown中的图片
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("!\\[(.*?)\\]\\((https?://[^\\s)]+)\\)");
        java.util.regex.Matcher matcher = pattern.matcher(markdown);
        
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String alt = matcher.group(1);
            String url = matcher.group(2);
            
            // 下载图片
            String localPath = downloadAndSaveImage(url, assetsDir, null);
            
            // 替换URL
            matcher.appendReplacement(sb, "![" + alt + "](" + localPath.replace("$", "\\$") + ")");
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
}