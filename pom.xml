<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.2</version>
        <relativePath />
        <!-- lookup parent from repository -->
    </parent>
    <groupId>com.maixi.road</groupId>
    <artifactId>roadserver</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>the-road</name>
    <description>Demo project for Spring Boot</description>

    <properties>
        <java.version>21</java.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
        <lombok.version>1.18.36</lombok.version>
        <okhttp.version>4.12.0</okhttp.version>
    </properties>

    <modules>
        <module>roadserver-app</module>
        <module>s3-proxy</module>
        <module>road-clipper</module>
        <module>road-common</module>
        <module>output-notion</module>
        <module>output-markdown</module>
        <module>cloud-function</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.aventrix.jnanoid</groupId>
            <artifactId>jnanoid</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>3.5.9</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>33.0.0-jre</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <!-- 激活条件为"dev"系统属性存在 -->
                <property>
                    <name>env</name>
                    <value>dev</value>
                </property>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <activation>
                <!-- 激活条件为"prod"环境变量存在 -->
                <property>
                    <name>env</name>
                    <value>prod</value>
                </property>
            </activation>
        </profile>
        <profile>
            <id>aliyun</id>
            <activation>
                <!-- 激活条件为"prod"环境变量存在 -->
                <property>
                    <name>env</name>
                    <value>aliyun</value>
                </property>
            </activation>
        </profile>
    </profiles>
</project>