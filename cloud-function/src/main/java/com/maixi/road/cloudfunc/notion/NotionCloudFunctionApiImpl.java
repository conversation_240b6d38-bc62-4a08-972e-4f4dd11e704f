package com.maixi.road.cloudfunc.notion;

import java.io.ByteArrayInputStream;

import com.aliyun.fc20230330.Client;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.aliyun.fc20230330.models.InvokeFunctionRequest;
import com.aliyun.fc20230330.models.InvokeFunctionResponse;
import com.aliyun.tea.TeaException;
import com.maixi.road.cloudfunc.notion.dto.rq.ToNotionRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class NotionCloudFunctionApiImpl implements NotionCloudFunctionApi {

    @Resource
    private Client cloudFunctionDJ;

    @Override
    public PageCreateRS sendToNotion(ToNotionRQ request) {
        try {
            InvokeFunctionRequest invokeFunctionRequest = new InvokeFunctionRequest();
            invokeFunctionRequest.setQualifier("prod");
            invokeFunctionRequest.setBody(new ByteArrayInputStream(JSON.toJSONBytes(request)));
            InvokeFunctionResponse result = cloudFunctionDJ.invokeFunction("go-to-notion", invokeFunctionRequest);
            // InvokeFunctionResponse result = cloudFunctionDJ.invokeFunction("new_notion_client", invokeFunctionRequest);
            return JSON.parseObject(result.getBody(), PageCreateRS.class);
        } catch (TeaException error) {
            log.error(error.message);
        } catch (Exception error) {
            log.error(error.getMessage());
        }
        return null;
    }
}
