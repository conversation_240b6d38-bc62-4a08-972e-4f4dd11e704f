package com.maixi.road.cloudfunc.notion.dto.rq;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.common.integration.notion.model.page.Page;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ToNotionRQ {
    /**
     * 0 是 createPage,1 是 appendBlock
     */
    private Integer type;
    private String notionApiKey;
    private String unionId;

    // =========createPage 参数=========//
    private Page page;

    // =========appendBlock 参数=========//
    private String blockId;
    private BlockAppendRQ blockAppendRQ;

    // =========search 参数=========//
    private String accessToken;
    private Integer relationId;

    // =========createToken 参数=========//
    private String code;
    /**
     * 是否使用新集成
     */
    private Integer useNewIntegration;

    //=========update 参数=========//
    private String databaseId;
    private JSONObject updateBody;

    public ToNotionRQ(String notionApiKey, Page page, String unionId) {
        this.type = 0;
        this.notionApiKey = notionApiKey;
        this.unionId = unionId;
        this.page = page;
    }

    public ToNotionRQ(String notionApiKey, String blockId, String unionId, BlockAppendRQ blockAppendRQ) {
        this.type = 1;
        this.notionApiKey = notionApiKey;
        this.unionId = unionId;
        this.blockId = blockId;
        this.blockAppendRQ = blockAppendRQ;
    }

    public ToNotionRQ(String unionId, String accessToken, Integer relationId) {
        this.type = 2;
        this.unionId = unionId;
        this.accessToken = accessToken;
        this.relationId = relationId;
    }

    public ToNotionRQ(String code) {
        this.type = 3;
        this.code = code;
    }

    public ToNotionRQ(String code, boolean useNewIntegration) {
        this.type = 3;
        this.code = code;
        this.useNewIntegration = useNewIntegration ? 1 : 0;
    }

    public ToNotionRQ(String notionApiKey, String databaseId, JSONObject updateBody) {
        this.type = 4;
        this.notionApiKey = notionApiKey;
        this.databaseId = databaseId;
        this.updateBody = updateBody;
    }
}
