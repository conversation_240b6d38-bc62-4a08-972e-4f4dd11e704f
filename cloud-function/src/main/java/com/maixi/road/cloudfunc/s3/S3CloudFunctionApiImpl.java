package com.maixi.road.cloudfunc.s3;

import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.fc20230330.Client;
import com.aliyun.fc20230330.models.InvokeFunctionHeaders;
import com.aliyun.fc20230330.models.InvokeFunctionRequest;
import com.aliyun.fc20230330.models.InvokeFunctionResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.maixi.road.cloudfunc.s3.dto.S3CloudRQ;
import com.maixi.road.cloudfunc.s3.dto.S3CloudRS;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;

@Slf4j
@Component
public class S3CloudFunctionApiImpl implements S3CloudFunctionApi {

    @Resource
    private Client cloudFunctionDJ;
    @Resource
    private Client cloudFunctionSZ;
    @Resource
    private Client cloudFunctionHZ;
//    @Resource
//    private RedisManager redisManager;
    @Value("${aliyun.severless.domestic}")
    private String domestic;

    private final String SZ_SHENZHEN = "cn-shenzhen";
    private final String HZ_HANGZHOU = "cn-hangzhou";

    private static String getFunctionName() {
        return "go-s3-proxy";
    }

    @Override
    public S3CloudRS transformPicture(String unionId, S3CloudRQ request) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("transformPicture");
        try {
//            redisManager.transformPictureInitSet(unionId);
            log.info("request = {}", JSON.toJSONString(request));
            InvokeFunctionRequest invokeFunctionRequest = new InvokeFunctionRequest();
            invokeFunctionRequest.setQualifier("prod");
            invokeFunctionRequest.setBody(new ByteArrayInputStream(JSON.toJSONBytes(request)));
            InvokeFunctionHeaders headers = new InvokeFunctionHeaders();
            headers.setXFcInvocationType("Sync");
            RuntimeOptions runtimeOptions = new RuntimeOptions();
            String functionName = getFunctionName();
            InvokeFunctionResponse result;
            if (request.getS3Config().getProvider().contains("r2")) {
                log.info("使用东京函数");
                result = cloudFunctionDJ.invokeFunctionWithOptions(functionName,
                        invokeFunctionRequest, headers, runtimeOptions);
            } else {
                if (SZ_SHENZHEN.equals(domestic)) {
                    log.info("使用深圳函数");
                    result = cloudFunctionSZ.invokeFunctionWithOptions(functionName,
                            invokeFunctionRequest, headers, runtimeOptions);
                } else {
                    log.info("使用杭州函数");
                    result = cloudFunctionHZ.invokeFunctionWithOptions(functionName,
                            invokeFunctionRequest, headers, runtimeOptions);
                }
            }
            String responseBody = IoUtil.read(new InputStreamReader(result.getBody(), StandardCharsets.UTF_8));
            if (StringUtils.isBlank(responseBody)) {
                log.error("图片转存结果为空,body = null");
                return new S3CloudRS(new HashMap<>());
            }
            S3CloudRS s3Response = JSON.parseObject(responseBody, S3CloudRS.class);
//            redisManager.transformPictureEndSet(unionId);
            return s3Response;
        } catch (Exception error) {
            log.error("图片转存触发异常,message={}", error.getMessage());
        } finally {
            stopWatch.stop();
            log.info("[{}] rt:{}", stopWatch.lastTaskInfo().getTaskName(), stopWatch.getTotalTimeSeconds());
        }
        return new S3CloudRS(new HashMap<>());
    }

}
