package com.maixi.road.cloudfunc.s3.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

import com.maixi.road.common.integration.s3.config.S3Config;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class S3CloudRQ {

    private S3Config s3Config;
    private List<String> urls;
    private Map<String, String> extraParams;
}