# output-cloudfunc 模块文档

## 1. 模块说明
本模块提供基于阿里云函数的云端服务能力，主要包括图片上传服务和Notion集成服务。通过云函数实现无服务器架构，降低运维成本。

## 2. 主要功能介绍

### 图片上传服务
- 通过阿里云函数实现图片上传到云存储
- 支持多种图片格式处理
- 返回图片访问URL

### Notion集成服务
- 通过阿里云函数与Notion API交互
- 支持数据同步到Notion数据库
- 提供内容管理能力

## 3. 配置指南

### 阿里云函数基础配置
```yaml
service:
  name: output-cloudfunc
  description: 云函数服务模块

function:
  runtime: java8
  memorySize: 512
  timeout: 60
```

### 区域设置
```yaml
region: cn-hangzhou
```

### 超时设置
建议超时时间根据业务需求设置：
- 图片上传：30秒
- Notion集成：60秒

### 环境变量配置
```properties
# 阿里云访问密钥
ALIYUN_ACCESS_KEY_ID=your-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-access-key-secret

# Notion集成配置
NOTION_API_KEY=your-notion-api-key
NOTION_DATABASE_ID=your-database-id
```

## 4. 使用示例

### 图片上传调用示例
```java
AcFunctionImageUploadClient client = new AcFunctionImageUploadClient();
S3CloudRequest request = new S3CloudRequest();
request.setBucketName("your-bucket");
request.setObjectKey("example.jpg");
request.setImageBytes(imageData);

S3CloudResponse response = client.uploadImage(request);
System.out.println("Image URL: " + response.getImageUrl());
```

### Notion集成调用示例
```java
AcFunctionNotionClient client = new AcFunctionNotionClient();
NotionPageRequest request = new NotionPageRequest();
request.setDatabaseId("your-database-id");
request.setTitle("示例标题");
request.setContent("示例内容");

NotionPageResponse response = client.createPage(request);
System.out.println("Page created: " + response.getPageId());