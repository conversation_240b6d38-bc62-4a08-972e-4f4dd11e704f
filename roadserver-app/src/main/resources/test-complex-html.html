<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML解析测试页面</title>
</head>

<body>
    <div id="js_content">
        <div id="js_name"></div>
        <h1>HTML解析测试页面</h1>

        <!-- 标题测试 -->
        <h1>一级标题</h1>
        <h2>二级标题</h2>
        <h3>三级标题</h3>

        <!-- 段落测试 -->
        <p>这是一个普通的段落，包含一些<strong>粗体文字</strong>和<em>斜体文字</em>。</p>
        <p>这个段落包含<a href="https://example.com">链接</a>和<code>代码片段</code>。</p>

        <!-- 列表测试 - 简单无序列表 -->
        <h2>简单无序列表</h2>
        <ul>
            <li>无序列表项1</li>
            <li>无序列表项2</li>
            <li>无序列表项3</li>
        </ul>

        <!-- 列表测试 - 简单有序列表 -->
        <h2>简单有序列表</h2>
        <ol>
            <li>有序列表项1</li>
            <li>有序列表项2</li>
            <li>有序列表项3</li>
        </ol>

        <!-- 嵌套列表测试 -->
        <h2>嵌套列表测试</h2>
        <ul>
            <li>一级无序列表项1
                <ul>
                    <li>二级无序列表项1</li>
                    <li>二级无序列表项2</li>
                </ul>
            </li>
            <li>一级无序列表项2
                <ol>
                    <li>二级有序列表项1</li>
                    <li>二级有序列表项2</li>
                </ol>
            </li>
            <li>一级无序列表项3</li>
        </ul>

        <!-- 嵌套列表测试 -->
        <h2>嵌套列表测试-2</h2>
        <ol>
            <li>一级无序列表项1</li>
            <ul>
                <li>二级无序列表项1</li>
                <li>二级无序列表项2</li>
            </ul>
            <li>一级无序列表项2</li>
            <ol>
                <li>二级有序列表项1</li>
                <li>二级有序列表项2</li>
            </ol>
            <li>一级无序列表项3</li>
        </ol>

        <!-- 混合嵌套列表 -->
        <h2>混合嵌套列表</h2>
        <ol>
            <li>有序列表项1
                <ul>
                    <li>嵌套无序项1</li>
                    <li>嵌套无序项2</li>
                </ul>
            </li>
            <li>有序列表项2
                <ol>
                    <li>嵌套有序项1</li>
                    <li>嵌套有序项2
                        <ul>
                            <li>三级无序项1</li>
                            <li>三级无序项2</li>
                        </ul>
                    </li>
                </ol>
            </li>
        </ol>

        <!-- 列表项包含图片 -->
        <h2>列表项包含图片</h2>
        <ul>
            <li>带图片的列表项1
                <img src="https://mmbiz.qpic.cn/sz_mmbiz_png/ibBPzzwQ3qZWAalnharyhJiaVJ5FX1IscoXNZSvQ6A3xGEATuicEN1ibLfHtGBYqyDayqU66E5O43yX6alfJVHBiayw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1" alt="测试图片1">
            </li>
            <li>带图片的列表项2
                <p>段落中的图片 <img src="https://mmbiz.qpic.cn/sz_mmbiz_png/ibBPzzwQ3qZWAalnharyhJiaVJ5FX1IscoXNZSvQ6A3xGEATuicEN1ibLfHtGBYqyDayqU66E5O43yX6alfJVHBiayw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1" alt="测试图片2"> 后续文字</p>
            </li>
            <li>复杂内容列表项
                <p>这是一个段落</p>
                <img src="https://mmbiz.qpic.cn/sz_mmbiz_png/ibBPzzwQ3qZWAalnharyhJiaVJ5FX1IscoXNZSvQ6A3xGEATuicEN1ibLfHtGBYqyDayqU66E5O43yX6alfJVHBiayw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1" alt="测试图片3">
                <div>
                    <span>嵌套的图片：<img src="https://mmbiz.qpic.cn/sz_mmbiz_png/ibBPzzwQ3qZWAalnharyhJiaVJ5FX1IscoXNZSvQ6A3xGEATuicEN1ibLfHtGBYqyDayqU66E5O43yX6alfJVHBiayw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1"
                            alt="嵌套图片"></span>
                </div>
            </li>
        </ul>

        <!-- 图片测试 -->
        <h2>图片测试</h2>
        <img src="https://mmbiz.qpic.cn/sz_mmbiz_png/ibBPzzwQ3qZWAalnharyhJiaVJ5FX1IscoXNZSvQ6A3xGEATuicEN1ibLfHtGBYqyDayqU66E5O43yX6alfJVHBiayw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1" alt="单独的图片">
        <p>段落中的<img src="https://mmbiz.qpic.cn/sz_mmbiz_png/ibBPzzwQ3qZWAalnharyhJiaVJ5FX1IscoXNZSvQ6A3xGEATuicEN1ibLfHtGBYqyDayqU66E5O43yX6alfJVHBiayw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1" alt="内联图片">内联图片。</p>

        <!-- 代码块测试 -->
        <h2>代码块测试</h2>
        <pre><code>function hello() {
    console.log("Hello, World!");
    return "success";
}</code></pre>

        <!-- 引用测试 -->
        <h2>引用测试</h2>
        <blockquote>
            <p>这是一个引用块，可以包含多行内容。</p>
            <p>引用块的第二段。</p>
        </blockquote>

        <!-- 表格测试 -->
        <h2>表格测试</h2>
        <table border="1">
            <thead>
                <tr>
                    <th>姓名</th>
                    <th>年龄</th>
                    <th>城市</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>张三</td>
                    <td>25</td>
                    <td>北京</td>
                </tr>
                <tr>
                    <td>李四</td>
                    <td>30</td>
                    <td>上海</td>
                </tr>
            </tbody>
        </table>

        <!-- 合并单元格测试 -->
        <h2>合并单元格测试</h2>
        
        <!-- 列合并测试 -->
        <h3>列合并（colspan）测试</h3>
        <table border="1">
            <thead>
                <tr>
                    <th colspan="3">员工信息表</th>
                </tr>
                <tr>
                    <th>姓名</th>
                    <th>年龄</th>
                    <th>部门</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>王五</td>
                    <td>28</td>
                    <td>技术部</td>
                </tr>
                <tr>
                    <td colspan="2">赵六（兼职）</td>
                    <td>市场部</td>
                </tr>
                <tr>
                    <td>孙七</td>
                    <td colspan="2">25岁，人事部</td>
                </tr>
            </tbody>
        </table>
        
        <!-- 行合并测试 -->
        <h3>行合并（rowspan）测试</h3>
        <table border="1">
            <thead>
                <tr>
                    <th>部门</th>
                    <th>员工</th>
                    <th>职位</th>
                    <th>薪资</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="3">技术部</td>
                    <td>张三</td>
                    <td>前端工程师</td>
                    <td>12000</td>
                </tr>
                <tr>
                    <td>李四</td>
                    <td>后端工程师</td>
                    <td>15000</td>
                </tr>
                <tr>
                    <td>王五</td>
                    <td>测试工程师</td>
                    <td>10000</td>
                </tr>
                <tr>
                    <td rowspan="2">市场部</td>
                    <td>赵六</td>
                    <td>市场专员</td>
                    <td>8000</td>
                </tr>
                <tr>
                    <td>孙七</td>
                    <td>销售经理</td>
                    <td>18000</td>
                </tr>
            </tbody>
        </table>
        
        <!-- 复杂合并测试 -->
        <h3>复杂合并（行列同时合并）测试</h3>
        <table border="1">
            <thead>
                <tr>
                    <th colspan="4">公司组织架构</th>
                </tr>
                <tr>
                    <th>部门</th>
                    <th>团队</th>
                    <th>员工</th>
                    <th>备注</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="4">研发部</td>
                    <td rowspan="2">前端团队</td>
                    <td>张三</td>
                    <td>React专家</td>
                </tr>
                <tr>
                    <td>李四</td>
                    <td>Vue专家</td>
                </tr>
                <tr>
                    <td rowspan="2">后端团队</td>
                    <td>王五</td>
                    <td>Java工程师</td>
                </tr>
                <tr>
                    <td>赵六</td>
                    <td>Python工程师</td>
                </tr>
                <tr>
                    <td rowspan="2">运营部</td>
                    <td colspan="2">孙七（运营总监）</td>
                    <td>负责整体运营</td>
                </tr>
                <tr>
                    <td>内容团队</td>
                    <td>周八</td>
                    <td>内容编辑</td>
                </tr>
                <tr>
                    <td colspan="4" style="text-align: center; font-weight: bold;">总计：7名员工</td>
                </tr>
            </tbody>
        </table>
        
        <!-- 不规则合并测试 -->
        <h3>不规则合并测试</h3>
        <table border="1">
            <thead>
                <tr>
                    <th>项目</th>
                    <th>Q1</th>
                    <th>Q2</th>
                    <th>Q3</th>
                    <th>Q4</th>
                    <th>年度总计</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>收入</td>
                    <td>100万</td>
                    <td>120万</td>
                    <td>150万</td>
                    <td>180万</td>
                    <td rowspan="2">550万</td>
                </tr>
                <tr>
                    <td>支出</td>
                    <td>80万</td>
                    <td>90万</td>
                    <td>100万</td>
                    <td>110万</td>
                </tr>
                <tr>
                    <td>利润</td>
                    <td colspan="4">各季度利润：20万、30万、50万、70万</td>
                    <td>170万</td>
                </tr>
                <tr>
                    <td colspan="6" style="text-align: center; background-color: #f0f0f0;">
                        <strong>备注：以上数据仅供测试使用</strong>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <!-- 嵌套内容的合并单元格测试 -->
        <h3>合并单元格包含复杂内容测试</h3>
        <table border="1">
            <thead>
                <tr>
                    <th>类型</th>
                    <th>内容</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="3">多媒体内容</td>
                    <td>
                        <img src="https://mmbiz.qpic.cn/sz_mmbiz_png/ibBPzzwQ3qZWAalnharyhJiaVJ5FX1IscoXNZSvQ6A3xGEATuicEN1ibLfHtGBYqyDayqU66E5O43yX6alfJVHBiayw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1"
                            alt="表格中的图片" style="width: 100px;">
                    </td>
                    <td>图片内容</td>
                </tr>
                <tr>
                    <td>
                        <a href="https://example.com">链接内容</a>
                    </td>
                    <td>外部链接</td>
                </tr>
                <tr>
                    <td>
                        <ul>
                            <li>列表项1</li>
                            <li>列表项2</li>
                        </ul>
                    </td>
                    <td>列表内容</td>
                </tr>
                <tr>
                    <td colspan="2">
                        <p>这是一个包含<strong>粗体</strong>和<em>斜体</em>的段落。</p>
                        <p>还包含<code>代码片段</code>和<mark>高亮文本</mark>。</p>
                    </td>
                    <td>富文本内容</td>
                </tr>
            </tbody>
        </table>

        <!-- 分割线测试 -->
        <h2>分割线测试</h2>
        <p>分割线前的内容</p>
        <hr>
        <p>分割线后的内容</p>

        <!-- 富文本格式测试 -->
        <h2>富文本格式测试</h2>
        <p>这里有<strong>粗体</strong>、<em>斜体</em>、<u>下划线</u>、<s>删除线</s>、<code>代码</code>文字。</p>
        <p><mark>高亮文本</mark>和<small>小号文字</small>。</p>

        <!-- 复杂嵌套结构测试 -->
        <h2>复杂嵌套结构测试</h2>
        <div>
            <h3>嵌套标题</h3>
            <p>嵌套段落包含<strong>粗体</strong>和<a href="https://example.com">链接</a>。</p>
            <ul>
                <li>嵌套列表项1
                    <p>列表项中的段落</p>
                    <img src="https://mmbiz.qpic.cn/sz_mmbiz_png/ibBPzzwQ3qZWAalnharyhJiaVJ5FX1IscoXNZSvQ6A3xGEATuicEN1ibLfHtGBYqyDayqU66E5O43yX6alfJVHBiayw/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1" alt="嵌套图片">
                    <ul>
                        <li>更深层的嵌套项1</li>
                        <li>更深层的嵌套项2</li>
                    </ul>
                </li>
                <li>嵌套列表项2</li>
            </ul>
        </div>

        <!-- 链接测试 -->
        <h2>链接测试</h2>
        <p><a href="https://www.baidu.com">百度链接</a></p>
        <p><a href="https://www.google.com" title="Google搜索">带标题的链接</a></p>

        <!-- 书签测试 -->
        <h2>书签测试</h2>
        <p>这里可以添加一些书签链接用于测试书签解析功能。</p>

        <!-- 嵌入内容测试 -->
        <h2>嵌入内容测试</h2>
        <iframe src="https://www.example.com" width="300" height="200" title="嵌入框架"></iframe>
    </div>
</body>

</html>