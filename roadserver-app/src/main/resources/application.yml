# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 200
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 10


# 日志配置
logging:
  level:
    org.springframework: warn
  file:
    path: ${user.home}/logs

sls:
  tencent:
    endpoint: ap-guangzhou.cls.tencentcs.com
    topicId: 85baa1cc-f88d-4bcc-bbbb-2fff842b65f7
    secretId: AKID9BDCPZisx0uytaFlCd7GNUkvtdMwUtHX
    secretKey: CNYZWs6FG4jFa0LbgZG0c3V0MYZXLwJI

# DataSource Config
spring:
  main:
    allow-circular-references: true
  profiles:
    active: dev
  application:
    name: roadserver
  output:
    ansi:
      enabled: always
  config:
    import:
      - classpath:clipper-config.yml
  thymeleaf:
    cache: false
    prefix: classpath:/templates/
    encoding: UTF-8
    suffix: .html
    mode: HTML
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB


wx:
  cp:
    corpId: ww159be3581f507b9a
    appConfigs:
      - agentId: 2024330
        secret: WENcyFYJKvrhnnBddA4hYeAjep6G9d5v26DQ7Ekq2XQ
        token: jLBYQ9XbTg1rBvCY75V9OvK667I1LmY
        aesKey: o8pDNJAJ9VnrWAEkw9BcvAvOVe6JrUskOBtmVQk2Sks

  miniapp:
    configs:
      # <EMAIL>
      - appId: wx34b2cfe3fdbc3b61
        secret: e6e14c66d6d513e117b8ad89c44bbcdb
        token:
        aesKey:
        msgDataFormat: JSON
      # <EMAIL>
      - appId: wxe23374330660c146
        secret: 1f48c64d695a58cdcc6ecc5d136a1e7c
        token:
        aesKey:
        msgDataFormat: JSON
      # <EMAIL>
      - appId: wxfa42c9c1b1ea3807
        secret: 747a70d559f089bbd3d1622294cfb23b
        token:
        aesKey:
        msgDataFormat: JSON
  pay:
    # 多小程序支付配置 - 共用商户号
    configs:
      # 主小程序 - <EMAIL>
      - appId: wx34b2cfe3fdbc3b61
        mchId: 1665776770
        mchKey: O8O71MQPv6BKG7UgIb86ZCtwrbthdq29
        keyPath: classpath:cert/apiclient_cert.p12
      # FreeHelper小程序 - <EMAIL>  
      - appId: wxfa42c9c1b1ea3807
        mchId: 1665776770
        mchKey: O8O71MQPv6BKG7UgIb86ZCtwrbthdq29
        keyPath: classpath:cert/apiclient_cert.p12
    # 向后兼容：单配置模式（如果configs为空时使用）
    appId: wx34b2cfe3fdbc3b61
    mchId: 1665776770
    mchKey: O8O71MQPv6BKG7UgIb86ZCtwrbthdq29
    keyPath: classpath:cert/apiclient_cert.p12