<!DOCTYPE html>
<html lang="en" xmlns:th="https://www.thymeleaf.org/">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>授权提示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f7f7f7;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            color: #333;
        }
        p {
            color: #666;
            font-size: 18px;
        }
        .box{
            margin-top: 40px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            gap: 10px;
            /*margin: 0;
            padding: 0;*/

            b{
                color: red;
            }

            div{
                color: #666;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <h1>操作提示</h1>
    <p th:text="${msg}"></p>
    <div class="box">
        <div>授权处理中，请勿频繁刷新页面</div>
    </div>
</div>
</body>
</html>