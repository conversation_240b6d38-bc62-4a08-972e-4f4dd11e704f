<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>

    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <springProperty name="port" scope="context" source="server.port" defaultValue="0000"/>
    <springProperty name="endpoint" source="sls.tencent.endpoint" defaultValue="ap-guangzhou.cls.tencentcs.com"/>
    <!-- 日志输出格式 -->
    <property name="log.pattern" value="%d{yyyy-MM-dd'T'HH:mm:ss.SSS} [%thread] [${port}] %-5level %logger{20} - [%method,%line] - [%X{traceId:-N/A}] - %msg%n"/>

    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>


    <!-- 系统 Info 日志输出 -->
    <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/info.log</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件名格式 -->
            <fileNamePattern>${LOG_PATH}/backlog/info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 7 天 -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
        </filter>
    </appender>

    <!-- 控制台 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <appender name="cls" class="com.tencentcloudapi.cls.LoghubAppender">
        <!--必选项-->
        <!--域名配置-->
        <endpoint>${endpoint}</endpoint>
        <!--密钥信息-->
        <!--请确保密钥关联的账号具有相应的SDK日志上传权限-->
        <accessKeyId>AKID9BDCPZisx0uytaFlCd7GNUkvtdMwUtHX</accessKeyId>
        <accessKeySecret>CNYZWs6FG4jFa0LbgZG0c3V0MYZXLwJI</accessKeySecret>
        <!--日志主题ID-->
        <topicId>85baa1cc-f88d-4bcc-bbbb-2fff842b65f7</topicId>

        <!-- 可选项 设置时间格式 -->
        <timeFormat>yyyy-MM-dd'T'HH:mm:ssZ</timeFormat>
        <timeZone>Asia/Shanghai</timeZone>
        <customFields>{"serverPort":"${port}"}</customFields>
        <encoder>
            <pattern>[%X{traceId:-N/A}] - %msg</pattern>
        </encoder>
        <mdcFields>traceId</mdcFields>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
        </filter>
    </appender>

    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="warn"/>

    <logger name="com.github.binarywang.wxpay.service.impl.WxPayServiceImpl" level="OFF"/>

    <!--系统操作日志-->
    <springProfile name="prod">
        <root level="info">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="info"/>
            <appender-ref ref="cls"/>
        </root>
    </springProfile>

    <springProfile name="aliyun">
        <root level="info">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="info"/>
            <appender-ref ref="cls"/>
        </root>
    </springProfile>

    <springProfile name="!prod">
        <root level="info">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="info"/>
        </root>
    </springProfile>

</configuration>