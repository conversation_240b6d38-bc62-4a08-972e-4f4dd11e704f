spring:
  data:
    redis:
      database: 0
      host: **********
      port: 6379
      username: theroad
      password: labuladuo&2334
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************
    username: notionclipper
    password: clipper_0106
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      ## 连接池最大连接数，默认是10
      maximum-pool-size: 5
      ## 最小空闲连接数量
      minimum-idle: 1
      connection-test-query: SELECT 1
      pool-name: HikariCP-Pool
      ## 数据库连接超时时间,默认30秒，即30000
      connection-timeout: 30000
      ## 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 180000
      ## 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      max-lifetime: 1800000
      ## 此属性控制从池返回的连接的默认自动提交行为,默认值：true
      auto-commit: true

sls:
  tencent:
    # 内网
    endpoint: ap-guangzhou.cls.tencentyun.com

aliyun:
  severless:
    domestic: cn-shenzhen
    overseas: ap-northeast-1
  fc:
    access-key-id: LTAI5tLmzi49VU9umaXzbYNU
    access-key-secret: ******************************
    endpoint-hz: 1849264232287497.cn-hangzhou.fc.aliyuncs.com
    endpoint-sz: 1849264232287497.cn-shenzhen.fc.aliyuncs.com
    endpoint-dj: 1849264232287497.ap-northeast-1.fc.aliyuncs.com
    connect-timeout: 60000
    read-timeout: 60000