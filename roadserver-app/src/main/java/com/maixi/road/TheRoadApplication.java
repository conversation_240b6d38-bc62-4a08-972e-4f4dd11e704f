package com.maixi.road;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;

@EnableScheduling
@Slf4j
@SpringBootApplication
@EnableAsync
@MapperScan(value = {"com.maixi.road.admin.biz.dao"})
public class TheRoadApplication {

	public static void main(String[] args) {
		// 获取 JVM 启动参数
		RuntimeMXBean runtimeMxBean = ManagementFactory.getRuntimeMXBean();
		runtimeMxBean.getInputArguments().forEach(log::info);
		SpringApplication.run(TheRoadApplication.class, args);
	}

}
