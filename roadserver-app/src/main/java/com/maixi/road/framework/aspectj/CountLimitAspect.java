package com.maixi.road.framework.aspectj;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.biz.service.INotionResourceService;
import com.maixi.road.admin.manager.UserManager;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.enums.ActionEnum;
import com.maixi.road.framework.annotation.CountLimit;
import com.maixi.road.framework.config.CacheManager;
import com.maixi.road.framework.config.RedisManager;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Aspect
@Component
@Slf4j
public class CountLimitAspect {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private INotionResourceService notionResourceService;
    @Resource
    private IMemberService memberService;
    @Resource
    private RedisManager redisManager;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private UserManager userManager;

    @Around("@annotation(countLimit)")
    public Object around(ProceedingJoinPoint point, CountLimit countLimit) throws Throwable {
        MethodSignature signature = (MethodSignature) point.getSignature();
        String unionId = getUnionId(point, signature);
        if (unionId == null) {
            log.error("未找到unionId参数，跳过限制检查");
            return point.proceed();
        }
        
        Integer userId = userManager.getByUnionId(unionId);
        if (userId == null) {
            log.error("未找到userId,unionId={}, 跳过限制检查", unionId);
            return point.proceed();
        }
        ActionEnum action = getActionByCountType(countLimit.countType());

        boolean isVip = memberService.checkIfVip(unionId);
        
        if (isVip) {
            log.debug("VIP用户不受次数限制, unionId={}", unionId);
            Object result = point.proceed();
            incrementUserCounts(userId, action);
            return result;
        }

        checkUserLimits(userId, unionId, action, countLimit.countType());
        
        Object result = point.proceed();

        incrementUserCounts(userId, action);
        
        return result;
    }

    private ActionEnum getActionByCountType(String countType) {
        if ("resolve_count".equals(countType)) {
            return ActionEnum.ARTICLE_PARSE;
        } else if ("save_count".equals(countType)) {
            return ActionEnum.ARTICLE_SAVE;
        } else {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "未知的计数类型: " + countType);
        }
    }
    
    private void checkUserLimits(Integer userId, String unionId, ActionEnum action, String countType) {
        int dayCount = cacheManager.getDailyCount(userId, action);
        int monthCount = cacheManager.getMonthlyCount(userId, action);

        int dayMaxCount = notionResourceService.getDayLimit();
        int monthMaxCount = notionResourceService.getMonthLimit();

        if (dayCount >= dayMaxCount) {
            log.warn("每日{}次数超出限制,unionId={}; dayCount={}", countType, unionId, dayCount);
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_DAY_INSUFFICIENT_BALANCE);
        }
        
        if (monthCount >= monthMaxCount) {
            log.warn("每月{}次数超出限制,unionId={}; monthCount={}", countType, unionId, monthCount);
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_MONTH_INSUFFICIENT_BALANCE);
        }
        
        if (dayCount >= dayMaxCount * 0.8) {
            log.info("每日{}次数接近限制,unionId={}; dayCount={}/{}", countType, unionId, dayCount, dayMaxCount);
        }
        
        if (monthCount >= monthMaxCount * 0.8) {
            log.info("每月{}次数接近限制,unionId={}; monthCount={}/{}", countType, unionId, monthCount, monthMaxCount);
        }
    }
    
    private void incrementUserCounts(Integer userId, ActionEnum action) {
        cacheManager.incrementDailyCount(userId, action);
        cacheManager.incrementMonthlyCount(userId, action);
        log.debug("用户使用次数已增加, userId={}, action={}", userId, action);
    }

    private String getUnionId(ProceedingJoinPoint point, MethodSignature signature) {
        Object[] args = point.getArgs();
        String[] parameterNames = signature.getParameterNames();
        
        for (int i = 0; i < parameterNames.length; i++) {
            if ("unionId".equals(parameterNames[i])) {
                return (String) args[i];
            }
        }
        
        return null;
    }
}
