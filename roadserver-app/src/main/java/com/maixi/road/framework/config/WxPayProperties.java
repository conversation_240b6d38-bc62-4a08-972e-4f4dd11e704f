package com.maixi.road.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * 微信支付配置属性类
 * 支持多个小程序使用同一商户号的配置
 *
 * <AUTHOR> Wang
 */
@Data
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayProperties {

  /**
   * 多个支付配置列表
   */
  private List<Config> configs;

  /**
   * 向后兼容：单个配置模式
   * 如果configs为空，则使用这些单独的配置
   */
  private String appId;
  private String mchId;
  private String mchKey;
  private String subAppId;
  private String subMchId;
  private String keyPath;

  @Data
  public static class Config {
    /**
     * 设置微信公众号或者小程序等的appid
     */
    private String appId;

    /**
     * 微信支付商户号
     */
    private String mchId;

    /**
     * 微信支付商户密钥
     */
    private String mchKey;

    /**
     * 服务商模式下的子商户公众账号ID，普通模式请不要配置，请在配置文件中将对应项删除
     */
    private String subAppId;

    /**
     * 服务商模式下的子商户号，普通模式请不要配置，最好是请在配置文件中将对应项删除
     */
    private String subMchId;

    /**
     * apiclient_cert.p12文件的绝对路径，或者如果放在项目中，请以classpath:开头指定
     */
    private String keyPath;
  }
}
