package com.maixi.road.framework.web;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.admin.biz.service.IUserOpenIdService;
import com.maixi.road.admin.manager.LoginTokenGetManager;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.framework.config.RedisManager;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class LoginHandlerInterceptor implements HandlerInterceptor {

    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private IUserOpenIdService userOpenIdService;
    @Resource
    private LoginTokenGetManager loginTokenGetManager;
    @Resource
    private RedisManager redisManager;
    private static final String TRACE_ID = "traceId";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        String unionId = loginTokenGetManager.getLoginUnionId(request);
        if (StringUtils.isBlank(unionId)) {
            log.warn("preHandle, 未获取到 unionId");
            throw RoadException.create(ErrorCodeEnum.USER_NOT_LOGIN, "请重新进入小程序");
        }
        UserInfo userInfo = userInfoService.getUserInfoByUnionId(unionId);
        if (userInfo == null) {
            log.warn("preHandle, 未获取到 userInfo, unionId={}", unionId);
            throw RoadException.create(ErrorCodeEnum.USER_NOT_REGISTER, "请重新进入小程序");
        }

        // 获取当前请求的小程序appId
        String appId = request.getHeader("X-App-Id");
        String openId = userInfo.getOpenId(); // 默认使用原有的openId

        // 如果请求头中包含appId，则查询对应的openId
        if (StringUtils.isNotBlank(appId)) {
            String specificOpenId = userOpenIdService.getOpenIdByUnionIdAndAppId(unionId, appId);
            if (StringUtils.isNotBlank(specificOpenId)) {
                openId = specificOpenId;
                log.debug("使用小程序特定的openId, unionId={}, appId={}, openId={}", unionId, appId, openId);
            } else {
                log.warn("未找到对应小程序的openId, unionId={}, appId={}, 使用默认openId={}", unionId, appId, openId);
            }
        }

        if (StringUtils.isBlank(openId)) {
            log.warn("preHandle, 未获取到 openId, unionId={}, appId={}", unionId, appId);
            throw RoadException.create(ErrorCodeEnum.USER_OPENID_NOT_EXIST, "请重新进入小程序");
        }

        Road.setLoginUser(new LoginUser(userInfo.getUnionId(), userInfo.getId(), openId));
        // 将用户 ID 设置到 MDC 中
        MDC.put(TRACE_ID, unionId);
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        Road.removeLoginUser();
        // 请求完成后清理 MDC
        MDC.remove(TRACE_ID);
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
