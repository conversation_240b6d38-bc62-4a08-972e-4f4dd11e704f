package com.maixi.road.framework.config;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.maixi.road.admin.biz.domain.PropertyRule;
import com.maixi.road.admin.biz.service.IPropertyRuleService;
import com.maixi.road.common.core.constant.CommonConstants;
import com.maixi.road.common.core.model.dto.PropertyRuleDTO;

import jakarta.annotation.PostConstruct;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 通用站点规则管理
 * <p>
 * 在应用启动时从数据库中查询出所有的 property_rule (未删除的，按优先级排序)，
 * 然后以 type 为 key，对应的 PropertyRule 列表为 value，
 * 整体作为一个 Map 异步存入 Redis Hash 中 (使用 RedissonClient RMap)。
 * </p>
 * <AUTHOR>
 */
@Component
public class GeneralSiteRuleManger {

    private static final Logger log = LoggerFactory.getLogger(GeneralSiteRuleManger.class);

    @Autowired
    private IPropertyRuleService propertyRuleService;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 应用启动后执行，异步加载属性规则到 Redis。
     * 注意：此方法现在是异步执行的，不会阻塞应用启动。
     */
    @PostConstruct
    @Async
    public void loadPropertyRulesToRedis() {
        log.info("开始异步加载 PropertyRule 到 Redis RMap (使用 Redisson)...");
        try {
            List<PropertyRule> activeRules = fetchAndSortActivePropertyRules();
            if (CollectionUtils.isEmpty(activeRules)) {
                log.info("数据库中没有符合条件的 PropertyRule 数据 (未删除)，无需加载到 Redis (异步)。");
                return;
            }

            Map<Integer, List<PropertyRule>> rulesByType = groupRulesByType(activeRules);
            if (CollectionUtils.isEmpty(rulesByType)) {
                log.info("PropertyRule 数据中没有有效的 type 进行分组，无需加载到 Redis (异步)。");
                return;
            }

            cacheRulesToRMap(rulesByType);
            log.info("PropertyRule 成功异步加载到 Redis RMap (使用 Redisson)。");

        } catch (Exception e) {
            log.error("异步加载 PropertyRule 到 Redis RMap (Redisson) 过程中发生严重错误。", e);
        }
    }

    /**
     * 从数据库查询未删除的 PropertyRule 并按优先级排序。
     * @return 排序后的 PropertyRule 列表，如果查询不到则返回空列表。
     */
    private List<PropertyRule> fetchAndSortActivePropertyRules() {
        log.debug("开始从数据库查询并排序 PropertyRule (异步)...");
        QueryWrapper<PropertyRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted", 0);
        queryWrapper.orderByAsc("priority");

        List<PropertyRule> rules = propertyRuleService.list(queryWrapper);
        if (CollectionUtils.isEmpty(rules)) {
            log.debug("数据库中未查询到符合条件的 PropertyRule (异步)。");
            return Collections.emptyList();
        }
        log.debug("从数据库查询到 {} 条 PropertyRule (异步)。", rules.size());
        return rules;
    }

    /**
     * 将 PropertyRule 列表按 type 分组。
     * @param activeRules 未删除且已排序的 PropertyRule 列表。
     * @return 按 type 分组的 Map，如果输入为空或无法分组则返回空 Map。
     */
    private Map<Integer, List<PropertyRule>> groupRulesByType(List<PropertyRule> activeRules) {
        if (CollectionUtils.isEmpty(activeRules)) {
            return Collections.emptyMap();
        }
        log.debug("开始将 {} 条 PropertyRule 按 type 分组 (异步)...", activeRules.size());
        Map<Integer, List<PropertyRule>> groupedRules = activeRules.stream()
                .filter(rule -> rule.getType() != null)
                .collect(Collectors.groupingBy(PropertyRule::getType));
        
        if (CollectionUtils.isEmpty(groupedRules)) {
            log.debug("无法按 type 对 PropertyRule 进行有效分组 (异步)。");
            return Collections.emptyMap();
        }
        log.debug("PropertyRule 已成功按 type 分组，共 {} 组 (异步)。", groupedRules.size());
        return groupedRules;
    }

    /**
     * 将按 type 分组的 PropertyRule 整体存入一个 Redis RMap。
     * @param rulesByType 按 type 分组的 PropertyRule Map。
     */
    private void cacheRulesToRMap(Map<Integer, List<PropertyRule>> rulesByType) {
        if (CollectionUtils.isEmpty(rulesByType)) {
            log.warn("没有需要缓存到 Redis RMap 的 PropertyRule 分组数据 (异步)。");
            return;
        }
        log.info("开始将 {} 组 PropertyRule 存入 Redis RMap (Key: {}) (异步)...", rulesByType.size(), CommonConstants.PROPERTY_RULES_RMAP_KEY);
        
        RMap<Integer, List<PropertyRuleDTO>> redisMap = redissonClient.getMap(CommonConstants.PROPERTY_RULES_RMAP_KEY);
        
        try {
            if (redisMap.isExists()) {
                redisMap.delete();
                log.debug("已删除已存在的 Redis RMap: {} (异步)", CommonConstants.PROPERTY_RULES_RMAP_KEY);
            }
            rulesByType.forEach((key, value) -> {
                redisMap.put(key, value.stream().map(this::toPropertyRuleDTO).collect(Collectors.toList()));
            });
            log.info("成功将 {} 个类型的 PropertyRule 批量加载到 Redis RMap (Key: {}) (异步)。", rulesByType.size(), CommonConstants.PROPERTY_RULES_RMAP_KEY);
        } catch (Exception e) {
            log.error("加载 PropertyRule 到 Redis RMap (Key: {}) 时发生错误 (异步)", CommonConstants.PROPERTY_RULES_RMAP_KEY, e);
        }
    }

    private PropertyRuleDTO toPropertyRuleDTO(PropertyRule propertyRule) {
        PropertyRuleDTO propertyRuleDTO = new PropertyRuleDTO();
        propertyRuleDTO.setType(propertyRule.getType());
        propertyRuleDTO.setTypeDesc(propertyRule.getTypeDesc());
        propertyRuleDTO.setPriority(propertyRule.getPriority());
        propertyRuleDTO.setExpression(propertyRule.getExpression());
        return propertyRuleDTO;
    }
}
