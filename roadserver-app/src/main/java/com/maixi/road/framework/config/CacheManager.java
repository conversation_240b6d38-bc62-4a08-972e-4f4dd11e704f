package com.maixi.road.framework.config;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.maixi.road.common.integration.notion.enums.ActionEnum;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CacheManager {

    @Resource
    private RedissonClient redissonClient;

    private static final DateTimeFormatter DAILY_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter MONTHLY_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    // 周期类型常量
    public static final String CYCLE_DAILY = "daily";
    public static final String CYCLE_MONTHLY = "monthly";

    // 基础键前缀
    private static final String USER_KEY_PREFIX = "user";

    /**
     * 增加用户计数
     * 
     * @param userId 用户ID
     * @param action 操作类型 (clip/save)
     * @param cycle  周期类型 (daily/monthly)
     */
    public void incrementCount(Integer userId, ActionEnum action, String cycle) {
        String key = generateKey(action.getCode(), cycle);
        RMap<String, Integer> countMap = redissonClient.getMap(key);
        countMap.merge(userId.toString(), 1, Integer::sum);
        countMap.expire(getExpireDuration(cycle));
    }

    public void setCount(Integer userId, ActionEnum action, String cycle, Integer count) {
        String key = generateKey(action.getCode(), cycle);
        RMap<String, Integer> countMap = redissonClient.getMap(key);
        countMap.put(userId.toString(), count);
        countMap.expire(getExpireDuration(cycle));
    }

    /**
     * 获取用户计数
     * 
     * @param userId 用户ID
     * @param action 操作类型 (clip/save)
     * @param cycle  周期类型 (daily/monthly)
     * @return 计数值
     */
    public int getCount(Integer userId, ActionEnum action, String cycle) {
        String key = generateKey(action.getCode(), cycle);
        RMap<String, Integer> countMap = redissonClient.getMap(key);
        return countMap.getOrDefault(userId.toString(), 0);
    }

    /**
     * 生成缓存键
     * 
     * @param type  操作类型
     * @param cycle 周期类型
     * @return 缓存键
     */
    private String generateKey(String type, String cycle) {
        String dateStr;
        if (CYCLE_DAILY.equals(cycle)) {
            dateStr = LocalDateTime.now().format(DAILY_FORMATTER);
        } else {
            dateStr = LocalDateTime.now().format(MONTHLY_FORMATTER);
        }

        return String.format("%s:%s:%s:%s", USER_KEY_PREFIX, cycle, type, dateStr);
    }

    /**
     * 获取过期时间
     * 
     * @param cycle 周期类型
     * @return 过期时间
     */
    private Duration getExpireDuration(String cycle) {
        LocalDateTime now = LocalDateTime.now();

        if (CYCLE_DAILY.equals(cycle)) {
            LocalDateTime nextDayStart = now.toLocalDate().plusDays(1).atStartOfDay();
            return Duration.between(now, nextDayStart);
        } else if (CYCLE_MONTHLY.equals(cycle)) {
            YearMonth currentMonth = YearMonth.from(now);
            LocalDateTime nextMonthStart = currentMonth.plusMonths(1).atDay(1).atStartOfDay();
            return Duration.between(now, nextMonthStart);
        }

        return null;
    }

    // 以下是为了保持向后兼容的方法

    /**
     * 增加用户剪藏次数
     */
    public void incrementMonthlyClipCount(Integer userId) {
        incrementCount(userId, ActionEnum.ARTICLE_PARSE, CYCLE_MONTHLY);
    }

    /**
     * 增加用户保存次数
     */
    public void incrementMonthlySaveCount(Integer userId) {
        incrementCount(userId, ActionEnum.ARTICLE_SAVE, CYCLE_MONTHLY);
    }

    /**
     * 增加用户当月计数
     */
    public void incrementMonthlyCount(Integer userId, ActionEnum action) {
        incrementCount(userId, action, CYCLE_MONTHLY);
    }

    /**
     * 获取用户当月剪藏次数
     */
    public int getMonthlyClipCount(Integer userId) {
        return getCount(userId, ActionEnum.ARTICLE_PARSE, CYCLE_MONTHLY);
    }

    /**
     * 获取用户当月保存次数
     */
    public int getMonthlySaveCount(Integer userId) {
        return getCount(userId, ActionEnum.ARTICLE_SAVE, CYCLE_MONTHLY);
    }

    /**
     * 获取用户当月计数
     */
    public int getMonthlyCount(Integer userId, ActionEnum action) {
        return getCount(userId, action, CYCLE_MONTHLY);
    }

    /**
     * 增加用户每日剪藏次数
     */
    public void incrementDailyClipCount(Integer userId) {
        incrementCount(userId, ActionEnum.ARTICLE_PARSE, CYCLE_DAILY);
    }

    /**
     * 增加用户每日剪藏次数
     */
    public void incrementDailyCount(Integer userId, ActionEnum action) {
        incrementCount(userId, action, CYCLE_DAILY);
    }

    /**
     * 增加用户每日保存次数
     */
    public void incrementDailySaveCount(Integer userId) {
        incrementCount(userId, ActionEnum.ARTICLE_SAVE, CYCLE_DAILY);
    }

    /**
     * 获取用户当日剪藏次数
     */
    public int getDailyCount(Integer userId, ActionEnum action) {
        return getCount(userId, action, CYCLE_DAILY);
    }

    /**
     * 获取用户当日剪藏次数
     */
    public int getDailyClipCount(Integer userId) {
        return getCount(userId, ActionEnum.ARTICLE_PARSE, CYCLE_DAILY);
    }

    /**
     * 获取用户当日保存次数
     */
    public int getDailySaveCount(Integer userId) {
        return getCount(userId, ActionEnum.ARTICLE_SAVE, CYCLE_DAILY);
    }
}
