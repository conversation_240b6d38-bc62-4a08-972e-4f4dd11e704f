package com.maixi.road.framework.web;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.annotation.Resource;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private LoginHandlerInterceptor loginHandlerInterceptor;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册拦截器，并指定拦截路径
        registry.addInterceptor(loginHandlerInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                        "/notion/authorized",                // Notion授权入口
                        "/miniprogram/image/avatar/upload",              // 头像上传接口
                        "/notion/auth/callback",             // Notion授权回调
                        "/notion/termsofuse",                // 使用条款页面
                        "/notion/privacypolicy",             // 隐私政策页面
                        "/notion/success",                   // 授权成功页面
                        "/notion/frequencyLimit",            // 频率限制提示页面
                        "/notion/codeInvalided",             // 授权码失效页面
                        "/miniprogram/freehelper/login",     // 小程序免费助手登录
                        "/miniprogram/obclipper/login", // 小程序obclipper登录
                        "/miniprogram/login",                // 小程序登录接口
                        "/miniprogram/notify/order",         // 小程序订单通知
                        "/wx/cp/portal/**",                  // 企业微信门户接口
                        "/job/**",                          // 定时任务相关接口
                        "/test/**",                         // 测试接口
                        "/icons/icon_zh_48.png",             // 图标
                        "/favicon.ico");                    // favicon.ico
    }
}
