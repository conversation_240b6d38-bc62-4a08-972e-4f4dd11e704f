package com.maixi.road.framework.aspectj;

import com.maixi.road.admin.manager.LoginTokenGetManager;
import com.maixi.road.admin.manager.UserManager;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.framework.annotation.DistributedLock;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.concurrent.TimeUnit;

@Slf4j
@Aspect
@Component
public class DistributedLockAspect {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserManager userManager;
    @Resource
    private LoginTokenGetManager loginTokenGetManager;

    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint point, DistributedLock distributedLock) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String mainUnionId = userManager.getMainUnionId(loginTokenGetManager.getLoginUnionId(request));
        
        String methodName = point.getSignature().getName();
        String lockKey = "rLock:" + methodName + ":" + mainUnionId;
        RLock lock = redissonClient.getLock(lockKey);
        if (lock == null) {
            throw RoadException.create(ErrorCodeEnum.USER_OPERATION_FREQUENCY);
        }

        try {
            boolean getLock = lock.tryLock(distributedLock.waitTime(), distributedLock.lockTime(), TimeUnit.SECONDS);
            if (getLock) {
                log.info("[分布式锁加锁]: key:{}", lockKey);
                return point.proceed();
            }
            log.info("[用户操作过于频繁]: key:{}", lockKey);
            throw RoadException.create(ErrorCodeEnum.USER_OPERATION_FREQUENCY);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("[用户操作获取锁失败]: key:{}", lockKey);
            throw RoadException.create(ErrorCodeEnum.USER_OPERATION_FREQUENCY);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                log.info("[分布式锁释放]: key:{}", lockKey);
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("释放锁失败,msg={}", e.getMessage());
                }
            }
        }
    }
}
