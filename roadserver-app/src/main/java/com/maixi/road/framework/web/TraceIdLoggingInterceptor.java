package com.maixi.road.framework.web;

import com.maixi.road.admin.manager.LoginTokenGetManager;
import com.maixi.road.admin.manager.UserManager;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Slf4j
@Component
public class TraceIdLoggingInterceptor implements HandlerInterceptor {

    private static final String TRACE_ID = "traceId";

    @Resource
    private UserManager userManager;
    @Resource
    private LoginTokenGetManager loginTokenGetManager;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 从请求中获取用户 ID（假设用户 ID 存储在请求头或属性中）
        String unionId = "unknown";
        try {
            unionId = loginTokenGetManager.getLoginUnionId(request);
        } catch (Exception e) {
            // 不打印日志
        }

        // 将用户 ID 设置到 MDC 中
        MDC.put(TRACE_ID, unionId);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        // 请求完成后清理 MDC
        MDC.remove(TRACE_ID);
    }

}
