package com.maixi.road.framework.web;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MainUserWrapper {

    private String unionId;
    private Integer userId;
    private String mainUnionId;
    private Integer mainUserId;
    private boolean isMainUser;

    public String getMainUnionId(){
        if(isMainUser){
            return unionId;
        }
        return mainUnionId;
    }

    public Integer getMainUserId(){
        if(isMainUser){
            return userId;
        }
        return mainUserId;
    }
}
