package com.maixi.road.framework.config;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;

import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.common.service.redis.constant.RedisKeys;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Redis管理类，负责处理与Redis相关的操作
 * 主要用于用户统计数据的缓存管理
 */
@Component
@Slf4j
public class RedisManager {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IUserInfoService userInfoService;


    /**
     * 获取用户的文本同步总数
     * 优先从Redis缓存获取，如果缓存不存在则从数据库获取
     *
     * @param unionId 用户唯一标识
     * @return 用户文本同步总数
     */
    public Integer getTextSumByUnionId(String unionId) {
        return Optional.ofNullable(redissonClient.<Integer>getBucket(RedisKeys.getSumTextKey(unionId)).get())
                .orElseGet(() -> {
                    UserInfo userInfo = userInfoService.getUserInfoByUnionId(unionId);
                    Integer sum = Optional.ofNullable(userInfo.getWxMsgSum()).orElse(0);
                    log.info("从数据库获取用户同步信息次数:{}", sum);
                    return sum;
                });
    }

    /**
     * 获取用户的文章同步总数
     * 优先从Redis缓存获取，如果缓存不存在则从数据库获取
     *
     * @param unionId 用户唯一标识
     * @return 用户文章同步总数
     */
    public Integer getArticleSumByUnionId(String unionId) {
        return Optional.ofNullable(redissonClient.<Integer>getBucket(RedisKeys.getSumArticleKey(unionId)).get())
                .orElseGet(() -> {
                    UserInfo userInfo = userInfoService.getUserInfoByUnionId(unionId);
                    Integer sum = Optional.ofNullable(userInfo.getWxArticleSum()).orElse(0);
                    log.info("从数据库获取用户同步文章次数:{}", sum);
                    return sum;
                });
    }

    /**
     * 仅从缓存获取用户的文本同步总数
     * 如果缓存不存在则返回0
     *
     * @param unionId 用户唯一标识
     * @return 用户文本同步总数，缓存不存在时返回0
     */
    public Integer getTextSumByUnionIdFromCache(String unionId) {
        return Optional.ofNullable(redissonClient.<Integer>getBucket(RedisKeys.getSumTextKey(unionId)).get()).orElse(0);
    }

    /**
     * 仅从缓存获取用户的文章同步总数
     * 如果缓存不存在则返回0
     *
     * @param unionId 用户唯一标识
     * @return 用户文章同步总数，缓存不存在时返回0
     */
    public Integer getArticleSumByUnionIdFromCache(String unionId) {
        return Optional.ofNullable(redissonClient.<Integer>getBucket(RedisKeys.getSumArticleKey(unionId)).get())
                .orElse(0);
    }

    /**
     * 增加用户文本同步总数计数（+1）
     * 并设置2天的过期时间
     *
     * @param unionId 用户唯一标识
     */
    public void textSumIncrementOne(String unionId) {
        String key = RedisKeys.getSumTextKey(unionId);
        int value = getTextSumByUnionId(unionId) + 1;
        redissonClient.getBucket(key).set(value, Duration.ofDays(2));
    }

    /**
     * 增加用户文章同步总数计数（+1）
     * 并设置2天的过期时间
     *
     * @param unionId 用户唯一标识
     */
    public void articleSumIncrementOne(String unionId) {
        String key = RedisKeys.getSumArticleKey(unionId);
        int value = getArticleSumByUnionId(unionId) + 1;
        redissonClient.getBucket(key).set(value, Duration.ofDays(2));
    }

    /**
     * 清除用户文本同步总数的缓存
     *
     * @param unionId 用户唯一标识
     */
    public void clearTextSumByUnionId(String unionId) {
        redissonClient.getBucket(RedisKeys.getSumTextKey(unionId)).delete();
    }

    /**
     * 清除用户文章同步总数的缓存
     *
     * @param unionId 用户唯一标识
     */
    public void clearArticleSumByUnionId(String unionId) {
        redissonClient.getBucket(RedisKeys.getSumArticleKey(unionId)).delete();
    }

    /**
     * 清除用户所有相关的缓存数据
     * 包括文章同步总数、文本同步总数和分享总数
     *
     * @param unionId 用户唯一标识
     */
    public void removeAllCacheByUnionId(String unionId) {
        redissonClient.getBucket(RedisKeys.getSumArticleKey(unionId)).delete();
        redissonClient.getBucket(RedisKeys.getSumTextKey(unionId)).delete();
        redissonClient.getBucket(RedisKeys.getSumShareKey(unionId)).delete();
    }

    // =============================================================

    /**
     * 检查用户图片转换处理状态
     * 返回true表示图片正在处理中
     *
     * @param unionId 用户唯一标识
     * @return 如果返回true表示图片正在处理中，false表示没有处理或处理完成
     */
    public boolean transformPictureProcessing(String unionId) {
        Integer finishedFlag = redissonClient.<Integer>getBucket(RedisKeys.getPicTransFlagKey(unionId)).get();
        // 0 说明还在处理中
        return Objects.equals(finishedFlag, 0);
    }

    /**
     * 初始化用户图片转换处理状态
     * 设置处理中标志，并设置1分钟的过期时间
     *
     * @param unionId 用户唯一标识
     */
    public void transformPictureInitSet(String unionId) {
        redissonClient.<Integer>getBucket(RedisKeys.getPicTransFlagKey(unionId)).set(0, Duration.ofMinutes(1));
    }

    /**
     * 结束用户图片转换处理
     * 删除处理中标志
     *
     * @param unionId 用户唯一标识
     */
    public void transformPictureEndSet(String unionId) {
        redissonClient.<Integer>getBucket(RedisKeys.getPicTransFlagKey(unionId)).delete();
    }

}
