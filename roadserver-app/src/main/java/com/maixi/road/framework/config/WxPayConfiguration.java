package com.maixi.road.framework.config;

import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微信支付配置类
 * 支持多个小程序使用同一商户号的支付服务
 * 
 * <AUTHOR> Wang
 */
@Slf4j
@Configuration
@ConditionalOnClass(WxPayService.class)
@EnableConfigurationProperties(WxPayProperties.class)
@AllArgsConstructor
public class WxPayConfiguration {
  private WxPayProperties properties;

  /**
   * 微信支付服务管理器
   * 管理多个小程序的支付服务实例
   */
  @Bean
  @ConditionalOnMissingBean
  public WxPayServiceManager wxPayServiceManager() {
    log.info("注册微信支付服务管理器");

    // 如果配置了多个支付配置，使用多配置模式
    if (properties.getConfigs() != null && !properties.getConfigs().isEmpty()) {
      log.info("使用多配置模式，配置数量: {}", properties.getConfigs().size());

      Map<String, WxPayService> payServices = properties.getConfigs().stream()
          .collect(Collectors.toMap(
              WxPayProperties.Config::getAppId,
              this::createWxPayService,
              (existing, replacement) -> existing));

      return new WxPayServiceManager(payServices);
    } else {
      // 向后兼容：单配置模式
      log.info("使用单配置模式（向后兼容）");
      WxPayService wxPayService = createWxPayService(properties);
      return new WxPayServiceManager(Map.of(properties.getAppId(), wxPayService));
    }
  }

  /**
   * 向后兼容：提供单个WxPayService Bean
   * 默认返回第一个配置的服务或单配置模式的服务
   */
  @Bean
  @ConditionalOnMissingBean(name = "wxService")
  public WxPayService wxService() {
    log.info("注册默认微信支付服务（向后兼容）");
    WxPayServiceManager manager = wxPayServiceManager();
    return manager.getDefaultService();
  }

  /**
   * 根据配置创建WxPayService实例
   */
  private WxPayService createWxPayService(WxPayProperties.Config config) {
    log.info("创建微信支付服务，appId: {}", config.getAppId());

    WxPayConfig payConfig = new WxPayConfig();
    payConfig.setAppId(StringUtils.trimToNull(config.getAppId()));
    payConfig.setMchId(StringUtils.trimToNull(config.getMchId()));
    payConfig.setMchKey(StringUtils.trimToNull(config.getMchKey()));
    payConfig.setSubAppId(StringUtils.trimToNull(config.getSubAppId()));
    payConfig.setSubMchId(StringUtils.trimToNull(config.getSubMchId()));
    payConfig.setKeyPath(StringUtils.trimToNull(config.getKeyPath()));

    // 可以指定是否使用沙箱环境
    payConfig.setUseSandboxEnv(false);

    WxPayService wxPayService = new WxPayServiceImpl();
    wxPayService.setConfig(payConfig);
    return wxPayService;
  }

  /**
   * 向后兼容：根据单配置属性创建WxPayService实例
   */
  private WxPayService createWxPayService(WxPayProperties properties) {
    log.info("创建微信支付服务（单配置模式），appId: {}", properties.getAppId());

    WxPayConfig payConfig = new WxPayConfig();
    payConfig.setAppId(StringUtils.trimToNull(properties.getAppId()));
    payConfig.setMchId(StringUtils.trimToNull(properties.getMchId()));
    payConfig.setMchKey(StringUtils.trimToNull(properties.getMchKey()));
    payConfig.setSubAppId(StringUtils.trimToNull(properties.getSubAppId()));
    payConfig.setSubMchId(StringUtils.trimToNull(properties.getSubMchId()));
    payConfig.setKeyPath(StringUtils.trimToNull(properties.getKeyPath()));

    // 可以指定是否使用沙箱环境
    payConfig.setUseSandboxEnv(false);

    WxPayService wxPayService = new WxPayServiceImpl();
    wxPayService.setConfig(payConfig);
    return wxPayService;
  }

  /**
   * 微信支付服务管理器
   * 用于管理多个小程序的支付服务
   */
  public static class WxPayServiceManager {
    private final Map<String, WxPayService> payServices;

    public WxPayServiceManager(Map<String, WxPayService> payServices) {
      this.payServices = payServices;
    }

    /**
     * 根据appId获取对应的支付服务
     */
    public WxPayService getPayService(String appId) {
      WxPayService service = payServices.get(appId);
      if (service == null) {
        log.warn("未找到appId={}对应的支付服务，使用默认服务", appId);
        return getDefaultService();
      }
      return service;
    }

    /**
     * 获取默认支付服务（第一个配置的服务）
     */
    public WxPayService getDefaultService() {
      return payServices.values().iterator().next();
    }

    /**
     * 获取所有支付服务
     */
    public Map<String, WxPayService> getAllServices() {
      return payServices;
    }

    /**
     * 检查是否包含指定appId的服务
     */
    public boolean hasService(String appId) {
      return payServices.containsKey(appId);
    }
  }
}
