package com.maixi.road.framework.config;

import java.net.InetSocketAddress;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.config.Config;
import org.redisson.connection.ConnectionListener;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ClassUtils;

import io.netty.channel.nio.NioEventLoopGroup;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.data.redis")
public class RedissonConfig {
    private String address;
    private String host;
    private int port;
    private int connectionMinimumIdleSize = 10;
    private int idleConnectionTimeout = 10000;
    private int pingTimeout = 1000;
    private int connectTimeout = 10000;
    private int timeout = 3000;
    private int retryAttempts = 3;
    private int retryInterval = 1500;
    private int reconnectionTimeout = 3000;
    private int failedAttempts = 3;
    private String username;
    private String password;
    private int subscriptionsPerConnection = 5;
    private String clientName;
    private int subscriptionConnectionMinimumIdleSize = 1;
    private int subscriptionConnectionPoolSize = 50;
    private int connectionPoolSize = 64;
    private int database = 0;
    private boolean dnsMonitoring = true;
    private int dnsMonitoringInterval = 10000;
    /**
     * 当前处理核数量 * 2
     */
    private int thread;

    private String codec = "org.redisson.codec.JsonJacksonCodec";

    @Bean(value = "redissonClient")
    RedissonClient redissonClient() throws Exception {
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://" + host + ":" + port)
                .setConnectionMinimumIdleSize(connectionMinimumIdleSize)
                .setConnectionPoolSize(connectionPoolSize)
                .setDatabase(database)
                .setDnsMonitoringInterval(dnsMonitoringInterval)
                .setSubscriptionConnectionMinimumIdleSize(subscriptionConnectionMinimumIdleSize)
                .setSubscriptionConnectionPoolSize(subscriptionConnectionPoolSize)
                .setSubscriptionsPerConnection(subscriptionsPerConnection)
                .setRetryAttempts(retryAttempts)
                .setRetryInterval(retryInterval)
                .setTimeout(timeout)
                .setConnectTimeout(connectTimeout)
                .setIdleConnectionTimeout(idleConnectionTimeout)
                .setPingConnectionInterval(10000)
                .setUsername(username)
                .setPassword(password);
        Codec codec = (Codec) ClassUtils.forName(getCodec(), ClassUtils.getDefaultClassLoader()).getDeclaredConstructor().newInstance();
        // Codec codec = (Codec) redissonCodec(objectMapper());
        config.setCodec(codec);
        config.setThreads(thread);
        config.setEventLoopGroup(new NioEventLoopGroup());
        config.setConnectionListener(new ConnectionListener(){

            @Override
            public void onConnect(InetSocketAddress addr) {
                log.info("redis onConnect: {}", addr);
            }

            @Override
            public void onDisconnect(InetSocketAddress addr) {
                log.info("redis onDisconnect: {}", addr);
            }
        });
        return Redisson.create(config);
    }

    // @Bean
    // public JsonJacksonCodec redissonCodec(ObjectMapper objectMapper) {
    //     // Spring Boot 通常会自动配置 ObjectMapper 并注册 JavaTimeModule
    //     // 但为了确保 Redisson 使用的是带有此模块的 ObjectMapper，我们可以在这里再次确认或定制
    //     // 如果 Spring Boot 的 ObjectMapper 已经配置好了 JavaTimeModule，
    //     // 这里的 objectMapper 参数就会是那个配置好的实例。
    //     // 如果你想完全控制，可以 new 一个 ObjectMapper 并注册模块：
    //     // ObjectMapper newMapper = new ObjectMapper();
    //     // newMapper.registerModule(new JavaTimeModule());
    //     // return new JsonJacksonCodec(newMapper);

    //     // 推荐方式：依赖 Spring Boot 自动配置的 ObjectMapper，它应该已经包含了 JavaTimeModule
    //     // 如果 objectMapper 尚未注册 JavaTimeModule（不太可能在正确添加依赖后发生），
    //     // 你可以这样做来确保：
    //     if (!objectMapper.getRegisteredModuleIds().contains(JavaTimeModule.class.getName())) {
    //          objectMapper.registerModule(new JavaTimeModule());
    //     }
    //     return new JsonJacksonCodec(objectMapper);
    // }

    // @Bean
    // public ObjectMapper objectMapper() {
    //     return new ObjectMapper();
    // }
}
