package com.maixi.road.framework.config;

import cn.hutool.core.io.FileUtil;
import com.google.common.collect.Maps;
import com.maixi.road.common.core.constant.CommonConstants;
import com.maixi.road.wechat.handler.CpLogHandler;
import com.maixi.road.wechat.handler.CpMsgHandler;
import com.maixi.road.wechat.handler.CpSubscribeHandler;
import com.maixi.road.wechat.handler.CpUnsubscribeHandler;
import com.tencent.wework.Finance;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import me.chanjar.weixin.cp.message.WxCpMessageRouter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 单实例配置
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(WxCpProperties.class)
public class WxCpConfiguration {
    private CpLogHandler cpLogHandler;
    private CpMsgHandler cpMsgHandler;
    private CpUnsubscribeHandler cpUnsubscribeHandler;
    private CpSubscribeHandler cpSubscribeHandler;

    private WxCpProperties properties;

    private static Map<Integer, WxCpMessageRouter> routers = Maps.newHashMap();
    private static Map<Integer, WxCpService> cpServices = Maps.newHashMap();

    @Autowired
    public WxCpConfiguration(CpLogHandler cpLogHandler,
                             CpMsgHandler cpMsgHandler,
                             CpUnsubscribeHandler cpUnsubscribeHandler,
                             CpSubscribeHandler cpSubscribeHandler,
                             WxCpProperties properties) {
        this.cpLogHandler = cpLogHandler;
        this.cpMsgHandler = cpMsgHandler;
        this.cpUnsubscribeHandler = cpUnsubscribeHandler;
        this.cpSubscribeHandler = cpSubscribeHandler;
        this.properties = properties;
    }


    public static Map<Integer, WxCpMessageRouter> getRouters() {
        return routers;
    }

    public static WxCpService getCpService(Integer agentId) {
        return cpServices.get(agentId);
    }

    @PostConstruct
    public void initServices() {
        log.info("注册企业微信服务");
        cpServices = this.properties.getAppConfigs().stream().map(a -> {
            val configStorage = new WxCpDefaultConfigImpl();
            configStorage.setCorpId(this.properties.getCorpId());
            configStorage.setAgentId(a.getAgentId());
            configStorage.setCorpSecret(a.getSecret());
            configStorage.setToken(a.getToken());
            configStorage.setAesKey(a.getAesKey());
            configStorage.setMsgAuditPriKey(CommonConstants.priKey);
            configStorage.setMsgAuditSecret("WENcyFYJKvrhnnBddA4hYeAjep6G9d5v26DQ7Ekq2XQ");
            if (Finance.isWindows()){
                configStorage.setMsgAuditLibPath(FileUtil.getAbsolutePath("lib\\WeWorkFinanceSdk.dll"));
            }else {
                configStorage.setMsgAuditLibPath("/home/<USER>/libWeWorkFinanceSdk_Java.so");
                log.info("lib path: " + configStorage.getMsgAuditLibPath());
            }
            val service = new WxCpServiceImpl();
            service.setWxCpConfigStorage(configStorage);
            routers.put(a.getAgentId(), this.newRouter(service));
            return service;
        }).collect(Collectors.toMap(service -> service.getWxCpConfigStorage().getAgentId(), a -> a));
    }

    private WxCpMessageRouter newRouter(WxCpService wxCpService) {
        final val newRouter = new WxCpMessageRouter(wxCpService);

        // 记录所有事件的日志 （异步执行）
        newRouter.rule().handler(this.cpLogHandler).next();

        // 关注事件
        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxConsts.EventType.SUBSCRIBE).handler(this.cpSubscribeHandler)
                .end();

        // 取消关注事件
        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
                .event(WxConsts.EventType.UNSUBSCRIBE)
                .handler(this.cpUnsubscribeHandler).end();

        // 默认
        newRouter.rule().async(false).handler(this.cpMsgHandler).end();

        return newRouter;
    }

}
