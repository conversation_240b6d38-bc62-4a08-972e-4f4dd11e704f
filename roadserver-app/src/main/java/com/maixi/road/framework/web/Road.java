package com.maixi.road.framework.web;

public class Road {

    private static final ThreadLocal<LoginUser> loginUserThreadLocal = new ThreadLocal<>();

    public static void setLoginUser(LoginUser loginUser) {
        loginUserThreadLocal.set(loginUser);
    }

    public static LoginUser getLoginUser() {
        return loginUserThreadLocal.get();
    }

    public static void removeLoginUser() {
        loginUserThreadLocal.remove();
    }
}
