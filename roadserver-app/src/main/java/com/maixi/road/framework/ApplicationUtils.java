package com.maixi.road.framework;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * Spring应用上下文工具类
 * <p>
 * 提供静态方法来获取Spring管理的Bean实例，主要用于在非Spring管理的类中获取Bean
 * </p>
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApplicationUtils implements ApplicationContextAware {

    /**
     * Spring应用上下文对象
     */
    private static ApplicationContext applicationContext;

    /**
     * 实现ApplicationContextAware接口的回调方法，设置上下文对象
     * 
     * @param applicationContext Spring应用上下文
     * @throws BeansException Bean异常
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationUtils.applicationContext = applicationContext;
        log.info("ApplicationUtils初始化完成，Spring上下文已注入");
    }

    /**
     * 获取ApplicationContext对象
     * 
     * @return ApplicationContext对象
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 根据Bean名称获取Bean实例
     * 
     * @param name Bean名称
     * @return Bean实例
     * @throws RuntimeException 当Bean不存在时抛出异常
     */
    public static Object getBean(String name) {
        checkApplicationContext();
        try {
            return applicationContext.getBean(name);
        } catch (BeansException e) {
            log.error("根据Bean名称获取Bean失败, name={}", name, e);
            throw new RuntimeException("获取Bean失败: " + name, e);
        }
    }

    /**
     * 根据Bean类型获取Bean实例
     * 
     * @param <T>   Bean类型
     * @param clazz Bean类型Class对象
     * @return Bean实例
     * @throws RuntimeException 当Bean不存在时抛出异常
     */
    public static <T> T getBean(Class<T> clazz) {
        checkApplicationContext();
        try {
            return applicationContext.getBean(clazz);
        } catch (BeansException e) {
            log.error("根据Bean类型获取Bean失败, class={}", clazz.getName(), e);
            throw new RuntimeException("获取Bean失败: " + clazz.getName(), e);
        }
    }

    /**
     * 根据Bean名称和类型获取Bean实例
     * 
     * @param <T>   Bean类型
     * @param name  Bean名称
     * @param clazz Bean类型Class对象
     * @return Bean实例
     * @throws RuntimeException 当Bean不存在时抛出异常
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        checkApplicationContext();
        try {
            return applicationContext.getBean(name, clazz);
        } catch (BeansException e) {
            log.error("根据Bean名称和类型获取Bean失败, name={}, class={}", name, clazz.getName(), e);
            throw new RuntimeException("获取Bean失败: " + name + ", " + clazz.getName(), e);
        }
    }

    /**
     * 判断是否包含指定名称的Bean
     * 
     * @param name Bean名称
     * @return 是否包含该Bean
     */
    public static boolean containsBean(String name) {
        checkApplicationContext();
        return applicationContext.containsBean(name);
    }

    /**
     * 判断指定名称的Bean是否为单例
     * 
     * @param name Bean名称
     * @return 是否为单例
     */
    public static boolean isSingleton(String name) {
        checkApplicationContext();
        return applicationContext.isSingleton(name);
    }

    /**
     * 获取指定Bean的类型
     * 
     * @param name Bean名称
     * @return Bean类型
     */
    public static Class<?> getType(String name) {
        checkApplicationContext();
        return applicationContext.getType(name);
    }

    /**
     * 根据Bean类型获取Bean实例（可选方式，不存在时返回null）
     * 
     * @param <T>   Bean类型
     * @param clazz Bean类型Class对象
     * @return Bean实例，不存在时返回null
     */
    public static <T> T getBeanOrNull(Class<T> clazz) {
        checkApplicationContext();
        try {
            return applicationContext.getBean(clazz);
        } catch (BeansException e) {
            log.debug("Bean不存在: {}", clazz.getName());
            return null;
        }
    }

    /**
     * 根据Bean名称获取Bean实例（可选方式，不存在时返回null）
     * 
     * @param name Bean名称
     * @return Bean实例，不存在时返回null
     */
    public static Object getBeanOrNull(String name) {
        checkApplicationContext();
        try {
            return applicationContext.getBean(name);
        } catch (BeansException e) {
            log.debug("Bean不存在: {}", name);
            return null;
        }
    }

    /**
     * 检查ApplicationContext是否已初始化
     * 
     * @throws RuntimeException 当ApplicationContext未初始化时抛出异常
     */
    private static void checkApplicationContext() {
        if (applicationContext == null) {
            throw new RuntimeException("ApplicationContext未初始化，请确保ApplicationUtils已被Spring管理");
        }
    }

    /**
     * 获取当前激活的Profile
     * 
     * @return 激活的Profile数组
     */
    public static String[] getActiveProfiles() {
        checkApplicationContext();
        return applicationContext.getEnvironment().getActiveProfiles();
    }

    /**
     * 获取应用名称
     * 
     * @return 应用名称
     */
    public static String getApplicationName() {
        checkApplicationContext();
        return applicationContext.getApplicationName();
    }
}
