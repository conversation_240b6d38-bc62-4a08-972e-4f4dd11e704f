package com.maixi.road.framework;


import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.*;
import com.maixi.road.common.core.model.dto.Result;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


/**
 * 全局异常处理器
 *
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 业务异常
     */
    @ExceptionHandler(ClipperException.class)
    public Result<Object> handleServiceException(ClipperException e, HttpServletRequest request) {
        String requestURI = request.getRequestURL().toString();
        log.error("请求地址'{}',发生异常.| [ClipperException]: code={}, msg={}", requestURI, e.getCode(), e.getMsg(), e);
        return Result.fail(e.getCode(), e.getMsg());
    }

    @ExceptionHandler(ImageCloudException.class)
    public Result<Object> handleServiceException(ImageCloudException e, HttpServletRequest request) {
        String requestURI = request.getRequestURL().toString();
        log.error("请求地址'{}',发生异常.| [ImageCloudException]: code={}, msg={}", requestURI, e.getCode(), e.getMsg(), e);
        return Result.fail(e.getCode(), e.getMsg());
    }

    @ExceptionHandler(NotionException.class)
    public Result<Object> handleServiceException(NotionException e, HttpServletRequest request) {
        String requestURI = request.getRequestURL().toString();
        log.error("请求地址'{}',发生异常.| [NotionException]: code={}, msg={}", requestURI, e.getCode(), e.getMsg(), e);
        return Result.fail(e.getCode(), e.getMsg());
    }

    @ExceptionHandler(OrderException.class)
    public Result<Object> handleServiceException(OrderException e, HttpServletRequest request) {
        String requestURI = request.getRequestURL().toString();
        log.error("请求地址'{}',发生异常.| [OrderException]: code={}, msg={}", requestURI, e.getCode(), e.getMessage(), e);
        return Result.fail(e.getCode(), e.getMsg());
    }

    @ExceptionHandler(ParamException.class)
    public Result<Object> handleServiceException(ParamException e, HttpServletRequest request) {
        String requestURI = request.getRequestURL().toString();
        log.error("请求地址'{}',发生异常.| [ParamException]: code={}, msg={}", requestURI, e.getCode(), e.getMsg(), e);
        return Result.fail(e.getCode(), e.getMsg());
    }

    @ExceptionHandler(RoadException.class)
    public Result<Object> handleServiceException(RoadException e, HttpServletRequest request) {
        String requestURI = request.getRequestURL().toString();
        log.error("请求地址'{}',发生异常.| [RoadException]: code={}, msg={}", requestURI, e.getCode(), e.getMsg());
        return Result.fail(e.getCode(), e.getMsg());
    }


    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Result<Object> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生异常.| [RuntimeException]: msg={}", requestURI, e.getMessage(), e);
        return Result.fail(ErrorCodeEnum.DEFAULT_ERROR.getCode(), "系统开小差了");
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public Result<Object> handleException(Exception e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生异常.| [Exception]: msg={}", requestURI, e.getMessage(), e);
        return Result.fail(ErrorCodeEnum.DEFAULT_ERROR.getCode(), "系统开小差了");
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public Result<Object> handleBindException(BindException e) {
        log.error(e.getMessage(), e);
        String message = e.getAllErrors().getFirst().getDefaultMessage();
        return Result.fail(message);
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result<Object> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生异常.| [HttpRequestMethodNotSupportedException]: msg={}", requestURI, e.getMessage(), e);
        return Result.fail(ErrorCodeEnum.DEFAULT_ERROR.getCode(), e.getMessage());
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String message;
        if (e.getBindingResult().hasFieldErrors()) {
            FieldError fieldError = e.getBindingResult().getFieldError();
            if (fieldError != null) {
                message = fieldError.getDefaultMessage();
            } else {
                message = "参数校验失败";
            }
        } else {
            message = "请求参数无效";
        }
        log.error("请求地址'{}',发生异常.| [MethodArgumentNotValidException]: msg={}", requestURI, message, e);
        return Result.fail(message);
    }
}
