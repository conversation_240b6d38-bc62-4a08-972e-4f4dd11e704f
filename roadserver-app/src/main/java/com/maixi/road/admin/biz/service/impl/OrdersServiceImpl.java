package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.OrdersMapper;
import com.maixi.road.admin.biz.domain.Orders;
import com.maixi.road.admin.biz.service.IOrdersService;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class OrdersServiceImpl extends ServiceImpl<OrdersMapper, Orders> implements IOrdersService {

    @Override
    public Orders getByOrderNo(String outTradeNo) {
        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Orders::getOrderNo, outTradeNo).eq(Orders::getDeleted, 0);
        return getOne(queryWrapper);
    }

    @Override
    public void updateStatusByOrderNo(Integer status, LocalDateTime payTime, String orderNo) {
        LambdaUpdateChainWrapper<Orders> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        lambdaUpdateChainWrapper.set(Orders::getStatus, status).set(Orders::getPayTime, payTime).eq(Orders::getOrderNo, orderNo).update();
    }
}
