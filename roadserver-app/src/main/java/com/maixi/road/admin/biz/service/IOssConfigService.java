package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.OssConfig;

import java.util.Optional;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IOssConfigService extends IService<OssConfig> {

    Optional<OssConfig> getOssConfigOptByUnionId(String unionId);

    OssConfig getOssConfigByUnionId(String unionId);

    void updateRegionAndEndpoint(Integer id, String region, String endpoint);

    void deleteByUnionId(String mainUnionId);
}
