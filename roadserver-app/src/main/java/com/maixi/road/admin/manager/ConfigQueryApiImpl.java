package com.maixi.road.admin.manager;

import java.util.Optional;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.maixi.road.admin.biz.domain.CustomConfig;
import com.maixi.road.admin.biz.domain.ObsidianConfig;
import com.maixi.road.admin.biz.service.ICustomConfigService;
import com.maixi.road.admin.biz.service.INotionResourceService;
import com.maixi.road.admin.biz.service.IObsidianConfigService;
import com.maixi.road.admin.biz.service.IUserArticleService;
import com.maixi.road.admin.biz.service.IUserMessageService;
import com.maixi.road.common.core.model.dto.ArticleFieldDTO;
import com.maixi.road.common.core.model.dto.GloablConfig;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.core.model.dto.ObsidianArticleProperty;
import com.maixi.road.common.core.model.dto.ObsidianDTO;
import com.maixi.road.common.core.model.dto.ObsidianMessageProperty;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ConfigQueryApiImpl implements ConfigQueryApi {

    @Resource
    private ICustomConfigService customConfigService;
    @Resource
    private INotionResourceService notionResourceService;
    @Resource
    private S3Manager s3Manager;
    @Resource
    private IUserArticleService userArticleService;
    @Resource
    private IUserMessageService userMessageService;
    @Resource
    private IObsidianConfigService obsidianConfigService;

    @Override
    public UserConfig queryConfig(String unionId) {
        CustomConfig config = customConfigService.getCustomConfigByUnionId(unionId);
        if (config == null) {
            UserConfig userConfig = new UserConfig();
            userConfig.setAlwaysUseCloudinary(1);
            userConfig.setAlwaysUsePicCloud(1);
            userConfig.setNoCover(0);
            userConfig.setNoIcon(0);
            userConfig.setQuickClipAsEnterPage(0);
            userConfig.setSupportAllSite(0);
            userConfig.setToObsidian(0);
            userConfig.setUnionId(unionId);
            return userConfig;
        }
        UserConfig userConfig = new UserConfig();
        userConfig.setAlwaysUseCloudinary(Optional.ofNullable(config.getAlwaysUseCloudinary()).orElse(1));
        userConfig.setAlwaysUsePicCloud(Optional.ofNullable(config.getAlwaysUsePicCloud()).orElse(1));
        userConfig.setNoCover(Optional.ofNullable(config.getNoCover()).orElse(0));
        userConfig.setNoIcon(Optional.ofNullable(config.getNoIcon()).orElse(0));
        userConfig.setQuickClipAsEnterPage(config.getQuickClipAsEnterPage());
        userConfig.setSupportAllSite(config.getSupportAllSite());
        userConfig.setToObsidian(Optional.ofNullable(config.getToObsidian()).orElse(0));
        userConfig.setUnionId(config.getUnionId());
        if (config.getToObsidian() == 1) {
            ObsidianConfig obsidianConfig = obsidianConfigService.getByUnionId(unionId);
            if (obsidianConfig != null) {
                userConfig.setOutputType("markdown");
                userConfig.setMarkdownSavePath(obsidianConfig.getSaveRoot());
                userConfig.setObConfig(getObConfig(obsidianConfig));
            }
        }
        return userConfig;
    }

    @Override
    public S3Config queryS3Config(String unionId) {
        return s3Manager.getS3Config(unionId);
    }

    @Override
    public GloablConfig gloablConfig() {
        GloablConfig config = new GloablConfig();
        config.setAppend2NotionUseCloudFunction(notionResourceService.append2NotionUseCloudFunction());
        config.setBlockSizeLimit(notionResourceService.getBlockSizeLimit());
        config.setCreateTokenUseCloudFunction(notionResourceService.createTokenUseCloudFunction());
        config.setMaxImageSizeLimit(notionResourceService.getMaxImageSizeLimit());
        config.setSearchNotionUseCloudFunction(notionResourceService.searchNotionUseCloudFunction());
        config.setSend2NotionUseCloudFunction(notionResourceService.send2NotionUseCloudFunction());
        config.setUpdate2NotionUseCloudFunction(notionResourceService.update2NotionUseCloudFunction());
        return config;
    }

    @Override
    public S3Config getImgS3Config(String unionId) {
        return s3Manager.getImgS3Config(unionId);
    }

    @Override
    public ArticleFieldDTO getArticleFieldDTO(String unionId) {
        return userArticleService.queryArticleFieldDTO(unionId);
    }

    @Override
    public MessageFieldDTO getMessageFieldDTO(String unionId) {
        return userMessageService.queryMessageFieldDto(unionId);
    }


    private ObsidianDTO getObConfig(ObsidianConfig config) {
        ObsidianDTO obConfig = new ObsidianDTO();
        obConfig.setUnionId(config.getUnionId());
        obConfig.setSaveRoot(config.getSaveRoot());
        obConfig.setAttRoot(config.getAttRoot());
        obConfig.setArtInfo(JSON.parseObject(config.getArtInfo(), ObsidianArticleProperty.class));
        obConfig.setMsgInfo(JSON.parseObject(config.getMsgInfo(), ObsidianMessageProperty.class));
        return obConfig;
    }
}
