package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 用户常用标签实体类
 * 用于存储用户的常用标签信息，支持客户端同步功能
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@Setter
@TableName("user_common_tags")
public class UserCommonTags {

    /**
     * 主键ID，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户唯一标识
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 常用标签列表，JSON格式存储
     * 例如：["技术", "学习", "工作"]
     */
    @TableField("tags")
    private String tags;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 软删除标记
     * 0-未删除，1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
}