package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 站点解析规则
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("site_rules")
public class SiteRules {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 来源
     */
    @TableField("origin")
    private Integer origin;

    /**
     * 站点名称
     */
    @TableField("site_name")
    private String siteName;

    /**
     * 站点路径表达式
     */
    @TableField("url_regex")
    private String urlRegex;

    /**
     * logo 获取方式
     */
    @TableField("icon_expr")
    private String iconExpr;

    /**
     * cover 获取方式
     */
    @TableField("cover_expr")
    private String coverExpr;

    /**
     * 标题获取方式
     */
    @TableField("title_expr")
    private String titleExpr;

    /**
     * 作者获取方式
     */
    @TableField("author_expr")
    private String authorExpr;

    /**
     * 链接获取方式
     */
    @TableField("link_expr")
    private String linkExpr;

    /**
     * 发布时间获取方式
     */
    @TableField("time_expr")
    private String timeExpr;

    /**
     * 描述内容获取方式
     */
    @TableField("desc_expr")
    private String descExpr;

    /**
     * 正文获取方式
     */
    @TableField("article_expr")
    private String articleExpr;

    /**
     * 评论获取方式
     */
    @TableField("comments_expr")
    private String commentsExpr;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Integer deleted;
}
