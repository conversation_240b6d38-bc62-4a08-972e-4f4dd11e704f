package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.GoodsMapper;
import com.maixi.road.admin.biz.domain.Goods;
import com.maixi.road.admin.biz.service.IGoodsService;
import com.maixi.road.common.core.model.response.GoodsVo;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class GoodsServiceImpl extends ServiceImpl<GoodsMapper, Goods> implements IGoodsService {

    @Override
    public List<GoodsVo> getGoodsList() {
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Goods::getStatus, 1).eq(Goods::getDeleted, 0).eq(Goods::getType, 0);
        List<Goods> goodsList = list(queryWrapper);
        return goodsList.stream().map(e -> GoodsVo.builder()
                .goodsName(e.getGoodsName())
                .goodsNo(e.getGoodsNo())
                .description(e.getDescription())
                .price(e.getPrice())
                .discount(e.getDiscount())
                .build()).collect(Collectors.toList());
    }

    @Override
    public Goods getByNo(String goodsNo) {
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Goods::getGoodsNo, goodsNo)
                .eq(Goods::getDeleted, 0);
        return getOne(queryWrapper);
    }

    @Override
    public List<GoodsVo> getGoodsListObsidian() {
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Goods::getStatus, 1)
        .eq(Goods::getDeleted, 0)
        .eq(Goods::getType, 1);
        List<Goods> goodsList = list(queryWrapper);
        return goodsList.stream().map(e -> GoodsVo.builder()
                .goodsName(e.getGoodsName())
                .goodsNo(e.getGoodsNo())
                .description(e.getDescription())
                .price(e.getPrice())
                .discount(e.getDiscount())
                .build()).collect(Collectors.toList());
    }
}
