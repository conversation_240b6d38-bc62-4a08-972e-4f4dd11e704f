package com.maixi.road.admin.web.controller;

import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.framework.web.Road;

import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */

@RestController
@RequestMapping("/miniprogram")
public class MemberController extends BaseController {

    @Resource
    private IMemberService memberService;


    @GetMapping("/remainForeverVipCount")
    public Result<Integer> remainForeverVipCount() {
        return Result.success(memberService.remainForeverVipCount());
    }


    @GetMapping("/vipOpen")
    public Result<Boolean> vipOpen() {
        return Result.success(memberService.vipOpen(Road.getLoginUser().getUnionId()));
    }


    @GetMapping("/vipInfo")
    public Result<Integer> getVipType() {
        return Result.success(memberService.getVipType(Road.getLoginUser().getUnionId()));
    }

}
