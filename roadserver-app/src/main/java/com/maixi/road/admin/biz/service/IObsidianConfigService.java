package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.ObsidianConfig;
import com.maixi.road.common.core.model.request.ObsidianSaveRQ;

/**
 * <p>
 * Obsidian配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IObsidianConfigService extends IService<ObsidianConfig> {

    /**
     * 根据用户ID获取Obsidian配置
     *
     * @param unionId 用户ID
     * @return Obsidian配置信息
     */
    ObsidianConfig getByUnionId(String unionId);

    /**
     * 保存或更新用户的Obsidian配置
     *
     * @param obsidianConfig Obsidian配置信息
     * @return 是否操作成功
     */
    boolean saveOrUpdateConfig(ObsidianSaveRQ obsidianSaveRQ);

    /**
     * 删除用户的Obsidian配置（逻辑删除）
     *
     * @param unionId 用户ID
     * @return 是否删除成功
     */
    boolean removeByUnionId(String unionId);

}