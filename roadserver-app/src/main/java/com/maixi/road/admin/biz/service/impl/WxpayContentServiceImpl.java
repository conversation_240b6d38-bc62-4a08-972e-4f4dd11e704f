package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.WxpayContentMapper;
import com.maixi.road.admin.biz.domain.WxpayContent;
import com.maixi.road.admin.biz.service.IWxpayContentService;

import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class WxpayContentServiceImpl extends ServiceImpl<WxpayContentMapper, WxpayContent> implements IWxpayContentService {

    @Override
    public void updateNotifyResult(String outTradeNo, String notifyResult, String transactionId) {
        LambdaUpdateChainWrapper<WxpayContent> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        lambdaUpdateChainWrapper
                .eq(WxpayContent::getOutTradeNo, outTradeNo)
                .set(WxpayContent::getNotifyResult, notifyResult)
                .set(WxpayContent::getTransactionId, transactionId)
                .update();
    }

    @Override
    public void updateQueryResult(String outTradeNo, String queryResult, String transactionId) {
        LambdaUpdateChainWrapper<WxpayContent> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        lambdaUpdateChainWrapper
               .eq(WxpayContent::getOutTradeNo, outTradeNo)
               .set(WxpayContent::getQueryResult, queryResult)
               .set(WxpayContent::getTransactionId, transactionId)
               .update();
    }
}
