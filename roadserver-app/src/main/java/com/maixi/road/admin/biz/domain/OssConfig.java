package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("oss_config")
public class OssConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 云厂商类型
     */
    @TableField("`type`")
    private String type;

    @TableField("union_id")
    private String unionId;

    @TableField("endpoint")
    private String endpoint;

    @TableField("region")
    private String region;

    @TableField("access_key")
    private String accessKey;

    @TableField("access_secret")
    private String accessSecret;

    @TableField("bucket_name")
    private String bucketName;

    /**
     * 关联域名
     */
    @TableField("custom_domain")
    private String customDomain;

    @TableField("gmt_create")
    private Long gmtCreate;

    @TableField("gmt_update")
    private Long gmtUpdate;

    @TableField("deleted")
    private Integer deleted;

    /**
     * 优先使用 Cloudinary 图床存储图片
     */
    @TableField("cloudinary_first")
    private Integer cloudinaryFirst;
}
