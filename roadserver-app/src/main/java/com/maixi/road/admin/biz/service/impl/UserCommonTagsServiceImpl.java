package com.maixi.road.admin.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.UserCommonTagsMapper;
import com.maixi.road.admin.biz.domain.UserCommonTags;
import com.maixi.road.admin.biz.service.IUserCommonTagsService;
import com.maixi.road.common.core.model.dto.UserCommonTagsDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户常用标签服务实现类
 * 提供用户常用标签的完整业务逻辑处理，包括标签的增删改查和JSON序列化处理
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
public class UserCommonTagsServiceImpl extends ServiceImpl<UserCommonTagsMapper, UserCommonTags>
        implements IUserCommonTagsService {

    @Override
    public UserCommonTagsDTO getUserCommonTags(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            log.warn("获取用户常用标签失败: unionId为空");
            return null;
        }

        try {
            // 查询用户的常用标签记录
            LambdaQueryWrapper<UserCommonTags> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserCommonTags::getUnionId, unionId);
            UserCommonTags userCommonTags = this.getOne(queryWrapper);

            if (userCommonTags == null) {
                log.info("用户暂无常用标签记录, unionId={}", unionId);
                return null;
            }

            // 解析标签JSON字符串
            List<String> tagsList = new ArrayList<>();
            if (StringUtils.isNotBlank(userCommonTags.getTags())) {
                try {
                    tagsList = JSON.parseObject(userCommonTags.getTags(), new TypeReference<List<String>>() {
                    });
                } catch (Exception e) {
                    log.error("解析用户常用标签JSON失败, unionId={}, tags={}", unionId, userCommonTags.getTags(), e);
                    // 如果解析失败，返回空列表而不是null
                    tagsList = new ArrayList<>();
                }
            }

            // 构建返回对象
            UserCommonTagsDTO result = UserCommonTagsDTO.builder()
                    .unionId(unionId)
                    .tags(tagsList)
                    .createTime(userCommonTags.getCreateTime())
                    .updateTime(userCommonTags.getUpdateTime())
                    .build();

            log.info("获取用户常用标签成功, unionId={}, tagCount={}", unionId, tagsList.size());
            return result;

        } catch (Exception e) {
            log.error("获取用户常用标签失败, unionId={}", unionId, e);
            return null;
        }
    }

    @Override
    public boolean syncUserCommonTags(String unionId, List<String> tags) {
        if (StringUtils.isBlank(unionId)) {
            log.warn("同步用户常用标签失败: unionId为空");
            return false;
        }

        // 如果标签列表为空，则删除用户的常用标签记录
        if (CollectionUtils.isEmpty(tags)) {
            return deleteUserCommonTags(unionId);
        }

        try {
            // 将标签列表转换为JSON字符串
            String tagsJson = JSON.toJSONString(tags);
            LocalDateTime now = LocalDateTime.now();

            // 查询用户是否已有常用标签记录
            LambdaQueryWrapper<UserCommonTags> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserCommonTags::getUnionId, unionId);
            UserCommonTags existingTags = this.getOne(queryWrapper);

            if (existingTags != null) {
                // 更新现有记录
                existingTags.setTags(tagsJson);
                existingTags.setUpdateTime(now);
                boolean updateResult = this.updateById(existingTags);

                if (updateResult) {
                    log.info("更新用户常用标签成功, unionId={}, tagCount={}", unionId, tags.size());
                } else {
                    log.error("更新用户常用标签失败, unionId={}, tagCount={}", unionId, tags.size());
                }
                return updateResult;
            } else {
                // 创建新记录
                UserCommonTags newTags = new UserCommonTags();
                newTags.setUnionId(unionId);
                newTags.setTags(tagsJson);
                newTags.setCreateTime(now);
                newTags.setUpdateTime(now);
                newTags.setDeleted(0);

                boolean saveResult = this.save(newTags);

                if (saveResult) {
                    log.info("创建用户常用标签成功, unionId={}, tagCount={}", unionId, tags.size());
                } else {
                    log.error("创建用户常用标签失败, unionId={}, tagCount={}", unionId, tags.size());
                }
                return saveResult;
            }

        } catch (Exception e) {
            log.error("同步用户常用标签失败, unionId={}, tagCount={}", unionId,
                    tags != null ? tags.size() : 0, e);
            return false;
        }
    }

    @Override
    public boolean deleteUserCommonTags(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            log.warn("删除用户常用标签失败: unionId为空");
            return false;
        }

        try {
            // 执行软删除
            LambdaQueryWrapper<UserCommonTags> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserCommonTags::getUnionId, unionId);
            boolean deleteResult = this.remove(queryWrapper);

            if (deleteResult) {
                log.info("删除用户常用标签成功, unionId={}", unionId);
            } else {
                log.warn("删除用户常用标签失败，可能记录不存在, unionId={}", unionId);
            }
            return deleteResult;

        } catch (Exception e) {
            log.error("删除用户常用标签失败, unionId={}", unionId, e);
            return false;
        }
    }
}