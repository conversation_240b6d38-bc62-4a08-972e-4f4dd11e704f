package com.maixi.road.admin.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.maixi.road.admin.biz.domain.ProductNews;
import com.maixi.road.admin.biz.service.IProductNewsService;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.response.ProductNewsVo;

import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */

@RestController
@RequestMapping("/miniprogram")
public class ProductNewsController extends BaseController {

    @Resource
    private IProductNewsService productNewsService;

    @GetMapping("/getProductNews")
    public Result<List<ProductNewsVo>> getProductNews() {
        LambdaQueryWrapper<ProductNews> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductNews::getDeleted, 0).eq(ProductNews::getType, 0);
        List<ProductNews> list = productNewsService.list(queryWrapper);
        return Result.success(list.stream().map(e -> {
            return ProductNewsVo.builder()
                    .id(e.getId())
                    .title(e.getTitle())
                    .desc(e.getDesc())
                    .link(e.getLink())
                    .cover(e.getCover())
                    .build();
        }).toList());
    }

    @GetMapping("/getObsidianNews")
    public Result<List<ProductNewsVo>> getObsidianNews() {
        LambdaQueryWrapper<ProductNews> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductNews::getDeleted, 0).eq(ProductNews::getType, 1);
        List<ProductNews> list = productNewsService.list(queryWrapper);
        return Result.success(list.stream().map(e -> {
            return ProductNewsVo.builder()
                    .id(e.getId())
                    .title(e.getTitle())
                    .desc(e.getDesc())
                    .link(e.getLink())
                    .cover(e.getCover())
                    .build();
        }).toList());
    }

}
