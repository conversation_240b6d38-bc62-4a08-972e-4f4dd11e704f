package com.maixi.road.admin.web.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.maixi.road.admin.biz.domain.ObsidianConfig;
import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.service.IObsidianConfigService;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.request.ObsidianSaveRQ;
import com.maixi.road.framework.web.LoginUser;
import com.maixi.road.framework.web.Road;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@RequestMapping("/miniprogram/obsidian")
public class ObsidianController extends BaseController {

    @Resource
    private IObsidianConfigService obsidianConfigService;
    @Resource
    private IUserInfoService userInfoService;

    @GetMapping("/getConfig")
    public Result<ObsidianConfig> getConfig() {
        LoginUser loginUser = Road.getLoginUser();
        // 获取主账号
        UserInfo mainUserInfo = userInfoService.getUserInfoBySubUnionId(loginUser.getUnionId());
        String unionId = loginUser.getUnionId();
        if (mainUserInfo != null) {
            unionId = mainUserInfo.getUnionId();
        }
        return Result.success(obsidianConfigService.getByUnionId(unionId));
    }

    @PostMapping("/saveConfig")
    public Result<Boolean> saveConfig(@RequestBody ObsidianSaveRQ obsidianSaveRQ) {
        LoginUser loginUser = Road.getLoginUser();
        obsidianSaveRQ.setUnionId(loginUser.getUnionId());
        log.info("saveConfig: {}", JSON.toJSONString(obsidianSaveRQ));
        return Result.success(obsidianConfigService.saveOrUpdateConfig(obsidianSaveRQ));
    }
}
