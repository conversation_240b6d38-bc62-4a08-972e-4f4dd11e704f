package com.maixi.road.admin.web.controller;

import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.maixi.road.admin.manager.OrderManager;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.enums.error.OrderErrCodeEnum;
import com.maixi.road.common.core.exception.OrderException;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.request.MemberCreateParam;
import com.maixi.road.common.core.model.request.OrderRequest;
import com.maixi.road.common.core.model.response.WxPayment;
import com.maixi.road.common.core.utils.RequestUtil;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j

@RestController
@RequestMapping("/miniprogram")
public class OrdersController extends BaseController {

    @Resource
    private OrderManager orderManager;


    @PostMapping("/createMember")
    public Result<Integer> createMember(@Valid @RequestBody MemberCreateParam param) {
        if (!Objects.equals("wocao20240317", param.getToken())) {
            throw OrderException.create(OrderErrCodeEnum.USER_UNAUTHORIZED_OPERATION);
        }
        return Result.success(orderManager.createMemberByHand(param));
    }


    @PostMapping("/submitOrder")
    public Result<WxPayment> submitOrder(HttpServletRequest request, @Valid @RequestBody OrderRequest orderRequest)
            throws WxPayException {
        String appId = request.getHeader("X-App-Id");
        if (StringUtils.isBlank(appId)) {
            appId = "wx34b2cfe3fdbc3b61";
        }
        WxPayment wxPayment = orderManager.createOrder(orderRequest, RequestUtil.getIpByHttpServletRequest(), appId);
        return Result.success(wxPayment);
    }


    @PostMapping("/notify/order")
    public String notify(@RequestBody String xmlData) {
        log.info("收到微信支付回调:{}", xmlData);
        try {
            orderManager.parseOrderNotifyResult(xmlData);
        } catch (Exception e) {
            return WxPayNotifyResponse.fail(e.getMessage());
        }
        return WxPayNotifyResponse.success("成功");
    }
}
