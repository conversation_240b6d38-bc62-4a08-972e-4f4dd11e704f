package com.maixi.road.admin.biz.service;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.common.core.model.response.UserSimpleInfo;
import com.maixi.road.common.core.model.response.UserVo;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IUserInfoService extends IService<UserInfo> {

    Optional<UserInfo> getUserInfoOptByUnionId(String unionId);

    Optional<UserInfo> getUserInfoOptBySubUnionId(String unionId);

    UserInfo getUserInfoByUnionId(String userId);

    UserInfo getUserInfoBySubUnionId(String userId);

    UserVo getUserInfo(String unionId, String appId);

    UserSimpleInfo updateUserInfo(String unionId, UserSimpleInfo param);

    String uploadAvatar(MultipartFile file, String unionId) throws IOException;

    List<UserInfo> getBySubUserIdList(List<String> unionIdList);

    void updateOpenIdByUnionId(String unionId, String openid);

    void clearOpenIdByUnionId(String unionId);

    void updateStatisticDataById(Integer textSum, Integer articleSum, Integer id);
}
