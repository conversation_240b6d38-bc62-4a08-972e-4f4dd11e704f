package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.UserMessage;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.core.model.response.UserMessageDto;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IUserMessageService extends IService<UserMessage> {

    UserMessageDto queryMessageDatabaseInfo(String unionId);

    MessageFieldDTO queryMessageFieldDto(String unionId);

//    boolean updateMessageDatabaseInfo(String unionId, UserMessageDto userMessageDto);

    UserMessage getByUnionId(String unionId);

    void removeByUnionId(String loginUnionId);
}
