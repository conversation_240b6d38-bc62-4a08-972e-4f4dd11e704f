package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.ChatOpenListMapper;
import com.maixi.road.admin.biz.domain.ChatOpenList;
import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.manager.UserManager;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.framework.web.MainUserWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class ChatOpenListServiceImpl extends ServiceImpl<ChatOpenListMapper, ChatOpenList>
        implements IChatOpenListService {

    @Resource
    private UserManager userManager;

    @Override
    public boolean enhanceSwitch(String unionId, boolean enhanceSwitch) {
        MainUserWrapper mainUserWrapper = userManager.getMainUserWrapper();
        if (!mainUserWrapper.isMainUser()) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
        }
        if (enhanceSwitch) {
            ChatOpenList chatOpenList = new ChatOpenList();
            chatOpenList.setUnionId(mainUserWrapper.getMainUnionId());
            this.save(chatOpenList);
        } else {
            LambdaQueryWrapper<ChatOpenList> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChatOpenList::getUnionId, mainUserWrapper.getMainUnionId());
            this.remove(queryWrapper);
        }
        return true;
    }

}
