package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.NotionRelation;

import java.io.IOException;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface INotionRelationService extends IService<NotionRelation> {

    NotionRelation getRelationByUnionId(String unionId);

    /**
     * 检查授权
     * @param unionId 用户的 unionId
     * @param withDeleteUnavailableRelation 是否删除无效授权关系
     * @return
     */
    boolean checkAuth(String unionId, boolean withDeleteUnavailableRelation);

    boolean authCallback(String code, String unionId) throws IOException;

    boolean checkUserOrMainUserHasBindNotion(String unionId);

    boolean removeAuth(String loginUnionId);

}
