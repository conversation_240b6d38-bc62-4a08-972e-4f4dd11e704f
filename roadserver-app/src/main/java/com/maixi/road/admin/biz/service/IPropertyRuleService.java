package com.maixi.road.admin.biz.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.PropertyRule;

/**
 * <p>
 * 属性解析规则 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IPropertyRuleService extends IService<PropertyRule> {

    List<PropertyRule> rmRules();

    List<PropertyRule> titleRule();

    List<PropertyRule> descriptionRule();

    List<PropertyRule> iconRule();

    List<PropertyRule> coverRule();

    List<PropertyRule> authorRule();

    List<PropertyRule> siteNameRule();

    List<PropertyRule> articleRule();
}
