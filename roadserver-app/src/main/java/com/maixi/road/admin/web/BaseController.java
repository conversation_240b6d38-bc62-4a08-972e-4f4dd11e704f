package com.maixi.road.admin.web;

import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.maixi.road.admin.manager.UserManager;
import com.maixi.road.framework.web.MainUserWrapper;
import com.maixi.road.framework.web.Road;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class BaseController {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserManager userManager;

    protected String getLoginUnionId() {
        return Road.getLoginUser().getUnionId();
    }

    protected MainUserWrapper getMainUserWrapper(){
        return userManager.getMainUserWrapper();
    }

    protected String getUnionIdFromCacheByState(String state) {
        return redissonClient.<String>getBucket(state).get();
    }

}
