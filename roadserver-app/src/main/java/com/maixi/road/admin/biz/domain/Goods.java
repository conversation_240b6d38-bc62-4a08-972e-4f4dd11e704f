package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("goods")
public class Goods {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商品类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 商品名称
     */
    @TableField("goods_name")
    private String goodsName;

    /**
     * 商品编码
     */
    @TableField("goods_no")
    private String goodsNo;

    /**
     * 商品描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 价格
     */
    @TableField("price")
    private Integer price;

    /**
     * 优惠
     */
    @TableField("discount")
    private Integer discount;

    /**
     * 货币类型
     */
    @TableField("currency")
    private String currency;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Integer deleted;
}
