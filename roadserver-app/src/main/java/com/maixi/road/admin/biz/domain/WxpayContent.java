package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import lombok.*;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("wxpay_content")
public class WxpayContent {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("transaction_id")
    private String transactionId;

    @TableField("out_trade_no")
    private String outTradeNo;

    @TableField("pre_request")
    private String preRequest;

    @TableField("pre_result")
    private String preResult;

    @TableField("notify_result")
    private String notifyResult;

    @TableField("query_result")
    private String queryResult;

    @TableField("create_time")
    private LocalDateTime createTime;

    @TableField("update_time")
    private LocalDateTime updateTime;
}
