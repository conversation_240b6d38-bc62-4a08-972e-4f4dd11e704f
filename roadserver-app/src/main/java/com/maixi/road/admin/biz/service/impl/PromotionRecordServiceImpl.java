package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.common.business.user.enums.PromotionStatusEnum;
import com.maixi.road.common.core.model.response.PromotionData;
import com.maixi.road.common.core.model.response.PromotionVo;
import com.maixi.road.common.core.utils.DateUtils;
import com.maixi.road.admin.biz.dao.PromotionRecordMapper;
import com.maixi.road.admin.biz.domain.Member;
import com.maixi.road.admin.biz.domain.PromotionRecord;
import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.biz.service.IPromotionRecordService;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 推广记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class PromotionRecordServiceImpl extends ServiceImpl<PromotionRecordMapper, PromotionRecord>
        implements IPromotionRecordService {

    @Resource
    private IMemberService memberService;

    @Override
    public List<PromotionVo> creditListByUnionId(String unionId) {
        LambdaQueryWrapper<PromotionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PromotionRecord::getUnionId, unionId);
        queryWrapper.eq(PromotionRecord::getDeleted, 0);
        queryWrapper.orderByDesc(PromotionRecord::getId);
        List<PromotionRecord> list = this.list(queryWrapper);
        return list.stream().map(e -> {
            String formatLocalDate = DateUtils.formatLocalDate(e.getGmtCreate().toLocalDate());
            return PromotionVo.builder()
                    .promotionCode(e.getPromotionCode())
                    .credit(e.getCredit())
                    .objectId(getHideObjectId(e.getObjectId()))
                    .status(e.getStatus())
                    .gmtCreate(formatLocalDate)
                    .build();
        }).toList();
    }

    private String getHideObjectId(String objectId) {
        String hideObjectId = objectId.charAt(0) + "***"
                + objectId.substring(objectId.length() - 3);
        return hideObjectId;
    }

    @Override
    public PromotionData promotionData(String unionId) {
        PromotionData promotionData = new PromotionData();
        Member member = memberService.selectByUnionId(unionId);
        if (member == null) {
            promotionData.setSum(0);
            promotionData.setUseSum(0);
            promotionData.setAvailableSum(0);
            promotionData.setShareCount(0);
            return promotionData;
        }

        LambdaQueryWrapper<PromotionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PromotionRecord::getUnionId, unionId);
        queryWrapper.eq(PromotionRecord::getDeleted, 0);
        List<PromotionRecord> list = this.list(queryWrapper);

        promotionData.setPromotionCode(member.getPromotionCode());
        if (CollectionUtils.isEmpty(list)) {
            promotionData.setSum(0);
            promotionData.setUseSum(0);
            promotionData.setAvailableSum(0);
            promotionData.setShareCount(0);
        } else {
            int sum = 0;
            int useSum = 0;
            int availableSum = 0;
            for (PromotionRecord promotionRecord : list) {
                sum += promotionRecord.getCredit();
                if (PromotionStatusEnum.UN_EXCHANGE.getCode().equals(promotionRecord.getStatus())) {
                    availableSum += promotionRecord.getCredit();
                } else if (PromotionStatusEnum.EXCHANGED.getCode().equals(promotionRecord.getStatus())) {
                    useSum += promotionRecord.getCredit();
                }
            }
            promotionData.setSum(sum);
            promotionData.setUseSum(useSum);
            promotionData.setAvailableSum(availableSum);
            promotionData.setShareCount(list.size());
        }
        return promotionData;
    }

}
