package com.maixi.road.admin.manager;

import com.maixi.road.common.core.constant.CommonConstants;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.utils.JwtUtil;
import com.maixi.road.common.core.utils.RequestUtil;

import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LoginTokenGetManager {

    @Value("${spring.profiles.active}")
    private String activeProfile;

    public String getLoginUnionId() {
        HttpServletRequest request = RequestUtil.getRequest();
        return getLoginUnionId(request);
    }

    public String getLoginUnionId(HttpServletRequest request) {
        if ("dev".equals(activeProfile) && "dev".equals(request.getHeader("env"))) {
            return CommonConstants.TEST_UNION_ID;
        }
        String jwtToken = request.getHeader("Authorization");
        if (StringUtils.isBlank(jwtToken)) {
            throw RoadException.create(ErrorCodeEnum.USER_NOT_LOGIN, "请重新进入小程序");
        }
        Claims claims = JwtUtil.parse(jwtToken);
        if (claims == null) {
            throw RoadException.create(ErrorCodeEnum.USER_LOGIN_TOKEN_EXPIRED, "请重新进入小程序");
        }
        return claims.getSubject();
    }
}
