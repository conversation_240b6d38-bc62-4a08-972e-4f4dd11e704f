package com.maixi.road.admin.web.controller;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.maixi.road.admin.biz.domain.NotionRelation;
import com.maixi.road.admin.biz.domain.UserArticle;
import com.maixi.road.admin.biz.domain.UserMessage;
import com.maixi.road.admin.biz.service.INotionRelationService;
import com.maixi.road.admin.biz.service.IUserArticleService;
import com.maixi.road.admin.biz.service.IUserCommonTagsService;
import com.maixi.road.admin.biz.service.IUserMessageService;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.ArticleFieldDTO;
import com.maixi.road.common.core.model.dto.Field;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.core.model.dto.NotionDbRes;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.dto.TagOptionDTO;
import com.maixi.road.common.core.model.dto.UserCommonTagsDTO;
import com.maixi.road.common.core.model.request.SyncUserTagsRQ;
import com.maixi.road.framework.web.LoginUser;
import com.maixi.road.framework.web.MainUserWrapper;
import com.maixi.road.framework.web.Road;
import com.maixi.road.notion.remote.dto.response.DatabaseRS;
import com.maixi.road.notion.remote.dto.response.ListRS;
import com.maixi.road.notion.remote.impl.local.DatabaseLocalClient;
import com.maixi.road.notion.remote.impl.local.SearchLocalClient;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/miniprogram/notion")
public class NotionController extends BaseController {

    @Resource
    private INotionRelationService notionRelationService;
    @Resource
    private IUserArticleService userArticleService;
    @Resource
    private IUserMessageService userMessageService;
    @Resource
    private IUserCommonTagsService userCommonTagsService;
    @Resource
    private SearchLocalClient searchLocalClient;
    @Resource
    private DatabaseLocalClient databaseLocalClient;

    @GetMapping("/getArticleDatabaseFields")
    public Result<ArticleFieldDTO> getArticleDatabaseFields() {
        LoginUser loginUser = Road.getLoginUser();
        ArticleFieldDTO article = userArticleService.queryArticleFieldDTO(loginUser.getUnionId());
        return Result.success(article);
    }

    @GetMapping("/getMessageDatabaseFields")
    public Result<MessageFieldDTO> getMessageDatabaseFields() {
        LoginUser loginUser = Road.getLoginUser();
        return Result.success(userMessageService.queryMessageFieldDto(loginUser.getUnionId()));
    }

    /**
     * 获取用户常用标签
     * 返回用户保存的常用标签列表，用于客户端快速选择
     * 
     * @return Result<UserCommonTagsDTO> 用户常用标签信息
     */
    @GetMapping("/getUserCommonTags")
    public Result<UserCommonTagsDTO> getUserCommonTags() {
        LoginUser loginUser = Road.getLoginUser();

        try {
            UserCommonTagsDTO userCommonTags = userCommonTagsService.getUserCommonTags(loginUser.getUnionId());

            // 如果用户没有常用标签，返回空的DTO对象而不是null
            if (userCommonTags == null) {
                userCommonTags = UserCommonTagsDTO.builder()
                        .unionId(loginUser.getUnionId())
                        .tags(new ArrayList<>())
                        .build();
            }

            log.info("获取用户常用标签成功, unionId={}, tagCount={}",
                    loginUser.getUnionId(), userCommonTags.getTags().size());

            return Result.success(userCommonTags);

        } catch (Exception e) {
            log.error("获取用户常用标签失败, unionId={}", loginUser.getUnionId(), e);
            throw RoadException.create(ErrorCodeEnum.FALLBACK, "获取常用标签失败");
        }
    }

    /**
     * 客户端同步常用标签接口
     * 用于客户端标签缓存过期时从服务端同步最新的常用标签数据
     * 
     * @return Result<List<String>> 常用标签列表
     */
    @GetMapping("/getCommonTags")
    public Result<List<String>> getCommonTags() {
        LoginUser loginUser = Road.getLoginUser();
        
        try {
            UserCommonTagsDTO userCommonTags = userCommonTagsService.getUserCommonTags(loginUser.getUnionId());
            
            // 如果用户没有常用标签，返回空列表
            List<String> tags = new ArrayList<>();
            if (userCommonTags != null && !CollectionUtils.isEmpty(userCommonTags.getTags())) {
                tags = userCommonTags.getTags();
            }
            
            log.info("客户端同步常用标签成功, unionId={}, tagCount={}", 
                    loginUser.getUnionId(), tags.size());
            
            return Result.success(tags);
            
        } catch (Exception e) {
            log.error("客户端同步常用标签失败, unionId={}", loginUser.getUnionId(), e);
            throw RoadException.create(ErrorCodeEnum.FALLBACK, "同步常用标签失败");
        }
    }

    /**
     * 删除用户常用标签接口
     * 清空用户的常用标签备份数据，执行软删除操作
     * 
     * @return Result<Boolean> 删除操作结果
     */
    @PostMapping("/deleteCommonTags")
    public Result<Boolean> deleteCommonTags() {
        LoginUser loginUser = Road.getLoginUser();
        
        try {
            boolean success = userCommonTagsService.deleteUserCommonTags(loginUser.getUnionId());
            
            if (success) {
                log.info("删除用户常用标签成功, unionId={}", loginUser.getUnionId());
                return Result.success(true);
            } else {
                log.warn("删除用户常用标签失败，可能记录不存在, unionId={}", loginUser.getUnionId());
                return Result.success(false);
            }
            
        } catch (Exception e) {
            log.error("删除用户常用标签异常, unionId={}", loginUser.getUnionId(), e);
            throw RoadException.create(ErrorCodeEnum.FALLBACK, "删除常用标签失败");
        }
    }

    /**
     * 同步用户常用标签
     * 客户端上传标签列表，服务端进行全量替换更新
     * 
     * @param request 同步标签请求对象
     * @return Result<Boolean> 操作结果
     */
    @PostMapping("/syncUserCommonTags")
    public Result<Boolean> syncUserCommonTags(@RequestBody SyncUserTagsRQ request) {
        LoginUser loginUser = Road.getLoginUser();

        // 参数验证
        if (request == null || CollectionUtils.isEmpty(request.getTags())) {
            throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "参数为空");
        }

        // 去重和过滤空标签
        List<String> tags = request.getTags().stream()
                .filter(tag -> tag != null && !tag.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());

        try {
            boolean success = userCommonTagsService.syncUserCommonTags(loginUser.getUnionId(), tags);

            if (success) {
                log.info("同步用户常用标签成功, unionId={}, tagCount={}",
                        loginUser.getUnionId(), tags.size());
                return Result.success(true);
            } else {
                log.error("同步用户常用标签失败, unionId={}, tagCount={}",
                        loginUser.getUnionId(), tags.size());
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "同步常用标签失败");
            }

        } catch (Exception e) {
            log.error("同步用户常用标签异常, unionId={}, tagCount={}",
                    loginUser.getUnionId(), tags.size(), e);
            throw RoadException.create(ErrorCodeEnum.FALLBACK, "同步常用标签失败");
        }
    }

    /**
     * 获取文章数据库标签字段的所有可选值
     * 通过调用 Notion API 获取数据库详细信息，解析标签字段的所有选项
     * 
     * @return Result<TagOptionDTO> 标签字段的所有可选值
     */
    @GetMapping("/getArticleDatabaseTagOptions")
    public Result<TagOptionDTO> getArticleDatabaseTagOptions() {
        LoginUser loginUser = Road.getLoginUser();

        // 获取用户的授权信息和文章配置
        NotionRelation relation = notionRelationService.getRelationByUnionId(loginUser.getUnionId());
        if (relation == null) {
            throw RoadException.create(ErrorCodeEnum.USER_UNAUTHORIZED);
        }

        // 获取用户的文章字段配置
        ArticleFieldDTO articleFieldDTO = userArticleService.queryArticleFieldDTO(loginUser.getUnionId());
        if (articleFieldDTO == null || articleFieldDTO.getDatabaseId() == null) {
            throw RoadException.create(ErrorCodeEnum.FALLBACK, "未找到文章数据库配置");
        }

        // 检查是否配置了标签字段
        if (articleFieldDTO.getTags() == null || articleFieldDTO.getTags().getName() == null) {
            throw RoadException.create(ErrorCodeEnum.FALLBACK, "未配置标签字段");
        }

        try {
            // 调用 Notion API 获取数据库详细信息
            DatabaseRS databaseRS = databaseLocalClient.retrieveDatabase(
                    relation.getAccessToken(),
                    articleFieldDTO.getDatabaseId());

            if (databaseRS == null || databaseRS.getProperties() == null) {
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "获取数据库信息失败");
            }

            // 解析标签字段的选项
            String tagFieldName = articleFieldDTO.getTags().getName();
            JSONObject properties = databaseRS.getProperties();
            JSONObject tagProperty = properties.getJSONObject(tagFieldName);

            if (tagProperty == null) {
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "标签字段不存在: " + tagFieldName);
            }

            String fieldType = tagProperty.getString("type");
            List<String> options = new ArrayList<>();

            // 根据字段类型解析选项
            if ("multi_select".equals(fieldType) || "select".equals(fieldType)) {
                JSONObject typeObject = tagProperty.getJSONObject(fieldType);
                if (typeObject != null && typeObject.containsKey("options")) {
                    JSONArray optionsArray = typeObject.getJSONArray("options");
                    if (optionsArray != null) {
                        for (int i = 0; i < optionsArray.size(); i++) {
                            JSONObject option = optionsArray.getJSONObject(i);
                            if (option != null && option.containsKey("name")) {
                                options.add(option.getString("name"));
                            }
                        }
                    }
                }
            } else {
                log.warn("标签字段类型不支持获取选项: fieldType={}, fieldName={}", fieldType, tagFieldName);
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "标签字段类型不支持: " + fieldType);
            }

            // 构建返回结果
            TagOptionDTO result = TagOptionDTO.builder()
                    .fieldName(tagFieldName)
                    .fieldType(fieldType)
                    .options(options)
                    .build();

            log.info("获取标签字段选项成功, unionId={}, fieldName={}, optionCount={}",
                    loginUser.getUnionId(), tagFieldName, options.size());

            return Result.success(result);

        } catch (IOException e) {
            log.error("获取文章数据库标签选项失败, unionId={}, databaseId={}, error={}",
                    loginUser.getUnionId(), articleFieldDTO.getDatabaseId(), e.getMessage());
            throw RoadException.create(ErrorCodeEnum.FALLBACK, "获取标签选项失败");
        }
    }

    /**
     * 获取文章数据库的属性
     * 
     * @return
     */
    @GetMapping("/getArticleDatabaseProperties")
    public Result<NotionDbRes> getArticleDatabaseProperties() {
        // 使用用户的授权 token，查找 最新的 文章数据库
        LoginUser loginUser = Road.getLoginUser();
        NotionRelation relation = notionRelationService.getRelationByUnionId(loginUser.getUnionId());
        if (relation == null) {
            throw RoadException.create(ErrorCodeEnum.USER_UNAUTHORIZED);
        }
        try {
            ListRS<DatabaseRS> list = searchLocalClient.searchDbSortByLastEditedTimeDesc(relation.getAccessToken(),
                    "文章数据库");
            if (list == null || list.getResults() == null || list.getResults().isEmpty()) {
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "未查询到文章数据库，请确保已授权");
            }
            List<DatabaseRS> databaseResponses = list.getResults().stream()
                    .filter(e -> e.getTitle().get(0).getPlain_text().equals("文章数据库")).collect(Collectors.toList());
            if (databaseResponses.isEmpty()) {
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "未查询到文章数据库，请确保已授权");
            }
            if (databaseResponses.size() > 1) {
                log.warn("存在多个已授权的文章数据库, unionId={}, relationId={}", loginUser.getUnionId(), relation.getId());
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "存在多个已授权的文章数据库");
            }
            DatabaseRS databaseResponse = databaseResponses.getFirst();
            String databaseId = databaseResponse.getId();
            Set<Field> fields = new HashSet<>();
            JSONObject object = JSON.parseObject(JSON.toJSONString(databaseResponse.getProperties()));
            object.keySet().forEach(key -> {
                Field field = Field.builder().name(key).type(object.getJSONObject(key).get("type").toString()).build();
                fields.add(field);
            });
            return Result.success(new NotionDbRes(databaseId, fields));
        } catch (IOException e) {
            log.error("查询文章数据库失败, unionId={}, relationId={}", loginUser.getUnionId(), relation.getId(), e.getMessage());
            throw RoadException.create(ErrorCodeEnum.FALLBACK);
        }
    }

    @GetMapping("/getMessageDatabaseProperties")
    public Result<NotionDbRes> getMessageDatabaseProperties() {
        // 使用用户的授权 token，查找 最新的 消息数据库
        LoginUser loginUser = Road.getLoginUser();
        NotionRelation relation = notionRelationService.getRelationByUnionId(loginUser.getUnionId());
        if (relation == null) {
            throw RoadException.create(ErrorCodeEnum.USER_UNAUTHORIZED);
        }
        try {
            ListRS<DatabaseRS> list = searchLocalClient.searchDbSortByLastEditedTimeDesc(relation.getAccessToken(),
                    "消息数据库");
            if (list == null || list.getResults() == null || list.getResults().isEmpty()) {
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "未查询到消息数据库，请确保已授权");
            }
            List<DatabaseRS> databaseResponses = list.getResults().stream()
                    .filter(e -> e.getTitle().get(0).getPlain_text().equals("消息数据库")).collect(Collectors.toList());
            if (databaseResponses.isEmpty()) {
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "未查询到消息数据库，请确保已授权");
            }
            if (databaseResponses.size() > 1) {
                log.warn("存在多个已授权的消息数据库, unionId={}, relationId={}", loginUser.getUnionId(), relation.getId());
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "存在多个已授权的消息数据库");
            }
            DatabaseRS databaseResponse = databaseResponses.getFirst();
            String databaseId = databaseResponse.getId();
            Set<Field> fields = new HashSet<>();
            JSONObject object = JSON.parseObject(JSON.toJSONString(databaseResponse.getProperties()));
            object.keySet().forEach(key -> {
                Field field = Field.builder().name(key).type(object.getJSONObject(key).get("type").toString()).build();
                fields.add(field);
            });
            return Result.success(new NotionDbRes(databaseId, fields));
        } catch (IOException e) {
            log.error("查询消息数据库失败, unionId={}, relationId={}", loginUser.getUnionId(), relation.getId(), e.getMessage());
            throw RoadException.create(ErrorCodeEnum.FALLBACK);
        }
    }

    @PostMapping("/saveArticleDatabaseFields")
    public Result<Boolean> saveArticleDatabaseFields(@RequestBody ArticleFieldDTO articleFieldDTO) {
        MainUserWrapper mainUserWrapper = getMainUserWrapper();
        if (!mainUserWrapper.isMainUser()) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
        }
        LoginUser loginUser = Road.getLoginUser();
        UserArticle article = userArticleService.queryArticle(loginUser.getUnionId());
        if (article == null) {
            NotionRelation relation = notionRelationService.getRelationByUnionId(loginUser.getUnionId());
            if (relation == null) {
                throw RoadException.create(ErrorCodeEnum.USER_UNAUTHORIZED);
            }
            // 直接新建
            article = new UserArticle();
            article.setUnionId(loginUser.getUnionId());
            articleFieldDTO.setUnionId(loginUser.getUnionId());
            articleFieldDTO.setAccessToken(article.getAccessToken());
            article.setConfigJson(JSON.toJSONString(articleFieldDTO));
            article.setDatabaseId(articleFieldDTO.getDatabaseId());
            article.setAccessToken(relation.getAccessToken());
            article.setVersion("2025-04-13");
            article.setNeedContent(0);
            article.setUrl(articleFieldDTO.getUrl().getName());
            article.setOrigin(articleFieldDTO.getOrigin().getName());
            article.setAuthor(articleFieldDTO.getAuthor().getName());
            article.setTag(articleFieldDTO.getTags().getName());
            article.setCreateTime(articleFieldDTO.getCreateTime().getName());
            article.setPublishTime(articleFieldDTO.getPublishTime().getName());
            article.setRemark(articleFieldDTO.getRemark().getName());
            article.setGmtCreate(LocalDateTime.now());
            article.setGmtUpdate(LocalDateTime.now());
            userArticleService.save(article);
            return Result.success(true);
        }
        // 更新
        if (articleFieldDTO.getAccessToken() == null) {
            articleFieldDTO.setAccessToken(article.getAccessToken());
        }
        if (articleFieldDTO.getDatabaseId() == null) {
            log.warn("文章数据库ID为空, unionId={}", loginUser.getUnionId());
            articleFieldDTO.setDatabaseId(article.getDatabaseId());
        }
        if (articleFieldDTO.getUnionId() == null) {
            log.warn("unionId为空, unionId={}", loginUser.getUnionId());
            articleFieldDTO.setUnionId(loginUser.getUnionId());
        }
        article.setConfigJson(JSON.toJSONString(articleFieldDTO));
        article.setDatabaseId(articleFieldDTO.getDatabaseId());
        userArticleService.updateById(article);
        return Result.success(true);
    }

    @PostMapping("/saveMessageDatabaseFields")
    public Result<Boolean> saveMessageDatabaseFields(@RequestBody MessageFieldDTO messageFieldDTO) {
        MainUserWrapper mainUserWrapper = getMainUserWrapper();
        if (!mainUserWrapper.isMainUser()) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
        }
        LoginUser loginUser = Road.getLoginUser();
        UserMessage message = userMessageService.getByUnionId(loginUser.getUnionId());
        if (message == null) {
            NotionRelation relation = notionRelationService.getRelationByUnionId(loginUser.getUnionId());
            if (relation == null) {
                throw RoadException.create(ErrorCodeEnum.USER_UNAUTHORIZED);
            }
            // 直接新建
            message = new UserMessage();
            message.setUnionId(loginUser.getUnionId());
            messageFieldDTO.setUnionId(loginUser.getUnionId());
            messageFieldDTO.setAccessToken(message.getAccessToken());
            message.setConfigJson(JSON.toJSONString(messageFieldDTO));
            message.setDatabaseId(messageFieldDTO.getDatabaseId());
            message.setAccessToken(relation.getAccessToken());
            message.setVersion("2025-04-13");
            message.setType(messageFieldDTO.getType().getName());
            message.setTag(messageFieldDTO.getTags().getName());
            message.setCreateTime(messageFieldDTO.getCreateTime().getName());
            message.setGmtCreate(LocalDateTime.now());
            message.setGmtUpdate(LocalDateTime.now());
            userMessageService.save(message);
            return Result.success(true);
        }
        if (messageFieldDTO.getAccessToken() == null) {
            messageFieldDTO.setAccessToken(message.getAccessToken());
        }
        if (messageFieldDTO.getDatabaseId() == null) {
            log.warn("消息数据库ID为空, unionId={}", loginUser.getUnionId());
            messageFieldDTO.setDatabaseId(message.getDatabaseId());
        }
        if (messageFieldDTO.getUnionId() == null) {
            log.warn("unionId为空, unionId={}", loginUser.getUnionId());
            messageFieldDTO.setUnionId(loginUser.getUnionId());
        }
        message.setConfigJson(JSON.toJSONString(messageFieldDTO));
        message.setDatabaseId(messageFieldDTO.getDatabaseId());
        userMessageService.updateById(message);
        return Result.success(true);
    }

    
}
