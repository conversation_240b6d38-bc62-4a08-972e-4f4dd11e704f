package com.maixi.road.admin.manager;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.maixi.road.common.business.order.enums.OrderStatusEnum;
import com.maixi.road.common.core.model.response.OrderVo;
import com.maixi.road.admin.biz.domain.Orders;
import com.maixi.road.admin.biz.domain.OssConfig;
import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.service.INotionResourceService;
import com.maixi.road.admin.biz.service.IOrdersService;
import com.maixi.road.admin.biz.service.IOssConfigService;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.framework.config.CacheManager;
import com.maixi.road.framework.config.RedisManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class JobManager {

    @Resource
    private RedisManager redisManager;
    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private INotionResourceService notionResourceService;
    @Resource
    private IOrdersService ordersService;
    @Resource
    private OrderManager orderManager;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IOssConfigService ossConfigService;

    /**
     * 每天 6 点执行一次
     */
    @Scheduled(cron = "0 0 6 * * ?")
    public void orderCheckTask() throws InterruptedException {
        log.info("检查七天前的未支付订单支付情况");
        boolean open = notionResourceService.openOrderCheckJob();
        if (!open) {
            log.info("未开启 orderCheckTask 检查任务");
        }

        LambdaQueryWrapper<Orders> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Orders::getStatus, OrderStatusEnum.NOTPAY.getStatus())
                .le(Orders::getCreateTime, LocalDateTime.now().minusDays(7));
        List<Orders> orders = ordersService.list(queryWrapper);
        if (CollectionUtils.isEmpty(orders)) {
            log.info("无未支付订单");
            return;
        }
        for (Orders order : orders) {
            try {
                log.info("检查订单支付情况，订单号：{}", order.getOrderNo());
                OrderVo orderVo = orderManager.queryOrder(order.getOrderNo(), order.getUnionId());
                log.info("订单查询结果：{}", JSON.toJSONString(orderVo));
            } catch (Exception e) {
                log.error("订单查询异常，订单号：{}", order.getOrderNo(), e);
            }
            Thread.sleep(100);
        }
    }

    /**
     * 每天 1 点执行一次
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void statisticDataPersistenceTask() {
        QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("union_id", "id");
        List<UserInfo> userInfos = userInfoService.list(queryWrapper);
        if (CollectionUtils.isEmpty(userInfos)) {
            return;
        }
        for (UserInfo userInfo : userInfos) {
            Integer textSum = redisManager.getTextSumByUnionIdFromCache(userInfo.getUnionId());
            Integer articleSum = redisManager.getArticleSumByUnionIdFromCache(userInfo.getUnionId());
            userInfoService.updateStatisticDataById(textSum, articleSum, userInfo.getId());
            redisManager.clearTextSumByUnionId(userInfo.getUnionId());
            redisManager.clearArticleSumByUnionId(userInfo.getUnionId());
        }
    }

    public void processOssConfigRegion() {
        List<OssConfig> ossConfigs = ossConfigService.list();
        if (CollectionUtils.isEmpty(ossConfigs)) {
            return;
        }
        for (OssConfig ossConfig : ossConfigs) {
            String region = ossConfig.getRegion();
            if (StringUtils.isNotBlank(region)) {
                continue;
            }
            if ("r2".equals(ossConfig.getType())) {
                // 从 endpoint(https://1991125856666c8f18ac439df183cfaa.r2.cloudflarestorage.com)
                // 中获取 region
                // region 是 1991125856666c8f18ac439df183cfaa
                String prefix = "https://";
                String suffix = ".r2.cloudflarestorage.com";
                int startIndex = prefix.length();
                int endIndex = ossConfig.getEndpoint().indexOf(suffix);
                if (endIndex > startIndex) {
                    region = ossConfig.getEndpoint().substring(startIndex, endIndex);
                }
                ossConfig.setRegion(region);
                log.info("更新 ossConfig 的 region 为 {},endpoint={}", region, ossConfig.getEndpoint());
                ossConfigService.updateRegionAndEndpoint(ossConfig.getId(), region, ossConfig.getEndpoint());
            } else if ("oss".equals(ossConfig.getType())) {
                // oss-cn-hangzhou.aliyuncs.com
                // region 是 cn-hangzhou
                if (!ossConfig.getEndpoint().startsWith("https://")) {
                    ossConfig.setEndpoint("https://" + ossConfig.getEndpoint());
                }
                String prefix = "https://oss-";
                String suffix = ".aliyuncs.com";
                int startIndex = prefix.length();
                int endIndex = ossConfig.getEndpoint().indexOf(suffix);
                if (endIndex > startIndex) {
                    region = ossConfig.getEndpoint().substring(startIndex, endIndex);
                }
                ossConfig.setRegion(region);
                log.info("更新 ossConfig 的 region 为 {},endpoint={}", region, ossConfig.getEndpoint());
                ossConfigService.updateRegionAndEndpoint(ossConfig.getId(), region, ossConfig.getEndpoint());
            } else if ("s3".equals(ossConfig.getType())) {
                // us-west-1.amazonaws.com
                // region 是 us-west-1
                if (!ossConfig.getEndpoint().startsWith("https://s3.")) {
                    ossConfig.setEndpoint("https://s3." + ossConfig.getEndpoint());
                }
                String prefix = "https://s3.";
                String suffix = ".amazonaws.com";
                int startIndex = prefix.length();
                int endIndex = ossConfig.getEndpoint().indexOf(suffix);
                if (endIndex > startIndex) {
                    region = ossConfig.getEndpoint().substring(startIndex, endIndex);
                }
                ossConfig.setRegion(region);
                log.info("更新 ossConfig 的 region 为 {},endpoint={}", region, ossConfig.getEndpoint());
                ossConfigService.updateRegionAndEndpoint(ossConfig.getId(), region, ossConfig.getEndpoint());
            } else if ("cos".equals(ossConfig.getType())) {
                // cos.ap-guangzhou-internal.myqcloud.com
                // region 是 ap-guangzhou-internal
                if (!ossConfig.getEndpoint().startsWith("https://cos.")) {
                    ossConfig.setEndpoint("https://cos." + ossConfig.getEndpoint());
                }
                String prefix = "https://cos.";
                String suffix = ".myqcloud.com";
                int startIndex = prefix.length();
                int endIndex = ossConfig.getEndpoint().indexOf(suffix);
                if (endIndex > startIndex) {
                    region = ossConfig.getEndpoint().substring(startIndex, endIndex);
                }
                ossConfig.setRegion(region);
                log.info("更新 ossConfig 的 region 为 {},endpoint={}", region, ossConfig.getEndpoint());
                ossConfigService.updateRegionAndEndpoint(ossConfig.getId(), region, ossConfig.getEndpoint());
            }
        }
        log.info("更新 ossConfig 的 region 完成");
    }

}
