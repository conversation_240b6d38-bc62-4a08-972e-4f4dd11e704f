package com.maixi.road.admin.manager;

import static com.maixi.road.common.core.enums.error.ErrorCodeEnum.BIZ_ERROR;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

import org.jetbrains.annotations.NotNull;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.maixi.road.admin.biz.domain.UserMessage;
import com.maixi.road.admin.biz.service.ICustomConfigService;
import com.maixi.road.admin.biz.service.INotionResourceService;
import com.maixi.road.admin.biz.service.IUserArticleService;
import com.maixi.road.admin.biz.service.IUserMessageService;
import com.maixi.road.clipper.ClipperApi;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.ClipperResponse;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.core.model.dto.TextMessageRequest;
import com.maixi.road.common.core.model.request.MessageSyncParam;
import com.maixi.road.common.core.utils.AssertUtils;
import com.maixi.road.common.core.utils.UrlUtils;
import com.maixi.road.common.service.redis.constant.RedisKeys;
import com.maixi.road.framework.annotation.CountLimit;
import com.maixi.road.framework.config.CacheManager;
import com.maixi.road.framework.config.RedisManager;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ClipperManager {

    @Resource
    private UserManager userManager;
    @Resource
    private ClipperApi clipperApi;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisManager redisManager;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private IUserArticleService userArticleService;
    @Resource
    private IUserMessageService userMessageService;
    @Resource
    private ICustomConfigService customConfigService;
    @Resource
    private INotionResourceService notionResourceService;

    @NotNull
    private static String processDoubanDispatch(String url) {
        if (url.startsWith("https://www.douban.com/doubanapp/dispatch")) {
            String decodeUrl = null;
            decodeUrl = URLDecoder.decode(url, StandardCharsets.UTF_8);
            url = decodeUrl.replace("/doubanapp/dispatch?uri=", "");
            // 找到 '&' 的位置并截取前面的部分
            int ampersandIndex = url.indexOf("&");
            url = (ampersandIndex != -1) ? url.substring(0, ampersandIndex) : url;
            log.info("url:{}", url);
        }
        if (url.contains("https://www.douban.com/doubanapp/dispatch/review/")) {
            url = url.replace("https://www.douban.com/doubanapp/dispatch/review/", "https://movie.douban.com/review/");
            log.info("url:{}", url);
        }
        return url;
    }

    /**
     *
     * 记录 unionId 的解析次数
     *
     * @param url
     * @param unionId
     * @return
     */
    @CountLimit(countType = "resolve_count")
    public ResolveRS resolve(String url, String unionId) {
        log.info("resolve url:{}", url);
        // 处理豆瓣的 dispatch
        url = processDoubanDispatch(url);

        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
        }
        ClipperResponse<ResolveRS> resolve = clipperApi.parseUrl(url, unionId);
        if (resolve == null || resolve.getData() == null) {
            log.error("解析失败, url={}", url);
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR);
        }
        return resolve.getData();
    }

    /**
     *
     * @param param
     * @param unionId
     * @return
     */
    @CountLimit(countType = "save_count")
    public Boolean submit(ResolveFormRQ param, String unionId) {
        ClipperResponse<Boolean> submitArticle = clipperApi.saveContent(param, unionId);
        if (!submitArticle.isSuccess()) {
            log.error("创建 notion 页面失败, msg={},url={},urlKey={}", submitArticle.getMessage(), param.getLink(),
                    UrlUtils.generateMD5HexString(param.getLink()));
            throw RoadException.create(BIZ_ERROR, submitArticle.getMessage());
        }
        redisManager.articleSumIncrementOne(unionId);
        return true;
    }

    @NotNull
    private ResolveRS getResolveVoFromRedissonCache(ResolveFormRQ param, String unionId) {
        ResolveRS resolveVo = redissonClient
                .<ResolveRS>getBucket(RedisKeys.getArticleBlocksKey(param.getLink())).get();
        if (resolveVo != null) {
            return resolveVo;
        }

        long startTime = System.currentTimeMillis();
        long timeout = 60000; // 60 seconds timeout

        while (System.currentTimeMillis() - startTime < timeout) {
            if (!redisManager.transformPictureProcessing(unionId)) {
                break;
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待过程被中断", e);
                break;
            }
        }
        resolveVo = redissonClient.<ResolveRS>getBucket(RedisKeys.getArticleBlocksKey(param.getLink())).get();
        if (resolveVo == null) {
            throw RoadException.create(BIZ_ERROR, "文章内容解析失败，请稍后重试");
        }
        return resolveVo;
    }

    @CountLimit(countType = "save_count")
    public String syncMessage(MessageSyncParam param, String unionId) {
        UserMessage userMessage = userMessageService.getByUnionId(unionId);
        check(userMessage);
        TextMessageRequest request = new TextMessageRequest();
        request.setContent(param.getContent());
        ClipperResponse<String> syncTextMessage = clipperApi.saveText(request, unionId);
        if (!syncTextMessage.isSuccess()) {
            throw RoadException.create(BIZ_ERROR, syncTextMessage.getMessage());
        }
        return syncTextMessage.getData();
    }

    private void check(UserMessage userMessage) {
        AssertUtils.notNullWithBizExp(userMessage, "您还未关联消息数据库");
        AssertUtils.notNullWithBizExp(userMessage.getAccessToken(), "您还未关联消息数据库");
        AssertUtils.notNullWithBizExp(userMessage.getDatabaseId(), "您还未关联消息数据库");
    }

}
