package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.GenericResolverFailLogMapper;
import com.maixi.road.admin.biz.domain.GenericResolverFailLog;
import com.maixi.road.admin.biz.service.IGenericResolverFailLogService;

import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class GenericResolverFailLogServiceImpl extends ServiceImpl<GenericResolverFailLogMapper, GenericResolverFailLog> implements IGenericResolverFailLogService {

}
