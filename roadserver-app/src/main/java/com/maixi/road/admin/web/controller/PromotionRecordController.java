package com.maixi.road.admin.web.controller;

import com.maixi.road.admin.biz.service.IPromotionRecordService;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.response.PromotionData;
import com.maixi.road.common.core.model.response.PromotionVo;

import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 推广记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */

@RestController
@RequestMapping("/miniprogram")
public class PromotionRecordController extends BaseController {

    @Resource
    private IPromotionRecordService promotionRecordService;


    /**
     * 积分明细
     * @return
     */
    @GetMapping("/creditList")
    public Result<List<PromotionVo>> creditList() {
        return Result.success(promotionRecordService.creditListByUnionId(getLoginUnionId()));
    }

    /**
     * 推广数据
     * @return
     */
    @GetMapping("/getPromotionData")
    public Result<PromotionData> promotionData() {
        return Result.success(promotionRecordService.promotionData(getLoginUnionId()));
    }
}
