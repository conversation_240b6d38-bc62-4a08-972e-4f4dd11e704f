package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.CustomConfigMapper;
import com.maixi.road.admin.biz.domain.CustomConfig;
import com.maixi.road.admin.biz.service.ICustomConfigService;

import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class CustomConfigServiceImpl extends ServiceImpl<CustomConfigMapper, CustomConfig> implements ICustomConfigService {

    @Override
    public CustomConfig getCustomConfigByUnionId(String unionId) {
        LambdaQueryWrapper<CustomConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomConfig::getUnionId, unionId)
                .eq(CustomConfig::getDeleted, 0)
                .orderByDesc(CustomConfig::getId)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public Optional<CustomConfig> getCustomConfigOptByUnionId(String unionId) {
        LambdaQueryWrapper<CustomConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomConfig::getUnionId, unionId)
                .eq(CustomConfig::getDeleted, 0)
                .orderByDesc(CustomConfig::getId)
                .last("LIMIT 1");
        return this.getOneOpt(queryWrapper);
    }
    
}
