package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("notion_resource")
public class NotionResource {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 0,图片
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 编码,wx_group_qrcode:微信群二维码
     */
    @TableField("`code`")
    private String code;

    @TableField("resource")
    private String resource;

    /**
     * 描述
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 0,有效,1,无效
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
