package com.maixi.road.admin.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.UserMessageMapper;
import com.maixi.road.admin.biz.domain.UserMessage;
import com.maixi.road.admin.biz.service.IUserMessageService;
import com.maixi.road.admin.manager.UserManager;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.Field;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.core.model.response.UserMessageDto;
import com.maixi.road.common.integration.notion.enums.NotionTypeEnum;

import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class UserMessageServiceImpl extends ServiceImpl<UserMessageMapper, UserMessage> implements IUserMessageService {

    @Resource
    private UserManager userManager;

    @Override
    public UserMessageDto queryMessageDatabaseInfo(String unionId) {
        LambdaQueryWrapper<UserMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMessage::getUnionId, unionId)
                .orderByDesc(UserMessage::getId)
                .last("LIMIT 1");
        UserMessage userMessage = this.getOne(queryWrapper);
        if (userMessage == null) {
            throw RoadException.create(ErrorCodeEnum.USER_UNAUTHORIZED, "您还未授权绑定 Notion");
        }
        return UserMessageDto.builder()
                .databaseId(userMessage.getDatabaseId())
                .tag(userMessage.getTag())
                .type(userMessage.getType())
                .createTime(userMessage.getCreateTime())
                .build();
    }

    @Override
    public MessageFieldDTO queryMessageFieldDto(String unionId) {
        UserMessage message = getByUnionId(unionId);
        if (message == null) {
            MessageFieldDTO messageFieldDTO = new MessageFieldDTO();
            messageFieldDTO.setDatabaseId(null);
            messageFieldDTO.setAccessToken(null);
            messageFieldDTO.setUnionId(unionId);
            messageFieldDTO.setType(Field.builder().supportTypes(NotionTypeEnum.categoryType()).build());
            messageFieldDTO.setTags(Field.builder().supportTypes(NotionTypeEnum.tagsType()).build());
            messageFieldDTO.setCreateTime(Field.builder().supportTypes(NotionTypeEnum.createdTimeType()).build());
            return messageFieldDTO;
        }
        String configJson = message.getConfigJson();
        if (configJson == null || configJson.isEmpty()) {
            // 初始化历史数据。
            return MessageFieldDTO.builder()
                    .databaseId(message.getDatabaseId())
                    .unionId(unionId)
                    .accessToken(message.getAccessToken())
                    .type(Field.builder().name(message.getType()).type("select").supportTypes(NotionTypeEnum.categoryType()).build())
                    .tags(Field.builder().name(message.getTag()).type("multi_select").supportTypes(NotionTypeEnum.tagsType()).build())
                    .createTime(Field.builder().name(message.getCreateTime()).type("created_time").supportTypes(NotionTypeEnum.createdTimeType()).build())
                    .build();
        }
        MessageFieldDTO messageFieldDTO = JSON.parseObject(configJson, MessageFieldDTO.class);
        // FIXME 兼容历史数据
        if (StringUtils.isBlank(messageFieldDTO.getAccessToken())) {
            messageFieldDTO.setAccessToken(message.getAccessToken());
        }
        if (StringUtils.isBlank(messageFieldDTO.getDatabaseId())) {
            messageFieldDTO.setDatabaseId(message.getDatabaseId());
        }
        if (StringUtils.isBlank(messageFieldDTO.getUnionId())){
            messageFieldDTO.setUnionId(unionId);
        }
        return messageFieldDTO;
    }


    @Override
    public UserMessage getByUnionId(String unionId) {
        LambdaQueryWrapper<UserMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserMessage::getUnionId, unionId)
                .orderByDesc(UserMessage::getId)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public void removeByUnionId(String loginUnionId) {
        this.remove(new LambdaQueryWrapper<UserMessage>().eq(UserMessage::getUnionId, loginUnionId));
    }

}
