package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.WxUserCp;

import java.util.List;

/**
 * <p>
 * 微信的fromUser标识与unionId的关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IWxUserCpService extends IService<WxUserCp> {

    List<WxUserCp> getByWxUserIdList(List<String> fromUserIdList);

    WxUserCp getWxUserByUnionId(String unionId);
}
