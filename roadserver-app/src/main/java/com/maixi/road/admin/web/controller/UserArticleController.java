package com.maixi.road.admin.web.controller;

import com.maixi.road.admin.biz.service.IUserArticleService;
import com.maixi.road.admin.manager.UserManager;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.response.UserArticleDto;

import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/miniprogram")
public class UserArticleController extends BaseController{

    @Resource
    private IUserArticleService userArticleService;
    @Resource
    private UserManager userManager;


    @GetMapping("queryArticleDatabaseInfo")
    public Result<UserArticleDto> queryArticleDatabaseInfo() {
        return Result.success(userArticleService.queryArticleDatabaseInfo(getMainUserWrapper().getMainUnionId()));
    }


//    @PostMapping("updateArticleDatabaseInfo")
//    @DistributedLock(lockTime = 3, waitTime = 0)
//    public Result<Boolean> updateArticleDatabaseInfo(@RequestBody UserArticleDto userArticleDto) {
//        MainUserWrapper mainUserWrapper = getMainUserWrapper();
//        if (!mainUserWrapper.isMainUser()) {
//            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
//        }
//        return Result.success(userArticleService.updateArticleDatabaseInfo(mainUserWrapper.getMainUnionId(),userArticleDto));
//    }
}
