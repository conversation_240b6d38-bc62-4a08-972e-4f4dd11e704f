package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.WxMessageProcessErrorMapper;
import com.maixi.road.admin.biz.domain.WxMessageProcessError;
import com.maixi.road.admin.biz.service.IWxMessageProcessErrorService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 企业微信消息处理失败记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
@Service
public class WxMessageProcessErrorServiceImpl extends ServiceImpl<WxMessageProcessErrorMapper, WxMessageProcessError> implements IWxMessageProcessErrorService {

}
