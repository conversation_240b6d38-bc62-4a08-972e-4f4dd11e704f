package com.maixi.road.admin.manager;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import com.maixi.road.common.core.utils.UrlUtils;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.redis.api.RedisOpApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class RedisOpApiImpl implements RedisOpApi {

    @Resource
    private RedissonClient redissonClient;

    @Override
    public void saveClipResult(String url, List<Block> blocks) {
        String key = UrlUtils.generateMD5HexString(url);
        log.info("saveClipResult: key={},url={}, blocks.size={}", key, url, blocks.size());
        redissonClient.<List<Block>>getBucket(key).set(blocks, Duration.ofMinutes(10));
    }

    @Override
    public Future<List<Block>> getClipResult(String url) {
        String key = UrlUtils.generateMD5HexString(url);
        log.info("start getClipResult: key={},url={}", key, url);
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            long timeout = 10000;
            int count = 0;

            while (System.currentTimeMillis() - startTime < timeout) {
                // 每次创建新的bucket对象，确保没有缓存
                RBucket<List<Block>> bucket = redissonClient.getBucket(key);
                List<Block> result = bucket.get();
                if (result != null) {
                    log.info("Got clip result spent {}ms with key: {}, count={}",
                            System.currentTimeMillis() - startTime, key, count);
                    return result;
                }
                log.info("getClipResult: key={},url={}, result is null, count={}", key, url, count);
                count++;

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Interrupted while waiting for clip result", e);
                    break;
                }
            }

            log.info("Timeout waiting for clip result with key: {}", key);
            return Collections.emptyList();
        });
    }

}
