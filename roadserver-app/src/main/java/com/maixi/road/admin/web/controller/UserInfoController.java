package com.maixi.road.admin.web.controller;

import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.integration.notion.enums.ActionEnum;
import com.maixi.road.framework.annotation.DistributedLock;
import com.maixi.road.framework.config.CacheManager;
import com.maixi.road.framework.config.RedisManager;
import com.maixi.road.framework.web.LoginUser;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.response.ClipDataVO;
import com.maixi.road.common.core.model.response.DataVo;
import com.maixi.road.common.core.model.response.UserSimpleInfo;
import com.maixi.road.common.core.model.response.UserVo;
import com.maixi.road.framework.web.Road;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@RestController
@RequestMapping("/miniprogram")
public class UserInfoController extends BaseController {

    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private RedisManager redisManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CacheManager cacheManager;

    @GetMapping("getUserInfo")
    public Result<UserVo> getUserInfo(HttpServletRequest request) {
        String appId = request.getHeader("X-App-Id");
        if (StringUtils.isBlank(appId)) {
            appId = "wx34b2cfe3fdbc3b61";
        }
        return Result.success(userInfoService.getUserInfo(getLoginUnionId(), appId));
    }

    @GetMapping("getUserData")
    public Result<DataVo> getUserData() {
        String loginUnionId = getLoginUnionId();
        DataVo dataVo = new DataVo();
        Integer msgCount = redisManager.getTextSumByUnionId(loginUnionId);
        Integer articleCount = redisManager.getArticleSumByUnionId(loginUnionId);
        Integer shareCount = 0;
        dataVo.setMsgCount(msgCount);
        dataVo.setArticleCount(articleCount);
        dataVo.setShareCount(shareCount);
        return Result.success(dataVo);
    }

    @GetMapping("getUserClipData")
    public Result<ClipDataVO> getUserClipData() {
        LoginUser loginUser = Road.getLoginUser();
        ClipDataVO dataVo = new ClipDataVO();
        int dayCount = cacheManager.getDailyCount(loginUser.getUserId(), ActionEnum.ARTICLE_SAVE);
        int monthCount = cacheManager.getMonthlyCount(loginUser.getUserId(), ActionEnum.ARTICLE_SAVE);
        Integer totalCount = redisManager.getArticleSumByUnionId(loginUser.getUnionId());
        dataVo.setCurDayCount(dayCount);
        dataVo.setCurMonthCount(monthCount);
        dataVo.setTotalCount(totalCount);
        return Result.success(dataVo);
    }

    @GetMapping("checkSession")
    public Result<Boolean> checkSession() {
        try {
            Optional<UserInfo> userInfoOpt = userInfoService.getUserInfoOptByUnionId(getLoginUnionId());
            // 没有openid 也不行,还有一种情况是两个小程序的 openid 不一致，会手动清空 openid
            if (userInfoOpt.isEmpty() || StringUtils.isBlank(userInfoOpt.get().getOpenId())) {
                return Result.success(false);
            }
        } catch (Exception e) {
            return Result.success(false);
        }
        return Result.success(true);
    }

    @PostMapping("updateUserInfo")
    @DistributedLock(lockTime = 3, waitTime = 0)
    public Result<UserSimpleInfo> updateUserInfo(@RequestBody UserSimpleInfo param) {
        return Result.success(userInfoService.updateUserInfo(getLoginUnionId(), param));
    }

    @PostMapping("/image/avatar/upload")
    public Result<String> uploadAvatar(HttpServletRequest request, @RequestParam("file") MultipartFile file)
            throws IOException {
        String unionId = request.getParameter("unionId");
        String lockKey = "rLock:uploadAvatar:" + unionId;
        RLock lock = redissonClient.getLock(lockKey);
        if (lock == null) {
            throw RoadException.create(ErrorCodeEnum.USER_OPERATION_FREQUENCY);
        }
        try {
            boolean getLock = lock.tryLock(0, 3, TimeUnit.SECONDS);
            if (getLock) {
                log.info("[分布式锁加锁]: key:{}", lockKey);
                if (file == null) {
                    throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "头像文件为空");
                }
                return Result.success(userInfoService.uploadAvatar(file, unionId));
            }
            log.info("[用户操作过于频繁]: key:{}", lockKey);
            throw RoadException.create(ErrorCodeEnum.USER_OPERATION_FREQUENCY);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("[用户操作获取锁失败]: key:{}", lockKey);
            throw RoadException.create(ErrorCodeEnum.USER_OPERATION_FREQUENCY);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                log.info("[分布式锁释放]: key:{}", lockKey);
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("释放锁失败,msg={}", e.getMessage());
                }
            }
        }
    }
}
