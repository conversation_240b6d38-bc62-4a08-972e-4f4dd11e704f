package com.maixi.road.admin.biz.service.impl;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.NotionRelationMapper;
import com.maixi.road.admin.biz.domain.NotionRelation;
import com.maixi.road.admin.biz.domain.UserArticle;
import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.domain.UserMessage;
import com.maixi.road.admin.biz.service.INotionRelationService;
import com.maixi.road.admin.biz.service.IUserArticleService;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.admin.biz.service.IUserMessageService;
import com.maixi.road.common.core.model.dto.ArticleFieldDTO;
import com.maixi.road.common.core.model.dto.Field;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.notion.remote.NotionClient;
import com.maixi.road.notion.remote.dto.response.AuthenticationRS;

import cn.hutool.core.lang.Assert;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class NotionRelationServiceImpl extends ServiceImpl<NotionRelationMapper, NotionRelation> implements INotionRelationService {

    @Resource
    private NotionClient notionClient;
    @Resource
    private IUserArticleService userArticleService;
    @Resource
    private IUserMessageService userMessageService;
    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private ScheduledExecutorService scheduledExecutorService;

    @Override
    public NotionRelation getRelationByUnionId(String unionId) {
        LambdaQueryWrapper<NotionRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionRelation::getUnionId, unionId)
                .eq(NotionRelation::getDeleted, 0)
                .orderByDesc(NotionRelation::getId)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean checkAuth(String unionId, boolean withDeleteUnavailableRelation) {
        NotionRelation relation = getRelationByUnionId(unionId);
        if (relation == null) {
            return false;
        }
        // 授权是否已完成
        Pair<String, String> article_MsgDbId = searchAndCreateUserArticleAndMsgDb(unionId, relation.getAccessToken(), relation.getId());
        if (article_MsgDbId != null && article_MsgDbId.getKey() != null && article_MsgDbId.getValue() != null) {
            return true;
        }
        // 授权未完成，删除，重新进行授权
        if (withDeleteUnavailableRelation) {
            log.warn("checkAuth withDeleteUnavailableRelation  授权未完成，删除，重新进行授权, unionId={}, relationId={}", unionId, relation.getId());
            LambdaUpdateChainWrapper<NotionRelation> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
            lambdaUpdateChainWrapper
                    .eq(NotionRelation::getId, relation.getId())
                    .set(NotionRelation::getDeleted, 1)
                    .set(NotionRelation::getUpdateTime, LocalDateTime.now())
                    .update();
        }
        return false;
    }

    @Override
    public boolean authCallback(String code, String unionId) throws IOException {
        Assert.notBlank(unionId, "授权用户 id 不能为空");
        Assert.notBlank(code, "授权码不能为空串");

        // 调用 notion api createToken
        AuthenticationRS authenticationResponse = notionClient.createToken(code);

        String accessToken = authenticationResponse.getAccessToken();

        LambdaQueryWrapper<NotionRelation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(NotionRelation::getUnionId, unionId)
                .eq(NotionRelation::getAccessToken, accessToken)
                .eq(NotionRelation::getDeleted, 0)
                .orderByDesc(NotionRelation::getId)
                .last("LIMIT 1")
        ;
        NotionRelation relation = this.getOne(lambdaQueryWrapper);
        if (relation == null) {
            relation = new NotionRelation();
            relation.setUnionId(unionId);
            relation.setArticleDbId(null);
            relation.setTextDbId(null);
            relation.setAccessToken(accessToken);
            relation.setExtraInfo(JSONObject.toJSONString(authenticationResponse));
            relation.setCreateTime(LocalDateTime.now());
            this.save(relation);
        }
        Integer relationId = relation.getId();

        scheduledExecutorService.schedule(() -> searchAndCreateUserArticleAndMsgDb(unionId, accessToken, relationId), 1, TimeUnit.MINUTES);
        return true;
    }

    @Override
    public boolean checkUserOrMainUserHasBindNotion(String unionId) {
        UserInfo mainUser = userInfoService.getUserInfoBySubUnionId(unionId);
        if (mainUser != null) {
            return checkUserHasBindNotion(mainUser.getUnionId());
        }
        return checkUserHasBindNotion(unionId);
    }

    @Override
    public boolean removeAuth(String loginUnionId) {
        removeNotionRelationByUnionId(loginUnionId);
        userArticleService.removeByUnionId(loginUnionId);
        userMessageService.removeByUnionId(loginUnionId);
        return true;
    }

    private void removeNotionRelationByUnionId(String loginUnionId) {
        LambdaUpdateChainWrapper<NotionRelation> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        lambdaUpdateChainWrapper
                .eq(NotionRelation::getUnionId, loginUnionId)
                .set(NotionRelation::getDeleted, 1)
                .set(NotionRelation::getUpdateTime, LocalDateTime.now())
                .update();
    }

    /**
     * 检查用户是否已绑定 Notion
     * 优化逻辑：先检查业务表记录，再检查关系表
     * 
     * @param unionId 用户唯一标识
     * @return 是否已绑定
     */
    private boolean checkUserHasBindNotion(String unionId) {
        // 先检查 user_article 表是否存在记录
        // 如果 user_article 表有记录，说明授权已完成，直接返回 true
        UserArticle userArticle = userArticleService.queryArticle(unionId);
        if (userArticle != null) {
            log.debug("用户已完成 Notion 授权，unionId={}", unionId);
            return true;
        }

        // 如果业务表记录不完整，继续原有的 relation 检查逻辑
        NotionRelation relation = getRelationByUnionId(unionId);
        if (relation == null) {
            log.debug("用户未找到 Notion 关系记录，unionId={}", unionId);
            return false;
        }

        // 这里尝试再去获取一下，可能是授权流程还未完成
        if (StringUtils.isBlank(relation.getArticleDbId())) {
            log.info("用户 Notion 关系记录中 articleDbId 为空，尝试重新获取，unionId={}", unionId);
            searchAndCreateUserArticleAndMsgDb(unionId, relation.getAccessToken(), relation.getId());
            relation = getById(relation.getId());
        }

        // 实际判断是否完成授权，是根据 accessToken 是否为空
        boolean hasAuth = relation.getAccessToken() != null;
        log.debug("用户 Notion 授权检查结果，unionId={}, hasAuth={}", unionId, hasAuth);
        return hasAuth;
    }

    public Pair<String, String> searchAndCreateUserArticleAndMsgDb(String unionId, String accessToken, Integer relationId) {

        Pair<String, String> pair = notionClient.searchDb(unionId, accessToken, relationId);
        if (StringUtils.isAnyBlank(pair.getKey(), pair.getValue())) {
            return pair;
        }
        String articleDbId = pair.getKey();
        String msgDbId = pair.getValue();

        // 将用户的微信 unionId 与 用户的 notion 账号对应的数据库 ID 记录下来
        LambdaUpdateChainWrapper<NotionRelation> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        lambdaUpdateChainWrapper
                .eq(NotionRelation::getId, relationId)
                .set(NotionRelation::getTextDbId, msgDbId)
                .set(NotionRelation::getArticleDbId, articleDbId)
                .update();

        // 创建 user_article 记录
        createUserArticle(unionId, accessToken, articleDbId);

        // 创建 user_message 记录
        createUserMessage(unionId, accessToken, msgDbId);

        return Pair.of(articleDbId, msgDbId);
    }

    private void createUserMessage(String unionId, String accessToken, String msgDbId) {
        UserMessage userMessage = new UserMessage();
        userMessage.setUnionId(unionId);
        userMessage.setDatabaseId(msgDbId);
        userMessage.setAccessToken(accessToken);
        userMessage.setConfigJson(JSON.toJSONString(MessageFieldDTO.builder()
                .type(Field.builder().name("类型").type("select").build())
                .tags(Field.builder().name("标签").type("multi_select").build())
                .createTime(Field.builder().name("创建时间").type("created_time").build())
                .build()));
        userMessage.setType("类型");
        userMessage.setTag("标签");
        userMessage.setCreateTime("创建时间");
        userMessage.setVersion("2025-04-13");
        userMessage.setGmtCreate(LocalDateTime.now());
        userMessage.setGmtUpdate(LocalDateTime.now());
        userMessageService.save(userMessage);
    }

    private void createUserArticle(String unionId, String accessToken, String articleDbId) {
        UserArticle userArticle = new UserArticle();
        userArticle.setUnionId(unionId);
        userArticle.setDatabaseId(articleDbId);
        userArticle.setAccessToken(accessToken);
        userArticle.setConfigJson(JSON.toJSONString(ArticleFieldDTO.builder()
                .url(Field.builder().name("文章链接").type("url").build())
                .origin(Field.builder().name("来源").type("select").build())
                .author(Field.builder().name("作者").type("select").build())
                .tags(Field.builder().name("标签").type("multi_select").build())
                .remark(Field.builder().name("备注").type("rich_text").build())
                .createTime(Field.builder().name("创建时间").type("created_time").build())
                .publishTime(Field.builder().name("发布时间").type("date").build())
                .build()));
        userArticle.setUrl("文章链接");
        userArticle.setOrigin("来源");
        userArticle.setAuthor("作者");
        userArticle.setTag("标签");
        userArticle.setCreateTime("创建时间");
        userArticle.setPublishTime("发布时间");
        userArticle.setRemark("备注");
        userArticle.setNeedContent(0);
        userArticle.setVersion("2025-04-13");
        userArticle.setGmtCreate(LocalDateTime.now());
        userArticle.setGmtUpdate(LocalDateTime.now());
        userArticleService.save(userArticle);
    }

}
