package com.maixi.road.admin.web.controller;

import com.alibaba.fastjson.JSON;
import com.maixi.road.admin.manager.ClipperManager;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.request.MessageSyncParam;
import com.maixi.road.common.core.model.request.UrlParam;
import com.maixi.road.common.core.utils.RequestUtil;
import com.maixi.road.framework.annotation.DistributedLock;
import com.maixi.road.framework.config.RedisManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/miniprogram/")
public class ClipperController extends BaseController {

    @Resource
    private ClipperManager clipperManager;
    @Resource
    private RedisManager redisManager;


    @PostMapping("/resolve")
    @DistributedLock(lockTime = 3, waitTime = 0)
    public Result<ResolveRS> resolve(@Valid @RequestBody UrlParam urlParam) {
        return Result.success(clipperManager.resolve(urlParam.getUrl(), getMainUserWrapper().getMainUnionId()));
    }


    @PostMapping("/submit")
    @DistributedLock(lockTime = 3, waitTime = 0)
    public Result<Boolean> submit(@Valid @RequestBody ResolveFormRQ param) {
        if (StringUtils.isBlank(param.getLink())) {
            log.warn("文章链接为空:{}",JSON.toJSONString(param));
            return Result.fail("文章解析失败，请重新解析");
        }
        if (StringUtils.isBlank(param.getTitle())) {
            log.warn("文章链接已失效:{}", JSON.toJSONString(param));
            return Result.fail("文章链接已失效，请重新解析");
        }
        String domain = RequestUtil.getDomainFromUrl(param.getLink());
        if (StringUtils.isBlank(param.getAuthor())) {
            param.setAuthor(domain);
        }
        if (StringUtils.isBlank(param.getOrigin())) {
            param.setOrigin(domain);
        }
        if (StringUtils.isBlank(param.getSiteName())) {
            param.setSiteName(domain);
        }
        if (!CollectionUtils.isEmpty(param.getTags())) {
            String longTag = param.getTags().stream().filter(tag -> tag.length() > 20).findAny().orElse(null);
            if (longTag != null) {
                log.warn("标签过长:{}", JSON.toJSONString(param));
                return Result.fail("标签过长");
            }
        }
        return Result.success(clipperManager.submit(param, getMainUserWrapper().getMainUnionId()));
    }


    @PostMapping("/syncMessage")
    @DistributedLock(lockTime = 3, waitTime = 0)
    public Result<String> syncMessage(@Valid @RequestBody MessageSyncParam param) {
        String mainUnionId = getMainUserWrapper().getMainUnionId();
        String msg = clipperManager.syncMessage(param, mainUnionId);
        redisManager.textSumIncrementOne(mainUnionId);
        if (msg.contains("失败")) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, msg);
        }
        return Result.success(msg);
    }
}
