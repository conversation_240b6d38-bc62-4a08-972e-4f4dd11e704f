package com.maixi.road.admin.web.controller;

import com.maixi.road.admin.biz.domain.CloudinaryConfig;
import com.maixi.road.admin.biz.domain.OssConfig;
import com.maixi.road.admin.biz.service.ICloudinaryConfigService;
import com.maixi.road.admin.biz.service.IOssConfigService;
import com.maixi.road.admin.manager.ConfigManager;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.framework.annotation.DistributedLock;
import com.maixi.road.framework.web.MainUserWrapper;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.request.CloudinaryCreateRequest;
import com.maixi.road.common.core.model.response.CloudinaryConfigVo;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@RestController
@RequestMapping("/miniprogram/")
public class CloudinaryConfigController extends BaseController {

    @Resource
    private ConfigManager configManager;
    @Resource
    private ICloudinaryConfigService cloudinaryConfigService;
    @Resource
    private IOssConfigService ossConfigService;


    /**
     * 是否以绑定自定义图床
     *
     * @return true or false
     */

    @GetMapping("getHasBindCloudinary")
    public Result<Boolean> getHasBindCloudinary() {
        String mainUnionId = getMainUserWrapper().getMainUnionId();
        CloudinaryConfig cloudinaryConfig = cloudinaryConfigService.getCloudinaryConfigByUnionId(mainUnionId);
        if (cloudinaryConfig != null) {
            return Result.success(true);
        } else {
            OssConfig ossConfig = ossConfigService.getOssConfigByUnionId(mainUnionId);
            if (ossConfig != null) {
                return Result.success(true);
            } else {
                return Result.success(false);
            }
        }
    }


    /**
     * 获取 cloudinary 图床配置信息
     * @return
     */

    @GetMapping(value = {"getConfigList", "getCloudinaryConfig"})
    public Result<CloudinaryConfigVo> getCloudinaryConfig() {
        return Result.success(configManager.getCloudinaryConfig(getMainUserWrapper().getMainUnionId()));
    }


    @PostMapping("postCloudinaryConfig")
    @DistributedLock(lockTime = 3, waitTime = 0)
    public Result<Boolean> postCloudinaryConfig(@Valid @RequestBody CloudinaryCreateRequest param) {
        if (param == null
                || StringUtils.isBlank(param.getApiKey())
                || StringUtils.isBlank(param.getApiSecret())
                || StringUtils.isBlank(param.getCloudName())) {
            return Result.fail("图床配置不能留空");
        }
        MainUserWrapper mainUserWrapper = getMainUserWrapper();
        if (!mainUserWrapper.isMainUser()) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
        }
        configManager.postCloudinaryConfig(param,mainUserWrapper.getMainUnionId());
        return Result.success(true);
    }

    @GetMapping(value = {"getCloudinaryConfigV2"})
    public Result<CloudinaryConfig> getCloudinaryConfigV2() {
        return Result.success(configManager.getCloudinaryConfigV2(getMainUserWrapper().getMainUnionId()));
    }

    @GetMapping(value = {"removeCloudinaryConfig"})
    public Result<Boolean> removeCloudinaryConfig() {
        MainUserWrapper mainUserWrapper = getMainUserWrapper();
        if (!mainUserWrapper.isMainUser()) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
        }
        configManager.removeCloudinaryConfig(getMainUserWrapper().getMainUnionId());
        return Result.success(true);
    }

}
