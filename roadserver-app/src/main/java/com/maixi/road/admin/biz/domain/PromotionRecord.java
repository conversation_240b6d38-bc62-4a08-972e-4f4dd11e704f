package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 推广记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("promotion_record")
public class PromotionRecord {

    /**
     * 主键 ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 推荐人 ID
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 推广码
     */
    @TableField("promotion_code")
    private String promotionCode;

    /**
     * 受邀人 ID
     */
    @TableField("object_id")
    private String objectId;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 支付金额
     */
    @TableField("pay_amount")
    private Integer payAmount;

    /**
     * 积分
     */
    @TableField("credit")
    private Integer credit;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Integer deleted;
}
