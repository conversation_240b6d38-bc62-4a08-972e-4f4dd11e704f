package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("member")
public class Member {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 会员类型
     */
    @TableField("type")
    private Integer type;

    @TableField("union_id")
    private String unionId;

    @TableField("open_id")
    private String openId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    @TableField("vip_type")
    private Integer vipType;

    /**
     * 会员优惠码
     */
    @TableField("promotion_code")
    private String promotionCode;

    @TableField("start_time")
    private LocalDateTime startTime;

    @TableField("end_time")
    private LocalDateTime endTime;

    @TableField("remain_count")
    private Integer remainCount;

    @TableField("gmt_create")
    private Long gmtCreate;

    @TableField("gmt_update")
    private Long gmtUpdate;

    @TableField("deleted")
    private Integer deleted;
}
