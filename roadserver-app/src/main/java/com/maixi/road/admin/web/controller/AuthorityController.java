package com.maixi.road.admin.web.controller;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.maixi.road.admin.biz.service.INotionRelationService;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.integration.notion.constants.NotionIntegrations;
import com.maixi.road.framework.config.RedisManager;
import com.maixi.road.framework.web.MainUserWrapper;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
public class AuthorityController extends BaseController {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private INotionRelationService notionRelationService;

    @Resource
    private RedisManager redisManager;

    @GetMapping("/miniprogram/checkNotionBind")
    @ResponseBody
    public Result<Boolean> checkNotionBind() {
        String unionId = getLoginUnionId();
        return Result.success(notionRelationService.checkUserOrMainUserHasBindNotion(unionId));
    }

    @GetMapping("/miniprogram/removeAuth")
    @ResponseBody
    public Result<Boolean> removeAuth() {
        MainUserWrapper mainUserWrapper = getMainUserWrapper();
        if (!mainUserWrapper.isMainUser()) {
            return Result.fail("请从主账号解绑");
        }
        return Result.success(notionRelationService.removeAuth(mainUserWrapper.getMainUnionId()));
    }

    @GetMapping("/miniprogram/removeUsesData")
    @ResponseBody
    public Result<Boolean> removeUserData() {
        String loginUnionId = getLoginUnionId();
        // 例如用户的相关配置，图床，自定义配置等等
        redisManager.removeAllCacheByUnionId(loginUnionId);
        return Result.success(true);
    }

    /**
     * 获取授权的用户标识
     * 凭此标识前往 Notion 授权
     *
     * @return
     */

    @GetMapping("/notion/getAuthCode")
    @ResponseBody
    public Result<String> getAuthCode() {
        String unionId = getLoginUnionId();
        String code = NanoIdUtils.randomNanoId();
        log.info("getAuthCode, unionId={}, code={}", unionId, code);
        redissonClient.getBucket(code).set(unionId, Duration.ofMinutes(10));
        return Result.success(code);
    }

    @GetMapping("/notion/getAuthUrl")
    @ResponseBody
    public Result<String> getAuthUrl() {
        String unionId = getLoginUnionId();
        String code = NanoIdUtils.randomNanoId();
        log.info("getAuthUrl, unionId={}, code={}", unionId, code);
        redissonClient.getBucket(code).set(unionId, Duration.ofMinutes(10));
        String url = "https://www.notionmpclipper.site/notionclipper/notion/authorized?code=" + code;
        return Result.success(url);
    }

    /**
     * return
     */

    @GetMapping("/notion/authorized")
    public RedirectView authorized(@RequestParam("code") String authCode) throws UnsupportedEncodingException {
        String unionId = redissonClient.<String>getBucket(authCode).get();
        if (unionId == null) {
            log.warn("authorized, authCode is expired. authCode = {}", authCode);
            return new RedirectView("https://www.notionmpclipper.site/notionclipper/notion/codeInvalided");
        }

        RLock lock = redissonClient.getLock("NOTION_AUTHENTICATION:" + unionId + "&authorized&" + authCode);
        try {
            boolean isLock = lock.tryLock(0L, 1L, TimeUnit.MINUTES);
            if (!isLock) {
                log.warn("authorized, 授权请求过于频繁,authCode={}, unionId={}", authCode, unionId);
                return new RedirectView("https://www.notionmpclipper.site/notionclipper/notion/frequencyLimit");
            }

            boolean hasAuth = notionRelationService.checkAuth(unionId, true);
            if (hasAuth) {
                log.error("authorized, 已经授权过了, unionId={}", unionId);
                return new RedirectView("https://www.notionmpclipper.site/notionclipper/notion/success");
            }

            String state = NanoIdUtils.randomNanoId();
            redissonClient.getBucket(state).set(unionId, Duration.ofMinutes(10));
            String url = NotionIntegrations.INTEGRATION_HOST
                    .concat("?response_type=code")
                    .concat("&client_id=").concat(NotionIntegrations.CLIENT_ID)
                    .concat("&owner=user")
                    .concat("&state=").concat(state)
                    .concat("&redirect_uri=")
                    .concat(URLEncoder.encode(NotionIntegrations.REDIRECT_URI, StandardCharsets.UTF_8));
            return new RedirectView(url);

        } catch (Exception e) {
            log.error("authorized occur exception", e);
            return new RedirectView("https://www.notionmpclipper.site/notionclipper/notion/frequencyLimit");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    @GetMapping("/notion/auth/callback")
    public ModelAndView authCallback(@RequestParam("code") String code,
            @RequestParam(value = "state", required = false) String state,
            @RequestParam(value = "error", required = false) String error) throws IOException {
        log.info("authCallback：code={}, state={}, error={}", code, state, error);
        ModelAndView modelAndView = new ModelAndView();

        // 授权有错
        if (StringUtils.isNoneBlank(error)) {
            modelAndView.addObject("msg", error);
            modelAndView.setViewName("error");
            return modelAndView;
        }

        // 从缓存中获取 state -> unionId
        String unionId = getUnionIdFromCacheByState(state);
        if (StringUtils.isBlank(unionId)) {
            log.warn("authCallback 从缓存中未获取到 unionId");
            modelAndView.addObject("msg", "当前授权 token 已失效，请到小程序中确认绑定状态");
            modelAndView.setViewName("error");
            return modelAndView;
        }

        RLock lock = redissonClient.getLock("NOTION_AUTHENTICATION:" + unionId + "&callback&" + code);
        try {
            boolean isLock = lock.tryLock(0L, 1L, TimeUnit.MINUTES);
            if (!isLock) {
                log.warn("authCallback, 授权请求过于频繁,code={}, unionId={}", code, unionId);
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "授权处理中，请勿频繁刷新页面");
            }

            boolean hasAuth = notionRelationService.checkAuth(unionId, false);
            if (hasAuth) {
                log.warn("authCallback 您已授权成功，请务频繁重复操作,unionId={}", unionId);
                modelAndView.addObject("msg", "您已授权成功，请务频繁重复操作");
                modelAndView.setViewName("error");
                return modelAndView;
            }

            // 授权处理核心逻辑
            notionRelationService.authCallback(code, unionId);

            modelAndView.setViewName("auth");
            return modelAndView;
        } catch (Exception e) {
            log.error("authCallback occur exception", e);
            throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "授权处理中，请勿频繁刷新页面");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @GetMapping("/notion/termsofuse")
    @ResponseBody
    public Result<Boolean> termsOfUse() {
        // 加上调用计数
        return Result.success(true);
    }

    @GetMapping("/notion/privacypolicy")
    @ResponseBody
    public Result<Boolean> privacypolicy() {
        // 加上调用计数
        return Result.success(true);
    }

    @GetMapping("/notion/success")
    public ModelAndView success() {
        // 加上调用计数
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("auth");
        return modelAndView;
    }

    @GetMapping("/notion/frequencyLimit")
    public ModelAndView frequencyLimit() {
        // 加上调用计数
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("frequencyLimit");
        return modelAndView;
    }

    @GetMapping("/notion/codeInvalided")
    public ModelAndView codeInvalided() {
        // 加上调用计数
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("codeInvalided");
        return modelAndView;
    }

}
