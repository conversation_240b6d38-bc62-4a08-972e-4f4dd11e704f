package com.maixi.road.admin.biz.service.impl;

import com.maixi.road.admin.biz.dao.PropertyRuleMapper;
import com.maixi.road.admin.biz.domain.PropertyRule;
import com.maixi.road.admin.biz.service.IPropertyRuleService;
import com.maixi.road.common.integration.notion.enums.PropertyRuleTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 属性解析规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class PropertyRuleServiceImpl extends ServiceImpl<PropertyRuleMapper, PropertyRule> implements IPropertyRuleService {

    @Override
    public List<PropertyRule> rmRules() {
        LambdaQueryWrapper<PropertyRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PropertyRule::getType, PropertyRuleTypeEnum.REMOVE_ELEMENT.getType());
        queryWrapper.eq(PropertyRule::getDeleted, 0);
        queryWrapper.orderByAsc(PropertyRule::getPriority);
        return list(queryWrapper);
    }

    @Override
    public List<PropertyRule> titleRule() {
        LambdaQueryWrapper<PropertyRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PropertyRule::getType, PropertyRuleTypeEnum.TITLE.getType());
        queryWrapper.eq(PropertyRule::getDeleted, 0);
        queryWrapper.orderByAsc(PropertyRule::getPriority);
        return list(queryWrapper);
    }

    @Override
    public List<PropertyRule> descriptionRule() {
        LambdaQueryWrapper<PropertyRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PropertyRule::getType, PropertyRuleTypeEnum.DESCRIPTION.getType());
        queryWrapper.eq(PropertyRule::getDeleted, 0);
        queryWrapper.orderByAsc(PropertyRule::getPriority);
        return list(queryWrapper);
    }

    @Override
    public List<PropertyRule> iconRule() {
        LambdaQueryWrapper<PropertyRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PropertyRule::getType, PropertyRuleTypeEnum.ICON.getType());
        queryWrapper.eq(PropertyRule::getDeleted, 0);
        queryWrapper.orderByAsc(PropertyRule::getPriority);
        return list(queryWrapper);
    }

    @Override
    public List<PropertyRule> coverRule() {
        LambdaQueryWrapper<PropertyRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PropertyRule::getType, PropertyRuleTypeEnum.COVER.getType());
        queryWrapper.eq(PropertyRule::getDeleted, 0);
        queryWrapper.orderByAsc(PropertyRule::getPriority);
        return list(queryWrapper);
    }

    @Override
    public List<PropertyRule> authorRule() {
        LambdaQueryWrapper<PropertyRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PropertyRule::getType, PropertyRuleTypeEnum.AUTHOR.getType());
        queryWrapper.eq(PropertyRule::getDeleted, 0);
        queryWrapper.orderByAsc(PropertyRule::getPriority);
        return list(queryWrapper);
    }

    @Override
    public List<PropertyRule> siteNameRule() {
        LambdaQueryWrapper<PropertyRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PropertyRule::getType, PropertyRuleTypeEnum.SITE_NAME.getType());
        queryWrapper.eq(PropertyRule::getDeleted, 0);
        queryWrapper.orderByAsc(PropertyRule::getPriority);
        return list(queryWrapper);
    }

    @Override
    public List<PropertyRule> articleRule() {
        LambdaQueryWrapper<PropertyRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PropertyRule::getType, PropertyRuleTypeEnum.ARTICLE.getType());
        queryWrapper.eq(PropertyRule::getDeleted, 0);
        queryWrapper.orderByAsc(PropertyRule::getPriority);
        return list(queryWrapper);
    }
}
