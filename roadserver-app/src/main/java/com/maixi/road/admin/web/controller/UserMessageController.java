package com.maixi.road.admin.web.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.maixi.road.admin.biz.service.IUserMessageService;
import com.maixi.road.admin.manager.UserManager;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.response.UserMessageDto;

import jakarta.annotation.Resource;


@RestController
@RequestMapping("/miniprogram")
public class UserMessageController extends BaseController {

    @Resource
    private IUserMessageService userMessageService;
    @Resource
    private UserManager userManager;


    @GetMapping("queryMessageDatabaseInfo")
    public Result<UserMessageDto> queryMessageDatabaseInfo() {
        return Result.success(userMessageService.queryMessageDatabaseInfo(getMainUserWrapper().getMainUnionId()));
    }


//    @PostMapping("updateMessageDatabaseInfo")
//    @DistributedLock(lockTime = 3, waitTime = 0)
//    public Result<Boolean> updateMessageDatabaseInfo(
//            @RequestBody UserMessageDto userMessageDto) {
//        MainUserWrapper mainUserWrapper = getMainUserWrapper();
//        if (!mainUserWrapper.isMainUser()) {
//            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
//        }
//        return Result.success(userMessageService.updateMessageDatabaseInfo(mainUserWrapper.getMainUnionId(), userMessageDto));
//    }
}
