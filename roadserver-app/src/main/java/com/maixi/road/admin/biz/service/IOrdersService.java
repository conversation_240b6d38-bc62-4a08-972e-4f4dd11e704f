package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.Orders;

import java.time.LocalDateTime;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IOrdersService extends IService<Orders> {

    Orders getByOrderNo(String outTradeNo);

    void updateStatusByOrderNo(Integer status, LocalDateTime payTime, String orderNo);

}
