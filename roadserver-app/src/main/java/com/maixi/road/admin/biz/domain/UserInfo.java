package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("user_info")
public class UserInfo {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * unionId
     */
    @TableField("union_id")
    private String unionId;

    /**
     * openId
     */
    @TableField("open_id")
    private String openId;

    /**
     * 推荐码
     */
    @TableField("promote_code")
    private String promoteCode;

    /**
     * 介绍人
     */
    @TableField("intro_by")
    private String introBy;

    /**
     * notion token
     */
    @TableField("notion_api_key")
    private String notionApiKey;

    /**
     * 文章累计数量
     */
    @TableField("wx_article_sum")
    private Integer wxArticleSum;

    /**
     * 消息累计数量
     */
    @TableField("wx_msg_sum")
    private Integer wxMsgSum;

    /**
     * 是否激活，0 是 1 否
     */
    @TableField("active")
    private Integer active;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    /**
     * 用户昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 用户头像
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 是否点赞
     */
    @TableField("praise")
    private Integer praise;

    /**
     * 用户邮箱
     */
    @TableField("email")
    private String email;

    /**
     * vip编号
     */
    @TableField("vip_no")
    private Integer vipNo;

    /**
     * 子账号
     */
    @TableField("sub_user_id")
    private String subUserId;
}
