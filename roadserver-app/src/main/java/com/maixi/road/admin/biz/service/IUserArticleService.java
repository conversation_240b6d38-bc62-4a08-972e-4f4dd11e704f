package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.UserArticle;
import com.maixi.road.common.core.model.dto.ArticleFieldDTO;
import com.maixi.road.common.core.model.response.UserArticleDto;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IUserArticleService extends IService<UserArticle> {

    UserArticle queryArticle(String unionId);

    ArticleFieldDTO queryArticleFieldDTO(String unionId);

    UserArticleDto queryArticleDatabaseInfo(String unionId);

    void removeByUnionId(String loginUnionId);
}
