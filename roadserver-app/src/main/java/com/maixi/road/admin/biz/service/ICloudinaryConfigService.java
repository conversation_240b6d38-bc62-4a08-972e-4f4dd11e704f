package com.maixi.road.admin.biz.service;

import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.CloudinaryConfig;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface ICloudinaryConfigService extends IService<CloudinaryConfig> {

    Optional<CloudinaryConfig> getCloudinaryConfigOptByUnionId(String unionId);

    CloudinaryConfig getCloudinaryConfigByUnionId(String unionId);
    
    void deleteByUnionId(String unionId);
}
