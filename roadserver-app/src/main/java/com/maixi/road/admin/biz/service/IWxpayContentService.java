package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.WxpayContent;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IWxpayContentService extends IService<WxpayContent> {

    void updateNotifyResult(String outTradeNo, String jsonString, String transactionId);

    void updateQueryResult(String outTradeNo, String jsonString, String aNull);
}
