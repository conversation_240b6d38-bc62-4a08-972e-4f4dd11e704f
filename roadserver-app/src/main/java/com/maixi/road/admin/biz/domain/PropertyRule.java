package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 属性解析规则
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("property_rule")
public class PropertyRule {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 类型描述
     */
    @TableField("type_desc")
    private String typeDesc;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 解析表达式
     */
    @TableField("expression")
    private String expression;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Integer deleted;
}
