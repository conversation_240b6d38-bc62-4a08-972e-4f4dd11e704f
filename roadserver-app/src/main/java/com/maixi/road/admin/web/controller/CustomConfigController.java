package com.maixi.road.admin.web.controller;

import com.maixi.road.admin.manager.ConfigManager;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.framework.annotation.DistributedLock;
import com.maixi.road.framework.web.MainUserWrapper;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.request.CustomConfigCreateReq;
import com.maixi.road.common.core.model.response.CustomConfigVo;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;


/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@RestController
@RequestMapping("/miniprogram")

public class CustomConfigController extends BaseController {

    @Resource
    private ConfigManager configManager;

    @GetMapping("/getCustomConfig")
    public Result<CustomConfigVo> getCustomConfig() {
        return Result.success(configManager.getCustomConfig(getMainUserWrapper().getMainUnionId()));
    }

    @PostMapping("/postCustomConfig")
    @DistributedLock(lockTime = 3, waitTime = 0)
    public Result<Boolean> postCustomConfig(@Valid @RequestBody CustomConfigCreateReq createReq) {
        MainUserWrapper mainUserWrapper = getMainUserWrapper();
        if (!mainUserWrapper.isMainUser()) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
        }
        configManager.postCustomConfig(mainUserWrapper.getMainUnionId(), createReq);
        return Result.success(true);
    }
    

}
