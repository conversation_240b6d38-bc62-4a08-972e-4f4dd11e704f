package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("notion_relation")
public class NotionRelation {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("union_id")
    private String unionId;

    @TableField("text_db_id")
    private String textDbId;

    @TableField("article_db_id")
    private String articleDbId;

    @TableField("access_token")
    private String accessToken;

    @TableField("extra_info")
    private String extraInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 0正常 1 删除
     */
    @TableField("deleted")
    private Integer deleted;
}
