package com.maixi.road.admin.web.controller;

import com.maixi.road.admin.manager.JobManager;
import com.maixi.road.admin.web.BaseController;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/job")
public class JobController extends BaseController {

    @Resource
    private JobManager jobManager;

    @GetMapping("/processOssConfigRegion")
    public void processOssConfigRegion() {
        jobManager.processOssConfigRegion();
    }
}
