package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Optional;

import com.maixi.road.admin.biz.dao.CloudinaryConfigMapper;
import com.maixi.road.admin.biz.domain.CloudinaryConfig;
import com.maixi.road.admin.biz.service.ICloudinaryConfigService;
import com.maixi.road.common.core.utils.EncryptionUtil;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class CloudinaryConfigServiceImpl extends ServiceImpl<CloudinaryConfigMapper, CloudinaryConfig>
        implements ICloudinaryConfigService {

    @Override
    public boolean save(CloudinaryConfig entity) {
        entity.setApiSecret(EncryptionUtil.getEncryptSecret(entity.getApiSecret()));
        return super.save(entity);
    }

    @Override
    public boolean updateById(CloudinaryConfig entity) {
        entity.setApiSecret(EncryptionUtil.getEncryptSecret(entity.getApiSecret()));
        return super.updateById(entity);
    }

    @Override
    public Optional<CloudinaryConfig> getCloudinaryConfigOptByUnionId(String unionId) {
        return Optional.ofNullable(getCloudinaryConfigByUnionId(unionId));
    }

    @Override
    public CloudinaryConfig getCloudinaryConfigByUnionId(String unionId) {
        LambdaQueryWrapper<CloudinaryConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CloudinaryConfig::getUnionId, unionId)
                .orderByDesc(CloudinaryConfig::getId)
                .last("LIMIT 1");
        CloudinaryConfig config = this.getOne(queryWrapper);
        if (config != null) {
            config.setApiSecret(EncryptionUtil.getDecryptSecret(config.getApiSecret()));
        }
        return config;
    }

    @Override
    public void deleteByUnionId(String unionId) {
        LambdaQueryWrapper<CloudinaryConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CloudinaryConfig::getUnionId, unionId);
        this.remove(queryWrapper);
    }

}
