package com.maixi.road.admin.biz.service.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.UserInfoMapper;
import com.maixi.road.admin.biz.domain.Member;
import com.maixi.road.admin.biz.domain.NotionRelation;
import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.service.ICloudinaryConfigService;
import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.biz.service.INotionRelationService;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.common.business.user.enums.VipTypeEnum;
import com.maixi.road.common.core.constant.CommonConstants;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.response.UserSimpleInfo;
import com.maixi.road.common.core.model.response.UserVo;
import com.maixi.road.common.core.utils.DateUtils;
import com.maixi.road.framework.config.RedisManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.t;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements IUserInfoService {

    @Resource
    private IMemberService memberService;
    @Resource
    private INotionRelationService notionRelationService;
    @Resource
    private ICloudinaryConfigService cloudinaryConfigService;
    @Resource
    private S3Manager s3Manager;
    @Resource
    private RedisManager redisManager;

    @Override
    public UserInfo getUserInfoByUnionId(String unionId) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUnionId, unionId)
                .orderByDesc(UserInfo::getId)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public Optional<UserInfo> getUserInfoOptByUnionId(String unionId) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getUnionId, unionId)
                .orderByDesc(UserInfo::getId)
                .last("LIMIT 1");
        return this.getOneOpt(queryWrapper);
    }

    @Override
    public UserInfo getUserInfoBySubUnionId(String unionId) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getSubUserId, unionId)
                .orderByDesc(UserInfo::getId)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public Optional<UserInfo> getUserInfoOptBySubUnionId(String unionId) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfo::getSubUserId, unionId)
                .orderByDesc(UserInfo::getId)
                .last("LIMIT 1");
        return this.getOneOpt(queryWrapper);
    }

    @Override
    public UserVo getUserInfo(String unionId, String appId) {
        Optional<UserInfo> userInfoOpt = getUserInfoOptByUnionId(unionId);
        if (userInfoOpt.isEmpty()) {
            throw RoadException.create(ErrorCodeEnum.USER_NOT_REGISTER, "请重新进入小程序");
        }
        UserInfo userInfo = userInfoOpt.get();

        // 基础信息
        UserVo userVo = getUserVo(userInfo);

        // 查询会员信息
        getMember(unionId, userInfo, userVo, appId);

        // 查询是否完成notion授权
        isFinishNotionAuthorization(unionId, userVo);

        // 查询主账号
        getMainUser(unionId, userVo);

        userVo.setMsgCount(redisManager.getTextSumByUnionId(unionId));
        userVo.setArticleCount(redisManager.getArticleSumByUnionId(unionId));
        userVo.setShareCount(0);
        return userVo;
    }

    private UserVo getUserVo(UserInfo userInfo) {
        UserVo userVo = new UserVo();
        userVo.setUnionId(userInfo.getUnionId());
        userVo.setSubUserId(userInfo.getSubUserId());
        userVo.setOpenId(userInfo.getOpenId());
        userVo.setNickname(userInfo.getNickname());
        userVo.setAvatar(userInfo.getAvatar());
        userVo.setCreateTime(DateUtils.formatLocalDate(userInfo.getGmtCreate()));
        userVo.setIntroCode(userInfo.getPromoteCode());
        userVo.setPraise(userInfo.getPraise());
        userVo.setEmail(userInfo.getEmail());
        return userVo;
    }

    private void getMember(String unionId, UserInfo userInfo, UserVo userVo, String appId) {
        Integer type = 0;
        if ("wxfa42c9c1b1ea3807".equals(appId)) {
            type = 1;
        }
        Member member = memberService.selectByUnionIdAndType(unionId, type);
        if (member == null) {
            userVo.setUserType(VipTypeEnum.NORMAL.getDesc());
            userVo.setMemberType(type);
        } else {
            VipTypeEnum vipType = VipTypeEnum.getByType(member.getVipType());
            userVo.setUserType(Optional.ofNullable(vipType).orElse(VipTypeEnum.NORMAL).getDesc());
            userVo.setMemberType(member.getType());
            if (member.getEndTime() != null) {
                userVo.setEndTime(DateUtils.formatLocalDate(member.getEndTime()));
            }
            userVo.setRemainCount(member.getRemainCount());
            if (VipTypeEnum.FOREVER_VIP.equals(vipType)) {
                userVo.setMemberNo(userInfo.getVipNo());
            }
        }
    }

    private void isFinishNotionAuthorization(String unionId, UserVo userVo) {
        NotionRelation notionRelation = notionRelationService.getRelationByUnionId(unionId);
        if (notionRelation != null
                && notionRelation.getArticleDbId() != null
                && notionRelation.getAccessToken() != null) {
            userVo.setNotionAuth(true);
        }
    }

    private void getMainUser(String unionId, UserVo userVo) {
        UserInfo mainUser = getUserInfoBySubUnionId(unionId);
        if (mainUser != null) {
            // 基础信息
            UserVo mainUserVo = getUserVo(mainUser);

            Member mainUserMember = memberService.selectByUnionId(mainUser.getUnionId());
            if (mainUserMember == null) {
                mainUserVo.setUserType(VipTypeEnum.NORMAL.getDesc());
                mainUserVo.setMemberType(0);
            } else {
                VipTypeEnum vipType = VipTypeEnum.getByType(mainUserMember.getVipType());
                mainUserVo.setUserType(Optional.ofNullable(vipType).orElse(VipTypeEnum.NORMAL).getDesc());
                mainUserVo.setMemberType(mainUserMember.getType());
                if (mainUserMember.getEndTime() != null) {
                    mainUserVo.setEndTime(DateUtils.formatLocalDate(mainUserMember.getEndTime()));
                }
                mainUserVo.setRemainCount(mainUserMember.getRemainCount());
                if (VipTypeEnum.FOREVER_VIP.equals(vipType)) {
                    mainUserVo.setMemberNo(mainUser.getVipNo());
                }
            }
            userVo.setMainUserVo(mainUserVo);
        }
    }

    @Override
    public UserSimpleInfo updateUserInfo(String unionId, UserSimpleInfo param) {
        UserInfo userInfo = getUserInfoByUnionId(unionId);
        if (userInfo == null) {
            throw RoadException.create(ErrorCodeEnum.USER_NOT_REGISTER, "请重新进入小程序");
        }
        if (Objects.equals(unionId, param.getSubUserId())) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号不能与主账号相同");
        }
        UserInfo parentMainUser = getUserInfoBySubUnionId(unionId);
        if (parentMainUser != null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号不能再绑定子账号");
        }
        if (StringUtils.isNoneBlank(param.getSubUserId())) {
            UserInfo mainUser = getUserInfoBySubUnionId(param.getSubUserId());
            if (mainUser != null && !mainUser.getUnionId().equals(unionId)) {
                throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "该子账号已经绑定了其他主账号");
            }
        }
        userInfo.setNickname(param.getNickname());
        userInfo.setAvatar(param.getAvatar());
        userInfo.setEmail(param.getEmail());
        userInfo.setSubUserId(param.getSubUserId());
        this.updateById(userInfo);
        return param;
    }

    @Override
    public String uploadAvatar(MultipartFile file, String unionId) throws IOException {
        if (!s3Manager.hasImgClient(unionId)) {
            throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "请先设置图床");
        }
        String fileName = "avatar_" + NanoIdUtils.randomNanoId() + ".jpg";
        String filePath = "/home/<USER>/image/" + fileName;
        if (!CommonConstants.isLinux && !CommonConstants.isWindows) {
            filePath = "/Users/<USER>/Desktop/image/" + fileName;
        }
        File avatarFile = new File(filePath);
        file.transferTo(avatarFile);
        String targetAvatarUrl = s3Manager.uploadImgFile(unionId, avatarFile);
        avatarFile.delete();
        if (StringUtils.isBlank(targetAvatarUrl)) {
            throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "头像上传失败");
        }
        // 更新用户头像
        updateUserAvatar(unionId, targetAvatarUrl);
        return targetAvatarUrl;
    }

    private void updateUserAvatar(String unionId, String avatarUrl) {
        LambdaUpdateChainWrapper<UserInfo> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        lambdaUpdateChainWrapper
                .eq(UserInfo::getUnionId, unionId)
                .set(UserInfo::getAvatar, avatarUrl)
                .set(UserInfo::getGmtUpdate, LocalDateTime.now())
                .update();
    }

    @Override
    public List<UserInfo> getBySubUserIdList(List<String> unionIdList) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserInfo::getSubUserId, unionIdList);
        return this.list(queryWrapper);
    }

    @Override
    public void updateOpenIdByUnionId(String unionId, String openid) {
        LambdaUpdateChainWrapper<UserInfo> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        lambdaUpdateChainWrapper
                .eq(UserInfo::getUnionId, unionId)
                .set(UserInfo::getOpenId, openid)
                .update();
    }

    @Override
    public void clearOpenIdByUnionId(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            // 入参为空，不处理，要不要抛异常后面再看
            return;
        }
        LambdaUpdateChainWrapper<UserInfo> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        boolean update = lambdaUpdateChainWrapper
                .eq(UserInfo::getUnionId, unionId)
                .set(UserInfo::getOpenId, "")
                .set(UserInfo::getGmtUpdate, LocalDateTime.now())
                .update();
        log.info("clearOpenIdByUnionId {}, update={}", unionId, update);
    }

    @Override
    public void updateStatisticDataById(Integer textSum, Integer articleSum, Integer id) {
        LambdaUpdateChainWrapper<UserInfo> lambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper);
        if ((textSum == null || textSum == 0) && (articleSum == null || articleSum == 0)) {
            return;
        }
        if (textSum == null || textSum == 0) {
            log.info("更新用户同步文章次数:{}", articleSum);
            lambdaUpdateChainWrapper
                .eq(UserInfo::getId, id)
                .set(UserInfo::getWxArticleSum, articleSum)
                .set(UserInfo::getGmtUpdate, LocalDateTime.now())
                .update();
        } else if (articleSum == null || articleSum == 0) {
            log.info("更新用户同步信息次数:{}", textSum);
            lambdaUpdateChainWrapper
                    .eq(UserInfo::getId, id)
                    .set(UserInfo::getWxMsgSum, textSum)
                    .set(UserInfo::getGmtUpdate, LocalDateTime.now())
                    .update();
        } else {
            log.info("更新用户同步文章次数:{}, 更新用户同步信息次数:{}", articleSum, textSum);
            lambdaUpdateChainWrapper
                    .eq(UserInfo::getId, id)
                    .set(UserInfo::getWxMsgSum, textSum)
                    .set(UserInfo::getWxArticleSum, articleSum)
                    .set(UserInfo::getGmtUpdate, LocalDateTime.now())
                    .update();
        }
    }

}
