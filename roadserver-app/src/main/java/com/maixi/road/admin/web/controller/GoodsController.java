package com.maixi.road.admin.web.controller;

import com.maixi.road.admin.biz.service.IGoodsService;
import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.biz.service.INotionResourceService;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.request.CheckPromotionParam;
import com.maixi.road.common.core.model.response.GoodsVo;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 商品表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */

@RestController
@RequestMapping("/miniprogram")
public class GoodsController extends BaseController {

    @Resource
    private IGoodsService goodsService;
    @Resource
    private INotionResourceService notionResourceService;
    @Resource
    private IMemberService memberService;


    @GetMapping("/goodsList")
    public Result<List<GoodsVo>> goodsList() {
        return Result.success(goodsService.getGoodsList());
    }

    @GetMapping("/goodsList/obsidian")
    public Result<List<GoodsVo>> goodsListObsidian() {
        return Result.success(goodsService.getGoodsListObsidian());
    }

    @GetMapping("/getDiscount")
    public Result<Integer> getDiscount() {
        return Result.success(notionResourceService.getDiscount() / 100);
    }


    @PostMapping("/checkPromotionCode")
    public Result<Boolean> checkPromotionCode(@Valid @RequestBody CheckPromotionParam param) {
        return Result.success(memberService.checkPromotionCode(param.getPromotionCode()));
    }

    
    
}
