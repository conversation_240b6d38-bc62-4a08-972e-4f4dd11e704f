package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 微信的fromUser标识与unionId的关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("wx_user_cp")
public class WxUserCp {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("wx_user_id")
    private String wxUserId;

    @TableField("union_id")
    private String unionId;

    @TableField("create_time")
    private LocalDateTime createTime;
}
