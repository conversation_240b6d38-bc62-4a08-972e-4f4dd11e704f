package com.maixi.road.admin.biz.service;

import java.util.Optional;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.CustomConfig;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface ICustomConfigService extends IService<CustomConfig> {

    CustomConfig getCustomConfigByUnionId(String unionId);

    Optional<CustomConfig> getCustomConfigOptByUnionId(String unionId);
}
