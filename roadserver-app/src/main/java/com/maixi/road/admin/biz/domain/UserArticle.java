package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("user_article")
public class UserArticle {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * unionId
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 数据库 ID
     */
    @TableField("database_id")
    private String databaseId;

    @TableField("access_token")
    private String accessToken;

    /**
     * 配置
     */
    @TableField("config_json")
    private String configJson;

    /**
     * 文章链接自定义名称
     */
    @TableField("url")
    private String url;

    @TableField("origin")
    private String origin;

    /**
     * 文章作者自定义名称
     */
    @TableField("author")
    private String author;

    /**
     * 文章标签自定义名称
     */
    @TableField("tag")
    private String tag;

    /**
     * 文章创建时间自定义名称
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 文章备注自定义名称
     */
    @TableField("remark")
    private String remark;

    /**
     * 需要正文，0 是 1 否
     */
    @TableField("need_content")
    private Integer needContent;

    /**
     * 版本
     */
    @TableField("version")
    private String version;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    @TableField("publish_time")
    private String publishTime;
}
