package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("user_message")
public class UserMessage {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * unionId
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 数据库 ID
     */
    @TableField("database_id")
    private String databaseId;

    @TableField("access_token")
    private String accessToken;

    /**
     * 配置
     */
    @TableField("config_json")
    private String configJson;

    /**
     * 消息类型自定义名称
     */
    @TableField("`type`")
    private String type;

    /**
     * 消息标签自定义名称
     */
    @TableField("tag")
    private String tag;

    /**
     * 消息创建时间自定义名称
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 版本
     */
    @TableField("version")
    private String version;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;
}
