package com.maixi.road.admin.manager;

import cn.hutool.core.io.resource.ClassPathResource;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.maixi.road.framework.web.Road;
import com.maixi.road.s3proxy.S3ProxyService;
import com.maixi.road.admin.biz.domain.CloudinaryConfig;
import com.maixi.road.admin.biz.domain.CustomConfig;
import com.maixi.road.admin.biz.domain.OssConfig;
import com.maixi.road.admin.biz.service.ICloudinaryConfigService;
import com.maixi.road.admin.biz.service.ICustomConfigService;
import com.maixi.road.admin.biz.service.IOssConfigService;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.s3proxy.dto.UploadResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class S3Manager {

    private static final String HI_PNG = "hi.png";
    private static final String HI_MD = "hi.md";
    @Resource
    private ICloudinaryConfigService cloudinaryConfigService;
    @Resource
    private UserManager userManager;
    @Resource
    private IOssConfigService ossConfigService;
    @Resource
    private ICustomConfigService customConfigService;
    @Resource
    private S3ProxyService s3ProxyService;

    public boolean hasImgClient(String unionId) {
        Optional<CloudinaryConfig> cloudinaryClientOpt = cloudinaryConfigService
                .getCloudinaryConfigOptByUnionId(unionId);
        if (cloudinaryClientOpt.isPresent()) {
            return true;
        }
        return ossConfigService.getOssConfigOptByUnionId(unionId).isPresent();
    }

    public S3Config getImgS3Config(String unionId) {
        CustomConfig customConfig = customConfigService.getCustomConfigByUnionId(unionId);
        // 如果配置了 alwaysUseCloudinary 并且为1，则优先使用Cloudinary
        if (customConfig != null && customConfig.getAlwaysUseCloudinary() == 1) {
            Optional<CloudinaryConfig> cloudinaryClientOpt = cloudinaryConfigService
                    .getCloudinaryConfigOptByUnionId(unionId);
            if (cloudinaryClientOpt.isPresent()) {
                CloudinaryConfig cloudinaryConfig = cloudinaryClientOpt.get();
                return S3Config.builder()
                        .userId(unionId)
                        .provider("cloudinary")
                        .bucket(cloudinaryConfig.getCloudName())
                        .accessKey(cloudinaryConfig.getApiKey())
                        .secretKey(cloudinaryConfig.getApiSecret())
                        .prefix("mpclipper")
                        .build();
            }
        }
        // 查看是否配置了高级图床
        S3Config s3Config = getS3Config(unionId);
        if (s3Config != null) {
            return s3Config;
        }
        // 继续查看是否配置了cloudinary图床
        Optional<CloudinaryConfig> cloudinaryClientOpt = cloudinaryConfigService
                .getCloudinaryConfigOptByUnionId(unionId);
        if (cloudinaryClientOpt.isPresent()) {
            CloudinaryConfig cloudinaryConfig = cloudinaryClientOpt.get();
            return S3Config.builder()
                    .userId(unionId)
                    .provider("cloudinary")
                    .bucket(cloudinaryConfig.getCloudName())
                    .accessKey(cloudinaryConfig.getApiKey())
                    .secretKey(cloudinaryConfig.getApiSecret())
                    .prefix("mpclipper")
                    .build();
        }
        // 如果都没有配置，则返回null
        return null;
    }

    public S3Config getS3Config(String unionId) {
        Optional<OssConfig> ossConfigOpt = ossConfigService.getOssConfigOptByUnionId(unionId);
        if (ossConfigOpt.isPresent()) {
            // FIXME 检查并修正配置
            OssConfig ossConfig = checkAndCorrectConfig(ossConfigOpt.get());
            Map<String, String> extraParams = Maps.newHashMap();
            if (ossConfig.getCustomDomain() != null) {
                extraParams.put("customDomain", ossConfig.getCustomDomain());
            }
            return S3Config.builder()
                    .userId(unionId)
                    .provider(ossConfig.getType())
                    .endpoint(ossConfig.getEndpoint())
                    .region(ossConfig.getRegion())
                    .accessKey(ossConfig.getAccessKey())
                    .secretKey(ossConfig.getAccessSecret())
                    .bucket(ossConfig.getBucketName())
                    .prefix("mpclipper")
                    .extraParams(extraParams)
                    .build();
        }
        return null;
    }

    public String uploadImgFile(String unionId, File file) {
        S3Config s3Config = getImgS3Config(unionId);
        if (s3Config == null) {
            return null;
        }
        try {
            UploadResult uploadFile = s3ProxyService.uploadFile(s3Config, file, null);
            file.delete();
            return uploadFile.getUrl();
        } catch (Exception e) {
            log.error("uploadImgFile, occur exception, unionId={}", unionId, e);
        }
        return null;
    }

    public String uploadImgFile(String unionId, String filePath, String fileName) {
        S3Config s3Config = getImgS3Config(unionId);
        if (s3Config == null) {
            return null;
        }
        try {
            File file = new File(filePath);
            UploadResult uploadFile = s3ProxyService.uploadFile(s3Config, file, fileName);
            file.delete();
            return uploadFile.getUrl();
        } catch (Exception e) {
            log.error("uploadImgFile, occur exception, unionId={}, filePath={}, fileName={}", unionId, filePath,
                    fileName, e);
        }
        return null;
    }

    public String uploadFile(String unionId, String filePath, String fileName) {
        S3Config s3Config = getS3Config(unionId);
        if (s3Config == null) {
            return null;
        }
        try {
            File file = new File(filePath);
            UploadResult uploadFile = s3ProxyService.uploadFile(s3Config, file, fileName);
            file.delete();
            return uploadFile.getUrl();
        } catch (Exception e) {
            log.error("uploadFile, occur exception, unionId={}, filePath={}, fileName={}", unionId, filePath, fileName,
                    e);
        }
        return null;
    }

    public boolean verify(OssConfig ossConfig) {
        // TODO 这里是为了兼容，在页面未发版之前，必须兼容旧配置
        OssConfig cloneConfig = JSON.parseObject(JSON.toJSONString(ossConfig), OssConfig.class);
        // 检查并修正配置
        checkAndCorrectConfig(cloneConfig);
        Map<String, String> extraParams = Maps.newHashMap();
        if (cloneConfig.getCustomDomain() != null) {
            extraParams.put("customDomain", cloneConfig.getCustomDomain());
        }
        S3Config s3Config = S3Config.builder()
                .userId(Road.getLoginUser().getUnionId())
                .provider(cloneConfig.getType())
                .endpoint(cloneConfig.getEndpoint())
                .region(cloneConfig.getRegion())
                .accessKey(cloneConfig.getAccessKey())
                .secretKey(cloneConfig.getAccessSecret())
                .bucket(cloneConfig.getBucketName())
                .prefix("mpclipper")
                .extraParams(extraParams)
                .build();
        ClassPathResource resource = new ClassPathResource(HI_MD);
        File file = resource.getFile();
        UploadResult result = s3ProxyService.uploadFile(s3Config, file, file.getName());
        return result.isSuccess();
    }

    public boolean verify(CloudinaryConfig cloudinaryConfig) {
        S3Config s3Config = S3Config.builder()
                .userId(Road.getLoginUser().getUnionId())
                .provider("cloudinary")
                .bucket(cloudinaryConfig.getCloudName())
                .accessKey(cloudinaryConfig.getApiKey())
                .secretKey(cloudinaryConfig.getApiSecret())
                .prefix("mpclipper")
                .build();
        ClassPathResource resource = new ClassPathResource(HI_PNG);
        File file = resource.getFile();
        UploadResult result = s3ProxyService.uploadFile(s3Config, file, file.getName());
        return result.isSuccess();
    }

    private OssConfig checkAndCorrectConfig(OssConfig ossConfig) {
        String region = ossConfig.getRegion();
        if (StringUtils.isNotBlank(region)) {
            return ossConfig;
        }
        if ("r2".equals(ossConfig.getType())) {
            ossConfig.setRegion("auto");
            // log.info("更新 ossConfig 的 region 为 {},endpoint={}", region,
            // ossConfig.getEndpoint());
            // ossConfigService.updateRegionAndEndpoint(ossConfig.getId(), region,
            // ossConfig.getEndpoint());
        } else if ("oss".equals(ossConfig.getType())) {
            // oss-cn-hangzhou.aliyuncs.com
            // region 是 cn-hangzhou
            if (!ossConfig.getEndpoint().startsWith("https://")) {
                ossConfig.setEndpoint("https://" + ossConfig.getEndpoint());
            }
            String prefix = "https://oss-";
            String suffix = ".aliyuncs.com";
            int startIndex = prefix.length();
            int endIndex = ossConfig.getEndpoint().indexOf(suffix);
            if (endIndex > startIndex) {
                region = ossConfig.getEndpoint().substring(startIndex, endIndex);
            }
            ossConfig.setRegion(region);
            // log.info("更新 ossConfig 的 region 为 {},endpoint={}", region,
            // ossConfig.getEndpoint());
            // ossConfigService.updateRegionAndEndpoint(ossConfig.getId(), region,
            // ossConfig.getEndpoint());
        } else if ("s3".equals(ossConfig.getType())) {
            // us-west-1.amazonaws.com
            // region 是 us-west-1
            if (!ossConfig.getEndpoint().startsWith("https://s3.")) {
                ossConfig.setEndpoint("https://s3." + ossConfig.getEndpoint());
            }
            String prefix = "https://s3.";
            String suffix = ".amazonaws.com";
            int startIndex = prefix.length();
            int endIndex = ossConfig.getEndpoint().indexOf(suffix);
            if (endIndex > startIndex) {
                region = ossConfig.getEndpoint().substring(startIndex, endIndex);
            }
            ossConfig.setRegion(region);
            // log.info("更新 ossConfig 的 region 为 {},endpoint={}", region,
            // ossConfig.getEndpoint());
            // ossConfigService.updateRegionAndEndpoint(ossConfig.getId(), region,
            // ossConfig.getEndpoint());
        } else if ("cos".equals(ossConfig.getType())) {
            // cos.ap-guangzhou-internal.myqcloud.com
            // region 是 ap-guangzhou-internal
            if (!ossConfig.getEndpoint().startsWith("https://cos.")) {
                ossConfig.setEndpoint("https://cos." + ossConfig.getEndpoint());
            }
            String prefix = "https://cos.";
            String suffix = ".myqcloud.com";
            int startIndex = prefix.length();
            int endIndex = ossConfig.getEndpoint().indexOf(suffix);
            if (endIndex > startIndex) {
                region = ossConfig.getEndpoint().substring(startIndex, endIndex);
            }
            ossConfig.setRegion(region);
            // log.info("更新 ossConfig 的 region 为 {},endpoint={}", region,
            // ossConfig.getEndpoint());
            // ossConfigService.updateRegionAndEndpoint(ossConfig.getId(), region,
            // ossConfig.getEndpoint());
        }
        // return ossConfigService.getById(ossConfig.getId());
        return ossConfig;
    }
}
