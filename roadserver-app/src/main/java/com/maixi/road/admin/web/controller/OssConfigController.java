package com.maixi.road.admin.web.controller;

import com.google.common.collect.Sets;
import com.maixi.road.admin.manager.ConfigManager;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.framework.annotation.DistributedLock;
import com.maixi.road.framework.web.MainUserWrapper;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.request.OssConfigCreateReq;
import com.maixi.road.common.core.model.response.OssConfigVo;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.Set;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */

@RestController
@RequestMapping("/miniprogram")
public class OssConfigController extends BaseController {

    private static final Set<String> AVAILABLE_TYPES = Sets.newHashSet("r2", "s3", "oss", "cos", "minio");
    @Resource
    private ConfigManager configManager;

    @GetMapping("/getOssConfig")
    public Result<OssConfigVo> getOssConfig() {
        return Result.success(configManager.getOssConfig(getMainUserWrapper().getMainUnionId()));
    }

    @PostMapping("/postOssConfig")
    @DistributedLock(lockTime = 3, waitTime = 0)
    public Result<Boolean> postOssConfig(@Valid @RequestBody OssConfigCreateReq createReq) {
        if (createReq.getType().equals("r2") && StringUtils.isBlank(createReq.getCustomDomain())) {
            return Result.fail("关联域名不能留空");
        }
        if (createReq.getEndpoint().contains("qiniucs.com") && StringUtils.isBlank(createReq.getCustomDomain())) {
            return Result.fail("七牛云图床需要绑定域名");
        }
        if (StringUtils.isNotBlank(createReq.getCustomDomain()) && !createReq.getCustomDomain().startsWith("http")) {
            return Result.fail("关联域名必须以http或https开头");
        }
        if (createReq.getType().equals("r2") && StringUtils.isBlank(createReq.getRegion())) {
            return Result.fail("AccountId不能为空");
        }
        if (!AVAILABLE_TYPES.contains(createReq.getType())) {
            return Result.fail("不支持的图床类型");
        }
        if (!Objects.equals(createReq.getType(), "r2") && !Objects.equals(createReq.getType(), "minio") && StringUtils.isBlank(createReq.getRegion())) {
            return Result.fail("region不能为空");
        }
        if (!createReq.getEndpoint().startsWith("https://")&&!createReq.getEndpoint().startsWith("http://")) {
            return Result.fail("Endpoint必须以http://或https://开头");
        }
        if ("s3".equals(createReq.getType()) && !createReq.getEndpoint().startsWith("https://s3")) {
            return Result.fail("Endpoint格式不符");
        }
        if ("oss".equals(createReq.getType()) && !createReq.getEndpoint().startsWith("https://oss")) {
            return Result.fail("Endpoint格式不符");
        }
        if ("cos".equals(createReq.getType()) && !createReq.getEndpoint().startsWith("https://cos")) {
            return Result.fail("Endpoint格式不符");
        }
        MainUserWrapper mainUserWrapper = getMainUserWrapper();
        if (!mainUserWrapper.isMainUser()) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
        }
        configManager.postOssConfig(mainUserWrapper.getMainUnionId(), createReq);
        return Result.success(true);
    }

    @GetMapping("/removeOssConfig")
    public Result<Boolean> removeOssConfig() {
        MainUserWrapper mainUserWrapper = getMainUserWrapper();
        if (!mainUserWrapper.isMainUser()) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "子账号无权限操作");
        }
        configManager.removeOssConfig(mainUserWrapper.getMainUnionId());
        return Result.success(true);
    }

}
