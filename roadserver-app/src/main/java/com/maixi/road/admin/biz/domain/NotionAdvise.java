package com.maixi.road.admin.biz.domain;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("notion_advise")
public class NotionAdvise {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField("union_id")
    private String unionId;
    @TableField("`type`")
    private Integer type;
    @TableField("content")
    private String content;
    @TableField("create_time")
    private LocalDateTime createTime;
}
