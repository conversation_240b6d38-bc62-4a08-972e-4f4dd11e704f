package com.maixi.road.admin.manager;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.maixi.road.admin.biz.domain.Goods;
import com.maixi.road.admin.biz.domain.Member;
import com.maixi.road.admin.biz.domain.Orders;
import com.maixi.road.admin.biz.domain.Payment;
import com.maixi.road.admin.biz.domain.PromotionRecord;
import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.domain.WxpayContent;
import com.maixi.road.admin.biz.service.IGoodsService;
import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.biz.service.INotionResourceService;
import com.maixi.road.admin.biz.service.IOrdersService;
import com.maixi.road.admin.biz.service.IPaymentService;
import com.maixi.road.admin.biz.service.IPromotionRecordService;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.admin.biz.service.IUserOpenIdService;
import com.maixi.road.admin.biz.service.IWxpayContentService;
import com.maixi.road.admin.manager.dto.OrderExtraInfo;
import com.maixi.road.common.business.order.constant.OrderConstants;
import com.maixi.road.common.business.order.enums.GoodsEnum;
import com.maixi.road.common.business.order.enums.OrderStatusEnum;
import com.maixi.road.common.business.order.enums.OrderTypeEnum;
import com.maixi.road.common.business.order.enums.PayTypeEnum;
import com.maixi.road.common.business.order.enums.PaymentBizTypeEnum;
import com.maixi.road.common.business.order.enums.TradeStatusEnum;
import com.maixi.road.common.business.user.enums.VipTypeEnum;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.enums.error.OrderErrCodeEnum;
import com.maixi.road.common.core.exception.OrderException;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.request.MemberCreateParam;
import com.maixi.road.common.core.model.request.OrderRequest;
import com.maixi.road.common.core.model.response.OrderVo;
import com.maixi.road.common.core.model.response.WxPayment;
import com.maixi.road.common.core.utils.DateUtils;
import com.maixi.road.common.core.utils.MDCRunnable;
import com.maixi.road.common.core.utils.PromotionCodeGenerator;
import com.maixi.road.common.core.utils.RequestUtil;
import com.maixi.road.common.core.utils.TradeNoGenerator;
import com.maixi.road.framework.config.RedisManager;
import com.maixi.road.framework.config.WxPayProperties;
import com.maixi.road.framework.web.LoginUser;
import com.maixi.road.framework.web.Road;

import cn.hutool.core.util.IdUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import com.maixi.road.framework.config.WxPayConfiguration;

@Slf4j
@Service
public class OrderManager {

    @Resource
    private WxPayProperties wxPayProperties;
    @Resource
    private WxPayService wxService;
    @Resource
    private WxPayConfiguration.WxPayServiceManager wxPayServiceManager;
    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private IUserOpenIdService userOpenIdService;
    @Resource
    private IGoodsService goodsService;
    @Resource
    private IMemberService memberService;
    @Resource
    private INotionResourceService notionResourceService;
    @Resource
    private IOrdersService ordersService;
    @Resource
    private IPaymentService paymentService;
    @Resource
    private IWxpayContentService wxpayContentService;
    @Resource
    private IPromotionRecordService promotionRecordService;
    @Resource
    private RedisManager redisManager;
    private final ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 根据appId获取对应的商户密钥
     * 
     * @param appId 小程序appId
     * @return 商户密钥
     */
    private String getMchKeyByAppId(String appId) {
        try {
            // 如果配置了多个支付配置，从configs中查找
            if (wxPayProperties.getConfigs() != null && !wxPayProperties.getConfigs().isEmpty()) {
                return wxPayProperties.getConfigs().stream()
                        .filter(config -> appId.equals(config.getAppId()))
                        .findFirst()
                        .map(WxPayProperties.Config::getMchKey)
                        .orElse(wxPayProperties.getMchKey()); // 如果没找到，使用默认配置
            } else {
                // 单配置模式，直接返回配置的密钥
                return wxPayProperties.getMchKey();
            }
        } catch (Exception e) {
            log.error("获取商户密钥异常, appId={}", appId, e);
            // 异常情况下使用默认配置
            return wxPayProperties.getMchKey();
        }
    }

    /**
     * 获取用户在当前小程序中的正确openId
     * 
     * @param unionId 用户unionId
     * @return 正确的openId
     */
    private String getCurrentAppOpenId(String unionId, String appId) {
        try {
            // 根据unionId和appId查询对应的openId
            String specificOpenId = userOpenIdService.getOpenIdByUnionIdAndAppId(unionId, appId);
            if (StringUtils.isNotBlank(specificOpenId)) {
                log.debug("获取到小程序特定openId, unionId={}, appId={}, openId={}", unionId, appId, specificOpenId);
                return specificOpenId;
            }

            // 如果没有找到特定的openId，使用LoginUser中的默认openId
            String defaultOpenId = Road.getLoginUser().getOpenId();
            log.debug("使用默认openId, unionId={}, openId={}", unionId, defaultOpenId);
            return defaultOpenId;
        } catch (Exception e) {
            log.error("获取openId异常, unionId={}", unionId, e);
            // 异常情况下使用LoginUser中的默认openId
            return Road.getLoginUser().getOpenId();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public WxPayment createOrder(OrderRequest orderRequest, String clientIp, String appId) throws WxPayException {
        LoginUser loginUser = Road.getLoginUser();
        Goods goods = goodsService.getByNo(orderRequest.getGoodsNo());
        if (goods == null || goods.getStatus() != 1) {
            throw OrderException.create(OrderErrCodeEnum.GOOD_INVALID_STATUS);
        }

        if (!orderRequest.getAmount().equals(goods.getDiscount())) {
            throw OrderException.create(OrderErrCodeEnum.GOOD_PRICE_INCORRECT);
        }

        preOrderValidCheck(loginUser.getUnionId(), goods);

        Integer discount = 0;
        if (Objects.equals(orderRequest.getDiscountCode(), "BetterAndBetter")) {
            log.info("使用 BetterAndBetter 优惠码");
            discount = notionResourceService.getDiscount();
        } else if (StringUtils.isNoneBlank(orderRequest.getDiscountCode())) {
            Member member = memberService.getByDiscountCode(orderRequest.getDiscountCode().trim());
            if (member == null) {
                log.info("非会员推广码，优惠码无效");
                throw OrderException.create(OrderErrCodeEnum.MARKETING_PROMOTION_CODE_INVALID);
            }
            if (member.getVipType() == VipTypeEnum.YEAR_VIP.getType()) {
                log.info("使用年卡会员优惠码,优惠码={}", orderRequest.getDiscountCode());
                discount = 500;
            } else if (member.getVipType() == VipTypeEnum.FOREVER_VIP.getType()) {
                log.info("使用永久会员优惠码,优惠码={}", orderRequest.getDiscountCode());
                discount = 700;
            }
        }

        String orderNo = TradeNoGenerator.generateTradeNo(PaymentBizTypeEnum.PAY_ORDER,
                loginUser.getUserId().longValue());

        Integer payAmount = orderRequest.getAmount() - discount;

        // 获取当前小程序对应的正确openId
        String correctOpenId = getCurrentAppOpenId(loginUser.getUnionId(), appId);
        log.info("获取到小程序特定openId, unionId={}, appId={}, openId={}", loginUser.getUnionId(), appId, correctOpenId);

        // 生成订单
        this.createOrder(orderNo, loginUser, goods, discount, payAmount, orderRequest.getDiscountCode());

        Payment payment = Payment.builder()
                .orderNo(orderNo)
                .unionId(loginUser.getUnionId())
                .openId(correctOpenId)
                .payAmount(payAmount)
                .currency("CNY")
                .payType(PayTypeEnum.WXPAY.getCode())
                .status(OrderStatusEnum.NOTPAY.getStatus())
                .gmtCreate(LocalDateTime.now())
                .gmtUpdate(LocalDateTime.now())
                .deleted(0)
                .build();
        paymentService.save(payment);

        // 预支付单
        WxPayUnifiedOrderRequest request = WxPayUnifiedOrderRequest.newBuilder()
                .body(goods.getDescription())
                .outTradeNo(orderNo)
                .totalFee(payment.getPayAmount())
                .spbillCreateIp(clientIp)
                .timeStart(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")))
                .notifyUrl("https://www.notionmpclipper.site/notionclipper/miniprogram/notify/order")
                .tradeType("JSAPI")
                .openid(correctOpenId)
                .build();
        // 根据appId获取对应的支付服务
        WxPayService currentWxService = wxPayServiceManager.getPayService(appId);
        log.info("使用支付服务处理订单, appId={}, orderNo={}", appId, orderNo);

        WxPayUnifiedOrderResult result = null;
        try {
            result = currentWxService.unifiedOrder(request);
        } catch (WxPayException e) {
            if (e.getErrCodeDes().contains("appid和openid不匹配") || e.getErrCodeDes().contains("无效的openid")) {
                // 判断如果是 openid 不一致，需要清空当前用户的 openid，引导用户重新登录,剔除用户登录缓存
                executor.execute(new MDCRunnable(() -> {
                    userInfoService.clearOpenIdByUnionId(loginUser.getUnionId());
                }));
                throw RoadException.create(ErrorCodeEnum.USER_OPENID_NOT_MATCH_APPID, "请重新进入小程序操作");
            } else {
                throw RoadException.create(ErrorCodeEnum.DEFAULT_ERROR, "支付失败了");
            }
        } catch (Exception e) {
            log.error("[微信支付失败]:", e);
            throw RoadException.create(ErrorCodeEnum.DEFAULT_ERROR, "支付失败了");
        }

        // 记录微信支付记录
        WxpayContent payLog = WxpayContent.builder()
                .outTradeNo(orderNo)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .preRequest(JSON.toJSONString(request))
                .preResult(JSON.toJSONString(result))
                .build();
        wxpayContentService.save(payLog);

        // 生成签名
        WxPayment wxPayment = new WxPayment();
        wxPayment.setWxTradeId(orderNo);
        wxPayment.setAppId(result.getAppid());
        wxPayment.setTimeStamp((System.currentTimeMillis() / 1000) + "");
        wxPayment.set_package("prepay_id=" + result.getPrepayId());
        wxPayment.setNonceStr(IdUtil.fastSimpleUUID());
        wxPayment.setSignType("MD5");

        // 根据appId获取对应的商户密钥进行签名
        String mchKey = getMchKeyByAppId(appId);
        wxPayment.sign(mchKey);
        log.info("用户下单请求, unionId={}, openId={}, amount={}", loginUser.getUnionId(), correctOpenId,
                orderRequest.getAmount());
        return wxPayment;
    }

    public void createOrder(String tradeNo, LoginUser userInfo, Goods goods, Integer discount, Integer payAmount,
            String discountCode) {
        Orders order = new Orders();
        order.setOrderNo(tradeNo);
        order.setType(goods.getType());
        order.setGoodsNo(goods.getGoodsNo());
        order.setOpenId(userInfo.getOpenId());
        order.setUnionId(userInfo.getUnionId());
        order.setStatus(OrderStatusEnum.NOTPAY.getStatus());
        order.setAmount(goods.getPrice());
        order.setPayType(PayTypeEnum.WXPAY.getCode());// 微信支付
        order.setDiscount(discount);
        order.setPayAmount(payAmount);
        OrderExtraInfo extraInfo = OrderExtraInfo.builder()
                .goodsNo(goods.getGoodsNo())
                .goodAmount(goods.getPrice())
                .goodDiscount(goods.getDiscount())
                .goodDescription(goods.getDescription())
                .discountCode(discountCode)
                .build();
        order.setExtraInfo(JSON.toJSONString(extraInfo));
        LocalDateTime now = LocalDateTime.now();
        order.setCreateTime(now);
        order.setGmtCreate(now);
        order.setGmtUpdate(now);
        order.setDeleted(0);
        ordersService.save(order);
    }

    private void preOrderValidCheck(String unionId, Goods goods) {
        OrderTypeEnum orderTypeEnum;
        if (GoodsEnum.ONE_YEAR_MEMBER.getGoodsNo().equals(goods.getGoodsNo())) {
            orderTypeEnum = OrderTypeEnum.YEAR_VIP;
        } else if (GoodsEnum.TWO_YEAR_MEMBER.getGoodsNo().equals(goods.getGoodsNo())) {
            orderTypeEnum = OrderTypeEnum.TWO_YEAR_VIP;
        } else if (GoodsEnum.PERMANENT_MEMBER.getGoodsNo().equals(goods.getGoodsNo())) {
            orderTypeEnum = OrderTypeEnum.FOREVER_VIP;
        } else if (GoodsEnum.OB_ONE_YEAR_MEMBER.getGoodsNo().equals(goods.getGoodsNo())) {
            orderTypeEnum = OrderTypeEnum.OB_YEAR_VIP;
        } else if (GoodsEnum.OB_TWO_YEAR_MEMBER.getGoodsNo().equals(goods.getGoodsNo())) {
            orderTypeEnum = OrderTypeEnum.OB_TWO_YEAR_VIP;
        } else if (GoodsEnum.OB_PERMANENT_MEMBER.getGoodsNo().equals(goods.getGoodsNo())) {
            orderTypeEnum = OrderTypeEnum.OB_FOREVER_VIP;
        } else {
            throw OrderException.create(OrderErrCodeEnum.ORDER_UNKNOWN_ORDER_TYPE);
        }

        Member member = memberService.selectByUnionIdAndType(unionId, goods.getType());
        if (member != null) {
            Integer vipType = member.getVipType();
            if (OrderTypeEnum.FOREVER_VIP.getType().equals(orderTypeEnum.getType())) {
                // 如果是购买永久会员。
                // 需要查询限额是否超出
                int fiveYearCountLimit = notionResourceService.getForeverVipLimitCount();
                if (fiveYearCountLimit < 1) {
                    throw OrderException.create(OrderErrCodeEnum.ORDER_SELL_OUT);
                }
                // 需要查询是否多次购买
                if (VipTypeEnum.FOREVER_VIP.getType().equals(vipType)) {
                    throw OrderException.create(OrderErrCodeEnum.ORDER_BUY_ONLY_ONCE);
                }
            } else if (OrderTypeEnum.YEAR_VIP.getType().equals(orderTypeEnum.getType())) {
                // 永久会员无需购买
                if (VipTypeEnum.FOREVER_VIP.getType().equals(vipType)) {
                    throw OrderException.create(OrderErrCodeEnum.ORDER_NO_NEED_TO_BUY);
                }
            } else if (OrderTypeEnum.TWO_YEAR_VIP.getType().equals(orderTypeEnum.getType())) {
                // 永久会员无需购买
                if (VipTypeEnum.FOREVER_VIP.getType().equals(vipType)) {
                    throw OrderException.create(OrderErrCodeEnum.ORDER_NO_NEED_TO_BUY);
                }
            } else if (OrderTypeEnum.OB_FOREVER_VIP.getType().equals(orderTypeEnum.getType())) {
                // 永久会员无需购买
                if (VipTypeEnum.FOREVER_VIP.getType().equals(vipType)) {
                    throw OrderException.create(OrderErrCodeEnum.ORDER_NO_NEED_TO_BUY);
                }
            } else if (OrderTypeEnum.OB_YEAR_VIP.getType().equals(orderTypeEnum.getType())) {
                // 永久会员无需购买
                if (VipTypeEnum.FOREVER_VIP.getType().equals(vipType)) {
                    throw OrderException.create(OrderErrCodeEnum.ORDER_NO_NEED_TO_BUY);
                }
            } else if (OrderTypeEnum.OB_TWO_YEAR_VIP.getType().equals(orderTypeEnum.getType())) {
                // 永久会员无需购买
                if (VipTypeEnum.FOREVER_VIP.getType().equals(vipType)) {
                    throw OrderException.create(OrderErrCodeEnum.ORDER_NO_NEED_TO_BUY);
                }
            }
        }
    }

    public void parseOrderNotifyResult(String xmlData) throws WxPayException {
        // 使用默认服务解析回调结果（回调不区分appId）
        final WxPayOrderNotifyResult notifyResult = wxService.parseOrderNotifyResult(xmlData);
        log.info("收到订单支付回调通知, openId={},amount={}", notifyResult.getOpenid(), notifyResult.getTotalFee());
        // 记录微信支付日志
        wxpayContentService.updateNotifyResult(notifyResult.getOutTradeNo(), JSON.toJSONString(notifyResult),
                notifyResult.getTransactionId());

        Orders order = ordersService.getByOrderNo(notifyResult.getOutTradeNo());
        if (order == null) {
            log.info("订单支付回调异常：订单不存在,orderNo={}", notifyResult.getOutTradeNo());
            throw OrderException.create(OrderErrCodeEnum.ORDER_NOT_EXIST);
        }

        if (OrderStatusEnum.NOTPAY.getStatus().equals(order.getStatus())) {
            if (OrderConstants.SUCCESS.equals(notifyResult.getReturnCode())) {
                if (OrderConstants.SUCCESS.equals(notifyResult.getResultCode())) {
                    if (order.getPayAmount().equals(notifyResult.getTotalFee())) {
                        // 更新订单状态为已支付
                        LocalDateTime payTime = DateUtils.yyyyMMddHHmmss(notifyResult.getTimeEnd());
                        this.updateStatusToPayed(order, notifyResult.getTransactionId(), payTime);
                    } else {
                        log.error("订单支付回调异常:金额不一致,orderNo={},amount={}", order.getOrderNo(),
                                notifyResult.getTotalFee());
                        throw OrderException.create(OrderErrCodeEnum.ORDER_PRICE_INCORRECT);
                    }
                } else {
                    log.error("订单支付回调异常 orderNo={}, resultCode = {}, err_code={} , err_code_des={}", order.getOrderNo(),
                            notifyResult.getResultCode(), notifyResult.getErrCode(), notifyResult.getErrCodeDes());
                }
            } else {
                log.error("订单支付回调异常 orderNo={}, returnCode = {}, return_msg={}", order.getOrderNo(),
                        notifyResult.getReturnCode(), notifyResult.getReturnMsg());
            }
        } else {
            // 这里不能抛异常，否则会一直重试。
            log.error("订单非待支付状态，orderId={}, order={}", order.getId(), JSON.toJSONString(order));
        }
    }

    public OrderVo queryOrder(String outTradeNo, String unionId) throws WxPayException {
        Orders order = ordersService.getByOrderNo(outTradeNo);
        if (order == null) {
            throw OrderException.create(OrderErrCodeEnum.ORDER_NOT_EXIST);
        }
        if (!Objects.equals(order.getUnionId(), unionId)) {
            throw OrderException.create(OrderErrCodeEnum.ORDER_NOT_BELONG_YOU);
        }
        // 待支付订单要查一下微信支付平台
        if (OrderStatusEnum.NOTPAY.getStatus().equals(order.getStatus())) {
            // 根据订单信息获取对应的支付服务进行查询
            // 这里可以从订单的extraInfo中获取appId，或者使用默认服务查询
            WxPayOrderQueryResult result = wxService.queryOrder(null, outTradeNo);
            wxpayContentService.updateQueryResult(outTradeNo, JSON.toJSONString(result),
                    Optional.ofNullable(result).map(WxPayOrderQueryResult::getTransactionId).orElse("null"));
            if (result != null) {
                if (OrderConstants.SUCCESS.equals(result.getReturnCode())) {
                    if (OrderConstants.SUCCESS.equals(result.getResultCode())) {
                        // 交易成功判断条件： return_code、result_code和trade_state都为SUCCESS
                        if (TradeStatusEnum.SUCCESS.getCode().equals(result.getTradeState())) {
                            log.info("查询微信订单信息成功,status= SUCCESS. orderNo={}", outTradeNo);
                            LocalDateTime payTime = DateUtils.yyyyMMddHHmmss(result.getTimeEnd());
                            this.updateStatusToPayed(order, result.getTransactionId(), payTime);
                            order = ordersService.getByOrderNo(outTradeNo);
                        } else if (TradeStatusEnum.CLOSED.getCode().equals(result.getTradeState())) {
                            // 关单处理
                            log.warn("查询微信订单信息成功,status= CLOSED. orderNo={}", outTradeNo);
                            this.updateStatusToClosed(order, result.getTransactionId(), LocalDateTime.now());
                            order = ordersService.getByOrderNo(outTradeNo);
                        } else {
                            log.error("查询微信订单异常 status = {}, orderNo = {}", result.getTradeState(), outTradeNo);
                        }
                    } else {
                        log.error("查询微信订单异常 result_code = {}, err_code = {}, err_code_des={}", result.getResultCode(),
                                result.getErrCode(), result.getErrCodeDes());
                    }
                } else {
                    log.error("查询微信订单异常 return_code = {} ,orderNo = {}, return_msg={}", result.getReturnCode(),
                            outTradeNo, result.getReturnMsg());
                }
            }
        }
        return OrderVo.builder()
                .orderStatus(order.getStatus())
                .orderStatusDesc(Objects.requireNonNull(OrderStatusEnum.getByStatus(order.getStatus())).getDesc())
                .orderNo(outTradeNo).build();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatusToPayed(Orders order, String payNo, LocalDateTime payTime) {
        // 更新支付单记录
        Payment payment = paymentService.getByOrderNo(order.getOrderNo());
        if (payment == null) {
            log.error("更新支付单状态异常:支付单不存在,orderNo={}", order.getOrderNo());
            throw OrderException.create(OrderErrCodeEnum.PAYMENT_NOT_EXIST);
        }
        payment.setPayNo(payNo);
        payment.setStatus(OrderStatusEnum.SUCCESS.getStatus());
        payment.setPayTime(payTime);
        paymentService.updateById(payment);

        // 更新订单记录
        ordersService.updateStatusByOrderNo(OrderStatusEnum.SUCCESS.getStatus(), payTime, order.getOrderNo());

        // 创建会员记录
        Member member = memberService.selectByUnionIdAndType(order.getUnionId(), order.getType());
        if (member == null) {
            createNewMember(order);
        } else {
            updateMemberInfo(order, member);
        }

        try {
            OrderExtraInfo orderExtraInfo = JSON.parseObject(order.getExtraInfo(), OrderExtraInfo.class);
            if (orderExtraInfo != null && StringUtils.isNotBlank(orderExtraInfo.getDiscountCode())) {
                Member promotionMember = memberService.getByDiscountCode(orderExtraInfo.getDiscountCode());
                LocalDateTime now = LocalDateTime.now();
                if (promotionMember != null && promotionMember.getEndTime().isAfter(now)) {
                    PromotionRecord record = getPromotionRecord(order, promotionMember, now);
                    promotionRecordService.save(record);
                }
            }
        } catch (Exception e) {
            log.error("创建推广记录异常", e);
        }
    }

    @NotNull
    private PromotionRecord getPromotionRecord(Orders order, Member promotionMember, LocalDateTime now) {
        PromotionRecord record = new PromotionRecord();
        record.setUnionId(promotionMember.getUnionId());
        record.setPromotionCode(promotionMember.getPromotionCode());
        record.setObjectId(order.getUnionId());
        record.setOrderNo(order.getOrderNo());
        record.setPayAmount(order.getPayAmount());
        record.setCredit(notionResourceService.getPromotionCredit());
        record.setStatus(0);
        record.setGmtCreate(now);
        record.setGmtUpdate(now);
        record.setDeleted(0);
        return record;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatusToClosed(Orders order, String payNo, LocalDateTime payTime) {
        // 更新支付单记录
        Payment payment = paymentService.getByOrderNo(order.getOrderNo());
        if (payment == null) {
            log.error("更新支付单状态失败,支付单不存在,orderNo={}", order.getOrderNo());
            throw OrderException.create(OrderErrCodeEnum.PAYMENT_NOT_EXIST);
        }
        payment.setPayNo(payNo);
        payment.setStatus(OrderStatusEnum.CLOSED.getStatus());
        paymentService.updateById(payment);

        // 更新订单记录
        ordersService.updateStatusByOrderNo(OrderStatusEnum.CLOSED.getStatus(), payTime, order.getOrderNo());
    }

    private void createNewMember(Orders order) {
        Member member;
        member = new Member();
        member.setUnionId(order.getUnionId());
        member.setOpenId(order.getOpenId());
        member.setStartTime(LocalDateTime.now());
        member.setType(order.getType());
        if (GoodsEnum.PERMANENT_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            member.setEndTime(LocalDateTime.now().plusYears(99));
            member.setVipType(VipTypeEnum.FOREVER_VIP.getType());
        } else if (GoodsEnum.ONE_YEAR_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            member.setEndTime(LocalDateTime.now().plusYears(1));
            member.setVipType(VipTypeEnum.YEAR_VIP.getType());
        } else if (GoodsEnum.TWO_YEAR_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            member.setEndTime(LocalDateTime.now().plusYears(2));
            member.setVipType(VipTypeEnum.YEAR_VIP.getType());
        } else if (GoodsEnum.OB_ONE_YEAR_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            member.setEndTime(LocalDateTime.now().plusYears(1));
            member.setVipType(VipTypeEnum.YEAR_VIP.getType());
        } else if (GoodsEnum.OB_TWO_YEAR_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            member.setEndTime(LocalDateTime.now().plusYears(2));
            member.setVipType(VipTypeEnum.YEAR_VIP.getType());
        } else if (GoodsEnum.OB_PERMANENT_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            member.setEndTime(LocalDateTime.now().plusYears(99));
            member.setVipType(VipTypeEnum.FOREVER_VIP.getType());
        } else {
            log.error("不支持的会员类型,order={}", JSON.toJSONString(order));
        }
        member.setOrderNo(order.getOrderNo());
        member.setPromotionCode(generatePromotionCode());
        member.setGmtCreate(System.currentTimeMillis());
        member.setGmtUpdate(System.currentTimeMillis());
        member.setDeleted(0);
        memberService.save(member);
    }

    private void updateMemberInfo(Orders order, Member member) {
        if (GoodsEnum.ONE_YEAR_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            if (member.getEndTime() == null) {
                member.setEndTime(LocalDateTime.now().plusYears(1));
            } else {
                member.setEndTime(member.getEndTime().plusYears(1));
            }
            member.setVipType(VipTypeEnum.YEAR_VIP.getType());
        } else if (GoodsEnum.TWO_YEAR_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            if (member.getEndTime() == null) {
                member.setEndTime(LocalDateTime.now().plusYears(2));
            } else {
                member.setEndTime(member.getEndTime().plusYears(2));
            }
            member.setVipType(VipTypeEnum.YEAR_VIP.getType());
        } else if (GoodsEnum.PERMANENT_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            if (member.getEndTime() == null) {
                member.setEndTime(LocalDateTime.now().plusYears(99));
            } else {
                member.setEndTime(member.getEndTime().plusYears(99));
            }
            member.setVipType(VipTypeEnum.FOREVER_VIP.getType());
        } else if (GoodsEnum.OB_ONE_YEAR_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            if (member.getEndTime() == null) {
                member.setEndTime(LocalDateTime.now().plusYears(1));
            } else {
                member.setEndTime(member.getEndTime().plusYears(1));
            }
            member.setVipType(VipTypeEnum.YEAR_VIP.getType());
        } else if (GoodsEnum.OB_TWO_YEAR_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            if (member.getEndTime() == null) {
                member.setEndTime(LocalDateTime.now().plusYears(2));
            } else {
                member.setEndTime(member.getEndTime().plusYears(2));
            }
            member.setVipType(VipTypeEnum.YEAR_VIP.getType());
        } else if (GoodsEnum.OB_PERMANENT_MEMBER.getGoodsNo().equals(order.getGoodsNo())) {
            if (member.getEndTime() == null) {
                member.setEndTime(LocalDateTime.now().plusYears(99));
            } else {
                member.setEndTime(member.getEndTime().plusYears(99));
            }
            member.setVipType(VipTypeEnum.FOREVER_VIP.getType());
        } else {
            log.error("不支持的复购充值类型");
        }
        member.setGmtUpdate(System.currentTimeMillis());
        memberService.updateById(member);
    }

    private String generatePromotionCode() {
        int count = 0;
        do {
            String promotionCode = PromotionCodeGenerator.generateRandomCode();
            Member member = memberService.getByDiscountCode(promotionCode);
            if (member == null) {
                return promotionCode;
            }
            count++;
        } while (count < 10);
        return NanoIdUtils.randomNanoId();
    }

    public Integer createMemberByHand(MemberCreateParam param) {
        UserInfo userInfo = userInfoService.getUserInfoByUnionId(param.getUnionId());
        if (userInfo == null || userInfo.getOpenId() == null) {
            throw OrderException.create(OrderErrCodeEnum.USER_MISSING_OPENID);
        }
        Member member = new Member();
        member.setUnionId(param.getUnionId());
        member.setOpenId(userInfo.getOpenId());
        member.setVipType(Objects.requireNonNull(VipTypeEnum.getByType(param.getVipType())).getType());
        member.setStartTime(param.getStartTime());
        if (VipTypeEnum.FOREVER_VIP.getType().equals(param.getVipType())) {
            member.setEndTime(param.getStartTime().plusYears(99));
        } else if (VipTypeEnum.YEAR_VIP.getType().equals(param.getVipType())) {
            member.setEndTime(param.getStartTime().plusYears(1));
        }
        member.setOrderNo("createMemberByHand");
        member.setPromotionCode(NanoIdUtils.randomNanoId());
        member.setRemainCount(0);
        member.setGmtCreate(System.currentTimeMillis());
        member.setGmtUpdate(System.currentTimeMillis());
        member.setDeleted(0);
        memberService.save(member);
        return member.getId();
    }
}
