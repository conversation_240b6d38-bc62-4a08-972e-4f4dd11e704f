package com.maixi.road.admin.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.ArticleFieldDTO;
import com.maixi.road.common.core.model.dto.Field;
import com.maixi.road.common.core.model.response.UserArticleDto;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.UserArticleMapper;
import com.maixi.road.admin.biz.domain.UserArticle;
import com.maixi.road.admin.biz.service.IUserArticleService;
import com.maixi.road.admin.manager.UserManager;
import com.maixi.road.common.integration.notion.enums.NotionTypeEnum;
import com.maixi.road.framework.config.RedisManager;

import jakarta.annotation.Resource;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class UserArticleServiceImpl extends ServiceImpl<UserArticleMapper, UserArticle> implements IUserArticleService {

    @Resource
    private UserManager userManager;
    @Resource
    private RedisManager redisManager;

    @Override
    public UserArticleDto queryArticleDatabaseInfo(String unionId) {
        LambdaQueryWrapper<UserArticle> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserArticle::getUnionId, unionId)
                .orderByDesc(UserArticle::getId)
                .last("LIMIT 1");
        UserArticle userArticle = this.getOne(queryWrapper);
        if (userArticle == null) {
            throw RoadException.create(ErrorCodeEnum.USER_UNAUTHORIZED, "您还未授权绑定 Notion");
        }
        return UserArticleDto.builder()
                .databaseId(userArticle.getDatabaseId())
                .url(userArticle.getUrl())
                .origin(userArticle.getOrigin())
                .author(userArticle.getAuthor())
                .tag(userArticle.getTag())
                .createTime(userArticle.getCreateTime())
                .publishTime(userArticle.getPublishTime())
                .remark(userArticle.getRemark())
                .build();
    }

    @Override
    public UserArticle queryArticle(String unionId) {
        String mainUnionId = userManager.getMainUnionId(unionId);
        LambdaQueryWrapper<UserArticle> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserArticle::getUnionId, mainUnionId)
                .orderByDesc(UserArticle::getId)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }

    /**
     * 查询文章字段
     * @param unionId 用户 Id
     * @return ArticleFieldDTO
     */
    public ArticleFieldDTO queryArticleFieldDTO(String unionId) {
        // FIXME 只有 notioncontroller 的调用才会对 null 进行兜底返回。
        UserArticle article = queryArticle(unionId);
        if (article == null) {
            ArticleFieldDTO articleFieldDTO = new ArticleFieldDTO();
            articleFieldDTO.setDatabaseId(null);
            articleFieldDTO.setAccessToken(null);
            articleFieldDTO.setUnionId(unionId);
            articleFieldDTO.setUrl(Field.builder().supportTypes(NotionTypeEnum.urlType()).build());
            articleFieldDTO.setOrigin(Field.builder().supportTypes(NotionTypeEnum.originType()).build());
            articleFieldDTO.setAuthor(Field.builder().supportTypes(NotionTypeEnum.authorType()).build());
            articleFieldDTO.setTags(Field.builder().supportTypes(NotionTypeEnum.tagsType()).build());
            articleFieldDTO.setRemark(Field.builder().supportTypes(NotionTypeEnum.remarkType()).build());
            articleFieldDTO.setCreateTime(Field.builder().supportTypes(NotionTypeEnum.createdTimeType()).build());
            articleFieldDTO.setPublishTime(Field.builder().supportTypes(NotionTypeEnum.publicTimeType()).build());
            return articleFieldDTO;
        }
        String configJson = article.getConfigJson();
        if (configJson == null || configJson.isEmpty()) {
            // 初始化历史数据。
            return ArticleFieldDTO.builder()
                    .databaseId(article.getDatabaseId())
                    .accessToken(article.getAccessToken())
                    .unionId(unionId)
                    .url(Field.builder().name(article.getUrl()).type("url").supportTypes(NotionTypeEnum.urlType()).build())
                    .origin(Field.builder().name(article.getOrigin()).type("select").supportTypes(NotionTypeEnum.originType()).build())
                    .author(Field.builder().name(article.getAuthor()).type("select").supportTypes(NotionTypeEnum.authorType()).build())
                    .tags(Field.builder().name(article.getTag()).type("multi_select").supportTypes(NotionTypeEnum.tagsType()).build())
                    .remark(Field.builder().name(article.getRemark()).type("rich_text").supportTypes(NotionTypeEnum.remarkType()).build())
                    .createTime(Field.builder().name(article.getCreateTime()).type("created_time").supportTypes(NotionTypeEnum.createdTimeType()).build())
                    .publishTime(Field.builder().name(article.getPublishTime()).type("date").supportTypes(NotionTypeEnum.publicTimeType()).build())
                    .build();
        }
        ArticleFieldDTO articleFieldDTO = JSON.parseObject(configJson, ArticleFieldDTO.class);
        // FIXME 兼容历史数据
        if (StringUtils.isBlank(articleFieldDTO.getAccessToken())) {
            articleFieldDTO.setAccessToken(article.getAccessToken());
        }
        // FIXME 兼容历史数据
        if (StringUtils.isBlank(articleFieldDTO.getDatabaseId())) {
            articleFieldDTO.setDatabaseId(article.getDatabaseId());
        }
        if (StringUtils.isBlank(articleFieldDTO.getUnionId())){
            articleFieldDTO.setUnionId(unionId);
        }
        return articleFieldDTO;
    }

    @Override
    public void removeByUnionId(String loginUnionId) {
        this.remove(new LambdaQueryWrapper<UserArticle>().eq(UserArticle::getUnionId, loginUnionId));
    }

}
