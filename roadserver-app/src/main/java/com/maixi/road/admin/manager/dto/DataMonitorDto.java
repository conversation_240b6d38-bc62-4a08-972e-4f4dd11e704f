package com.maixi.road.admin.manager.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DataMonitorDto {

    private Long resolve;
    private Long article;
    private Long message;
    private Long sync;
    private Long notionMsg;
    private Long notionArticle;
    private Integer sum;
    private Integer count;
    private BigDecimal avg;
    private List<MapDto> top;
}
