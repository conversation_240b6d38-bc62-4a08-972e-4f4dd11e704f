package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.maixi.road.admin.biz.dao.NotionResourceMapper;
import com.maixi.road.admin.biz.domain.NotionResource;
import com.maixi.road.admin.biz.service.INotionResourceService;
import com.maixi.road.common.core.constant.ResourceCodeConstants;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class NotionResourceServiceImpl extends ServiceImpl<NotionResourceMapper, NotionResource>
        implements INotionResourceService {

    // 添加Guava Cache配置
    private final Cache<String, Integer> configCache = CacheBuilder.newBuilder()
            .maximumSize(100) // 设置缓存最大容量为100
            .expireAfterWrite(1, TimeUnit.HOURS)// 设置缓存过期时间为1小时
            .build();

    @Override
    public Integer getDiscount() {
        Integer discount = configCache.getIfPresent(ResourceCodeConstants.DISCOUNT_AMOUNT);
        if (discount != null) {
            // 打印缓存中的数据
            log.info("从缓存中获取折扣,discount={}, cache={}", discount, configCache.asMap());
            return discount;
        }
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.DISCOUNT_AMOUNT)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        Integer result = resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource()))
                .orElse(ResourceCodeConstants.DEFAULT_DISCOUNT_AMOUNT);
        configCache.put(ResourceCodeConstants.DISCOUNT_AMOUNT, result);
        return result;
    }

    @Override
    public Integer getPromotionCredit() {
        Integer promotionCredit = configCache.getIfPresent(ResourceCodeConstants.PROMOTION_CREDIT);
        if (promotionCredit != null) {
            log.info("从缓存中获取推广积分,promotionCredit={}, cache={}", promotionCredit, configCache.asMap());
            return promotionCredit;
        }
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.PROMOTION_CREDIT)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        Integer result = resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource()))
                .orElse(ResourceCodeConstants.DEFAULT_PROMOTION_CREDIT);
        configCache.put(ResourceCodeConstants.PROMOTION_CREDIT, result);
        return result;
    }

    @Override
    public Integer getForeverVipLimitCount() {
        Integer foreverVipLimitCount = configCache.getIfPresent(ResourceCodeConstants.PERMANENT_LIMIT);
        if (foreverVipLimitCount != null) {
            log.info("从缓存中获取永久会员限制数量,foreverVipLimitCount={}, cache={}", foreverVipLimitCount, configCache.asMap());
            return foreverVipLimitCount;
        }
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.PERMANENT_LIMIT)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        Integer result = resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource()))
                .orElse(ResourceCodeConstants.DEFAULT_FOREVER_VIP_LIMIT_COUNT);
        configCache.put(ResourceCodeConstants.PERMANENT_LIMIT, result);
        return result;
    }

    @Override
    public Integer getVipOpen() {
        Integer vipOpen = configCache.getIfPresent(ResourceCodeConstants.VIP_OPEN);
        if (vipOpen != null) {
            log.info("从缓存中获取会员开关,vipOpen={}, cache={}", vipOpen, configCache.asMap());
            return vipOpen;
        }
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.VIP_OPEN)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        Integer result = resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(0);
        configCache.put(ResourceCodeConstants.VIP_OPEN, result);
        return result;
    }

    @Override
    public Integer getBlockSizeLimit() {
        Integer blockSizeLimit = configCache.getIfPresent(ResourceCodeConstants.BLOCK_SIZE_LIMIT);
        if (blockSizeLimit != null) {
            log.info("从缓存中获取块大小限制,blockSizeLimit={}, cache={}", blockSizeLimit, configCache.asMap());
            return blockSizeLimit;
        }
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.BLOCK_SIZE_LIMIT)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        Integer result = resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(500);
        configCache.put(ResourceCodeConstants.BLOCK_SIZE_LIMIT, result);
        return result;
    }

    @Override
    public boolean send2NotionUseCloudFunction() {
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.SEND_2_NOTION_USE_CLOUD_FUNCTION)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        return resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(0) == 1;
    }

    @Override
    public boolean append2NotionUseCloudFunction() {
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.APPEND_2_NOTION_USE_CLOUD_FUNCTION)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        return resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(0) == 1;
    }

    @Override
    public boolean update2NotionUseCloudFunction() {
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.UPDATE_2_NOTION_USE_CLOUD_FUNCTION)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        return resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(0) == 1;
    }

    @Override
    public boolean searchNotionUseCloudFunction() {
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.SEARCH_NOTION_USE_CLOUD_FUNCTION)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        return resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(0) == 1;
    }

    @Override
    public boolean createTokenUseCloudFunction() {
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.CREATE_TOKEN_USE_CLOUD_FUNCTION)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        return resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(0) == 1;
    }

    @Override
    public int getDayLimit() {
        Integer dayLimit = configCache.getIfPresent(ResourceCodeConstants.DAY_LIMIT);
        if (dayLimit != null) {
            log.info("从缓存中获取日限制,dayLimit={}, cache={}", dayLimit, configCache.asMap());
            return dayLimit;
        }
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.DAY_LIMIT)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        Integer result = resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(10);
        configCache.put(ResourceCodeConstants.DAY_LIMIT, result);
        return result;
    }

    @Override
    public int getMonthLimit() {
        Integer monthLimit = configCache.getIfPresent(ResourceCodeConstants.MONTH_LIMIT);
        if (monthLimit != null) {
            log.info("从缓存中获取月限制,monthLimit={}, cache={}", monthLimit, configCache.asMap());
            return monthLimit;
        }
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.MONTH_LIMIT)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        Integer result = resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(30);
        configCache.put(ResourceCodeConstants.MONTH_LIMIT, result);
        return result;
    }

    @Override
    public boolean openOrderCheckJob() {
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.OPEN_ORDER_CHECK_JOB)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        return resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(0) == 1;
    }

    @Override
    public Integer getMaxImageSizeLimit() {
        Integer maxImageSizeLimit = configCache.getIfPresent(ResourceCodeConstants.MAX_IMAGE_SIZE_LIMIT);
        if (maxImageSizeLimit != null) {
            log.info("从缓存中获取最大图片数量限制,maxImageSizeLimit={}, cache={}", maxImageSizeLimit, configCache.asMap());
            return maxImageSizeLimit;
        }
        LambdaQueryWrapper<NotionResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NotionResource::getCode, ResourceCodeConstants.MAX_IMAGE_SIZE_LIMIT)
                .orderByDesc(NotionResource::getId)
                .last("LIMIT 1");
        Optional<NotionResource> resourceOpt = getOneOpt(queryWrapper);
        Integer result = resourceOpt.map(notionResource -> Integer.parseInt(notionResource.getResource())).orElse(30);
        configCache.put(ResourceCodeConstants.MAX_IMAGE_SIZE_LIMIT, result);
        return result;
    }
}
