package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.OssConfigMapper;
import com.maixi.road.admin.biz.domain.OssConfig;
import com.maixi.road.admin.biz.service.IOssConfigService;
import com.maixi.road.common.core.utils.EncryptionUtil;

import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class OssConfigServiceImpl extends ServiceImpl<OssConfigMapper, OssConfig> implements IOssConfigService {
    @Override
    public boolean save(OssConfig entity) {
        entity.setAccessSecret(EncryptionUtil.getEncryptSecret(entity.getAccessSecret()));
        return super.save(entity);
    }

    @Override
    public boolean updateById(OssConfig entity) {
        entity.setAccessSecret(EncryptionUtil.getEncryptSecret(entity.getAccessSecret()));
        return super.updateById(entity);
    }

    @Override
    public OssConfig getOssConfigByUnionId(String unionId) {
        LambdaQueryWrapper<OssConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OssConfig::getUnionId, unionId).eq(OssConfig::getDeleted, 0)
                .orderByDesc(OssConfig::getId)
                .last("LIMIT 1");
        OssConfig config = this.getOne(queryWrapper);
        if (config != null) {
            config.setAccessSecret(EncryptionUtil.getDecryptSecret(config.getAccessSecret()));
        }
        return config;
    }

    @Override
    public Optional<OssConfig> getOssConfigOptByUnionId(String unionId) {
        return Optional.ofNullable(getOssConfigByUnionId(unionId));
    }

    @Override
    public void updateRegionAndEndpoint(Integer id, String region, String endpoint) {
        LambdaUpdateWrapper<OssConfig> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(OssConfig::getId, id)
                .set(OssConfig::getRegion, region)
                .set(OssConfig::getEndpoint, endpoint);
        this.update(lambdaUpdateWrapper);
    }

    @Override
    public void deleteByUnionId(String mainUnionId) {
        LambdaUpdateWrapper<OssConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OssConfig::getUnionId, mainUnionId);
        updateWrapper.set(OssConfig::getDeleted, 1);
        updateWrapper.set(OssConfig::getGmtUpdate, System.currentTimeMillis());
        this.update(updateWrapper);
    }

}
