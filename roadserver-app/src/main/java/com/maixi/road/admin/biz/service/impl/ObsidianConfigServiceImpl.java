package com.maixi.road.admin.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.ObsidianConfigMapper;
import com.maixi.road.admin.biz.domain.ObsidianConfig;
import com.maixi.road.admin.biz.domain.OssConfig;
import com.maixi.road.admin.biz.service.IObsidianConfigService;
import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.biz.service.IOssConfigService;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.ObsidianArticleProperty;
import com.maixi.road.common.core.model.dto.ObsidianMessageProperty;
import com.maixi.road.common.core.model.request.ObsidianSaveRQ;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * <p>
 * Obsidian配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class ObsidianConfigServiceImpl extends ServiceImpl<ObsidianConfigMapper, ObsidianConfig>
        implements IObsidianConfigService {

    @Resource
    private IMemberService memberService;

    @Resource
    private IOssConfigService ossConfigService;

    @Override
    public ObsidianConfig getByUnionId(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            log.warn("getByUnionId: unionId 不能为空");
            return null;
        }

        LambdaQueryWrapper<ObsidianConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObsidianConfig::getUnionId, unionId)
                .eq(ObsidianConfig::getDeleted, 0)
                .orderByDesc(ObsidianConfig::getId)
                .last("LIMIT 1");

        ObsidianConfig config = getOne(queryWrapper);
        log.debug("getByUnionId: unionId={}, config={}", unionId, config != null ? "存在" : "不存在");

        if (config == null) {
            config = initConfig(unionId);
        }
        return config;
    }

    /**
     * 初始化用户的Obsidian配置
     * 
     * @param unionId 用户ID
     * @return 初始化后的Obsidian配置
     * @throws RoadException 当用户不是会员或未配置图床时抛出异常
     */
    public ObsidianConfig initConfig(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            log.warn("initConfig: unionId 不能为空");
            throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "用户ID不能为空");
        }

        // 判断用户是否是会员，如果不是会员，提示用户购买会员
        // boolean isVip = memberService.checkIfVip(unionId);
        // if (!isVip) {
        //     log.warn("initConfig: 用户不是会员，无法初始化Obsidian配置, unionId={}", unionId);
        //     throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "请先购买会员后再配置Obsidian");
        // }

        // 判断用户是否配置了图床(oss_config),如果没有配置，提示用户配置图床
        OssConfig ossConfig = ossConfigService.getOssConfigByUnionId(unionId);
        if (ossConfig == null) {
            log.warn("initConfig: 用户未配置图床，无法初始化Obsidian配置, unionId={}", unionId);
            throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "请先配置图床后再设置Obsidian配置");
        }

        log.info("initConfig: 开始初始化用户Obsidian配置, unionId={}", unionId);

        // 初始化配置
        ObsidianConfig obsidianConfig = new ObsidianConfig();
        obsidianConfig.setUnionId(unionId);
        obsidianConfig.setS3Id(ossConfig.getId()); // 设置用户的图床配置ID
        obsidianConfig.setSaveRoot("obclipper"); // 默认文章保存路径
        obsidianConfig.setAttRoot("mpclipper"); // 默认资源保存路径

        // 初始化文章属性配置 - 定义文章在Obsidian中的字段映射
        ObsidianArticleProperty artInfo = new ObsidianArticleProperty();
        artInfo.setCategory("category");
        artInfo.setTitle("title");
        artInfo.setUrl("url");
        artInfo.setAuthor("author");
        artInfo.setTags("tags");
        artInfo.setOrigin("origin");
        artInfo.setRemark("remark");
        artInfo.setCreateTime("createTime");
        artInfo.setPublishTime("publishTime");
        obsidianConfig.setArtInfo(JSON.toJSONString(artInfo));

        // 初始化消息属性配置 - 定义消息在Obsidian中的字段映射
        ObsidianMessageProperty msgInfo = new ObsidianMessageProperty();
        msgInfo.setCategory("category");
        msgInfo.setTitle("title");
        msgInfo.setTags("tags");
        msgInfo.setCreateTime("createTime");
        obsidianConfig.setMsgInfo(JSON.toJSONString(msgInfo));

        // 设置基础字段
        obsidianConfig.setDeleted(0);
        long currentTime = System.currentTimeMillis();
        obsidianConfig.setGmtCreate(currentTime);
        obsidianConfig.setGmtUpdate(currentTime);

        // 保存配置到数据库
        boolean saveResult = save(obsidianConfig);
        if (!saveResult) {
            log.error("initConfig: 保存Obsidian配置失败, unionId={}", unionId);
            throw RoadException.create(ErrorCodeEnum.INTERNAL_ERROR, "初始化Obsidian配置失败");
        }

        log.info("initConfig: 成功初始化用户Obsidian配置, unionId={}, configId={}", unionId, obsidianConfig.getId());
        return obsidianConfig;
    }

    @Override
    public boolean saveOrUpdateConfig(ObsidianSaveRQ obsidianSaveRQ) {
        if (obsidianSaveRQ == null || StringUtils.isBlank(obsidianSaveRQ.getUnionId())) {
            log.warn("saveOrUpdateConfig: 配置信息或unionId不能为空");
            return false;
        }

        try {
            // 查询是否已存在配置
            ObsidianConfig existingConfig = getByUnionId(obsidianSaveRQ.getUnionId());

            long currentTime = System.currentTimeMillis();

            if (existingConfig != null) {
                // 更新现有配置
                log.info("saveOrUpdateConfig: 更新用户Obsidian配置, unionId={}", obsidianSaveRQ.getUnionId());
                if (StringUtils.isNotBlank(obsidianSaveRQ.getSaveRoot())) {
                    existingConfig.setSaveRoot(obsidianSaveRQ.getSaveRoot());
                }
                if (StringUtils.isNotBlank(obsidianSaveRQ.getAttRoot())) {
                    existingConfig.setAttRoot(obsidianSaveRQ.getAttRoot());
                }
                if (StringUtils.isNotBlank(obsidianSaveRQ.getArtInfo())) {
                    existingConfig.setArtInfo(obsidianSaveRQ.getArtInfo());
                }
                if (StringUtils.isNotBlank(obsidianSaveRQ.getMsgInfo())) {
                    existingConfig.setMsgInfo(obsidianSaveRQ.getMsgInfo());
                }
                if (obsidianSaveRQ.getS3Id() != null) {
                    existingConfig.setS3Id(obsidianSaveRQ.getS3Id());
                }
                existingConfig.setGmtUpdate(currentTime);
                existingConfig.setDeleted(0);
                return updateById(existingConfig);
            } else {
                // 创建新配置
                log.info("saveOrUpdateConfig: 创建用户Obsidian配置, unionId={}", obsidianSaveRQ.getUnionId());
                existingConfig = initConfig(obsidianSaveRQ.getUnionId());
                if (StringUtils.isNotBlank(obsidianSaveRQ.getSaveRoot())) {
                    existingConfig.setSaveRoot(obsidianSaveRQ.getSaveRoot());
                }
                if (StringUtils.isNotBlank(obsidianSaveRQ.getAttRoot())) {
                    existingConfig.setAttRoot(obsidianSaveRQ.getAttRoot());
                }
                if (StringUtils.isNotBlank(obsidianSaveRQ.getArtInfo())) {
                    existingConfig.setArtInfo(obsidianSaveRQ.getArtInfo());
                }
                if (StringUtils.isNotBlank(obsidianSaveRQ.getMsgInfo())) {
                    existingConfig.setMsgInfo(obsidianSaveRQ.getMsgInfo());
                }
                if (obsidianSaveRQ.getS3Id() != null) {
                    existingConfig.setS3Id(obsidianSaveRQ.getS3Id());
                }
                existingConfig.setGmtUpdate(currentTime);
                existingConfig.setDeleted(0);
                return updateById(existingConfig);
            }
        } catch (Exception e) {
            log.error("saveOrUpdateConfig: 保存或更新Obsidian配置失败, unionId={}", obsidianSaveRQ.getUnionId(), e);
            return false;
        }
    }

    @Override
    public boolean removeByUnionId(String unionId) {
        if (StringUtils.isBlank(unionId)) {
            log.warn("removeByUnionId: unionId 不能为空");
            return false;
        }

        try {
            log.info("removeByUnionId: 删除用户Obsidian配置, unionId={}", unionId);

            LambdaUpdateWrapper<ObsidianConfig> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ObsidianConfig::getUnionId, unionId)
                    .eq(ObsidianConfig::getDeleted, 0)
                    .set(ObsidianConfig::getDeleted, 1)
                    .set(ObsidianConfig::getGmtUpdate, System.currentTimeMillis());

            return update(updateWrapper);
        } catch (Exception e) {
            log.error("removeByUnionId: 删除用户Obsidian配置失败, unionId={}", unionId, e);
            return false;
        }
    }
}