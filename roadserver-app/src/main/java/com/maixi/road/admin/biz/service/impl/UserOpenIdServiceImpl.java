package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.UserOpenIdMapper;
import com.maixi.road.admin.biz.domain.UserOpenId;
import com.maixi.road.admin.biz.service.IUserOpenIdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户小程序openId关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Slf4j
@Service
public class UserOpenIdServiceImpl extends ServiceImpl<UserOpenIdMapper, UserOpenId> implements IUserOpenIdService {

    @Override
    public String getOpenIdByUnionIdAndAppId(String unionId, String appId) {
        if (StringUtils.isBlank(unionId) || StringUtils.isBlank(appId)) {
            return null;
        }

        LambdaQueryWrapper<UserOpenId> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserOpenId::getUnionId, unionId)
                .eq(UserOpenId::getAppId, appId)
                .eq(UserOpenId::getDeleted, 0)
                .orderByDesc(UserOpenId::getId)
                .last("LIMIT 1");

        UserOpenId userOpenId = this.getOne(queryWrapper);
        return userOpenId != null ? userOpenId.getOpenId() : null;
    }

    @Override
    public void saveOrUpdateOpenId(String unionId, String appId, String openId) {
        if (StringUtils.isBlank(unionId) || StringUtils.isBlank(appId) || StringUtils.isBlank(openId)) {
            log.warn("saveOrUpdateOpenId: 参数不能为空, unionId={}, appId={}, openId={}", unionId, appId, openId);
            return;
        }

        // 查询是否已存在记录
        LambdaQueryWrapper<UserOpenId> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserOpenId::getUnionId, unionId)
                .eq(UserOpenId::getAppId, appId)
                .eq(UserOpenId::getDeleted, 0);

        UserOpenId existingRecord = this.getOne(queryWrapper);

        if (existingRecord != null) {
            // 更新现有记录
            if (!openId.equals(existingRecord.getOpenId())) {
                existingRecord.setOpenId(openId);
                existingRecord.setGmtUpdate(LocalDateTime.now());
                this.updateById(existingRecord);
                log.info("更新用户openId, unionId={}, appId={}, oldOpenId={}, newOpenId={}",
                        unionId, appId, existingRecord.getOpenId(), openId);
            }
        } else {
            // 创建新记录
            UserOpenId newRecord = new UserOpenId();
            newRecord.setUnionId(unionId);
            newRecord.setAppId(appId);
            newRecord.setOpenId(openId);
            newRecord.setGmtCreate(LocalDateTime.now());
            newRecord.setGmtUpdate(LocalDateTime.now());
            newRecord.setDeleted(0);
            this.save(newRecord);
            log.info("保存用户openId, unionId={}, appId={}, openId={}", unionId, appId, openId);
        }
    }

    @Override
    public void clearOpenId(String unionId, String appId) {
        if (StringUtils.isBlank(unionId) || StringUtils.isBlank(appId)) {
            log.warn("clearOpenId: 参数不能为空, unionId={}, appId={}", unionId, appId);
            return;
        }

        LambdaQueryWrapper<UserOpenId> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserOpenId::getUnionId, unionId)
                .eq(UserOpenId::getAppId, appId)
                .eq(UserOpenId::getDeleted, 0);

        UserOpenId userOpenId = this.getOne(queryWrapper);
        if (userOpenId != null) {
            userOpenId.setDeleted(1);
            userOpenId.setGmtUpdate(LocalDateTime.now());
            this.updateById(userOpenId);
            log.info("清空用户openId, unionId={}, appId={}", unionId, appId);
        }
    }
}