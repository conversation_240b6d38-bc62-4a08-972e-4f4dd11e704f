package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.Member;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IMemberService extends IService<Member> {

    boolean checkPromotionCode(String promotionCode);

    Integer remainForeverVipCount();

    boolean vipOpen(String unionId);

    Integer getVipType(String unionId);

    Member selectByUnionId(String unionId);

    Member selectByUnionIdAndType(String unionId, Integer type);

    Member getByDiscountCode(String discountCode);

    boolean checkIfVip(String unionId);
}
