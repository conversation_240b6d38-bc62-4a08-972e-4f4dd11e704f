package com.maixi.road.admin.web.controller;

import java.time.LocalDateTime;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.maixi.road.admin.biz.domain.NotionAdvise;
import com.maixi.road.admin.biz.service.INotionAdviceService;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.request.AdviceRQ;
import com.maixi.road.framework.web.Road;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/miniprogram")
public class NotionAdviceController {

    @Resource
    private INotionAdviceService notionAdviceService;

    @RequestMapping("/feedback/submit")
    public Result<Boolean> notionAdvice(@RequestBody AdviceRQ adviceRQ) {
        log.info("adviceRQ={}", adviceRQ);
        NotionAdvise notionAdvice = new NotionAdvise();
        notionAdvice.setType(adviceRQ.getType());
        notionAdvice.setContent(adviceRQ.getContent());
        notionAdvice.setUnionId(Road.getLoginUser().getUnionId());
        notionAdvice.setCreateTime(LocalDateTime.now());
        notionAdviceService.save(notionAdvice);
        return Result.success(true);
    }
}
