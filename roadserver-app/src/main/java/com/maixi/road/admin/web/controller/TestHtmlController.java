package com.maixi.road.admin.web.controller;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.core.model.dto.Result;

import lombok.extern.slf4j.Slf4j;

/**
 * 测试HTML控制器
 * <p>
 * 用于提供测试HTML内容，方便验证MpParser的解析逻辑
 * </p>
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestHtmlController extends BaseController {

    /**
     * 提供测试HTML页面
     * <p>
     * 返回完整的HTML页面，包含各种HTML元素，用于测试解析功能
     * </p>
     * 
     * @return HTML内容
     */
    @GetMapping(value = "/html", produces = MediaType.TEXT_HTML_VALUE)
    public ResponseEntity<String> getTestHtml() {
        try {
            // 从resources目录读取测试HTML文件
            ClassPathResource resource = new ClassPathResource("test-complex-html.html");
            String htmlContent = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);

            log.info("成功提供测试HTML内容，内容长度: {} 字符", htmlContent.length());

            // 设置HTTP响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_HTML);
            headers.set("Content-Language", "zh-CN");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(htmlContent);

        } catch (IOException e) {
            log.error("读取测试HTML文件失败", e);
            String errorHtml = """
                    <!DOCTYPE html>
                    <html>
                    <head><title>错误</title></head>
                    <body>
                        <h1>测试HTML文件读取失败</h1>
                        <p>错误信息: %s</p>
                    </body>
                    </html>
                    """.formatted(e.getMessage());

            return ResponseEntity.internalServerError()
                    .contentType(MediaType.TEXT_HTML)
                    .body(errorHtml);
        }
    }

    /**
     * 提供简单测试HTML
     * <p>
     * 返回一个简化版本的测试HTML，用于快速测试
     * </p>
     * 
     * @return 简单HTML内容
     */
    @GetMapping(value = "/simple-html", produces = MediaType.TEXT_HTML_VALUE)
    public ResponseEntity<String> getSimpleTestHtml() {
        String simpleHtml = """
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <title>简单测试页面</title>
                </head>
                <body>
                    <h1>简单测试页面</h1>
                    <p>这是一个<strong>简单的测试</strong>页面。</p>

                    <h2>简单列表</h2>
                    <ul>
                        <li>简单列表项1</li>
                        <li>简单列表项2
                            <ul>
                                <li>嵌套项1</li>
                                <li>嵌套项2</li>
                            </ul>
                        </li>
                    </ul>

                    <h2>图片测试</h2>
                    <ul>
                        <li>带图片的列表项
                            <img src="https://via.placeholder.com/100x100/blue/white?text=Test" alt="测试图片">
                        </li>
                    </ul>
                </body>
                </html>
                """;

        log.info("提供简单测试HTML内容");

        return ResponseEntity.ok()
                .contentType(MediaType.TEXT_HTML)
                .body(simpleHtml);
    }

    /**
     * 提供JSON格式的测试页面信息
     * <p>
     * 返回测试页面的基本信息，用于API调用
     * </p>
     * 
     * @return 测试页面信息
     */
    @GetMapping("/info")
    public Result<TestHtmlInfo> getTestHtmlInfo() {
        TestHtmlInfo info = new TestHtmlInfo();
        info.setComplexHtmlUrl("/test/html");
        info.setSimpleHtmlUrl("/test/simple-html");
        info.setDescription("测试HTML页面，用于验证MpParser解析逻辑");
        info.setFeatures(new String[] {
                "多级嵌套列表",
                "列表项包含图片",
                "富文本格式",
                "表格",
                "代码块",
                "引用块",
                "链接和书签"
        });

        log.info("提供测试HTML信息");
        return Result.success(info);
    }

    /**
     * 触发解析测试
     * <p>
     * 这个接口可以用来直接测试解析器的解析功能
     * 使用本地的测试HTML URL，兼容现有的解析器调用方式
     * </p>
     * 
     * @param type 测试类型（complex或simple）
     * @return 测试URL
     */
    @GetMapping("/parse")
    public Result<TestParseInfo> triggerParseTest(String type) {
        // 构建本地HTML URL
        String baseUrl = getBaseUrl(); // 动态获取当前服务的基础URL
        String htmlUrl;

        if ("simple".equals(type)) {
            htmlUrl = baseUrl + "/test/simple-html";
        } else {
            htmlUrl = baseUrl + "/test/html";
        }

        log.info("准备触发解析测试，目标URL: {}", htmlUrl);

        TestParseInfo parseInfo = new TestParseInfo();
        parseInfo.setTestUrl(htmlUrl);
        parseInfo.setType(type);
        parseInfo.setDescription("可以使用此URL进行解析器测试，支持现有的所有解析逻辑");
        parseInfo.setUsageInstructions(new String[] {
                "1. 复制 testUrl 到你的解析器测试中",
                "2. 此URL会返回标准的HTML内容",
                "3. 包含各种HTML元素用于验证解析逻辑",
                "4. 支持嵌套列表、图片、富文本等复杂结构"
        });

        return Result.success(parseInfo);
    }

    /**
     * 获取当前服务的基础URL
     * 
     * @return 基础URL
     */
    private String getBaseUrl() {
        // 可以从配置中读取，或者动态获取
        // 这里提供一个默认值，实际使用时可以根据环境调整
        return "http://localhost:8080";
    }

    /**
     * 测试HTML信息类
     */
    public static class TestHtmlInfo {
        private String complexHtmlUrl;
        private String simpleHtmlUrl;
        private String description;
        private String[] features;

        // Getters and Setters
        public String getComplexHtmlUrl() {
            return complexHtmlUrl;
        }

        public void setComplexHtmlUrl(String complexHtmlUrl) {
            this.complexHtmlUrl = complexHtmlUrl;
        }

        public String getSimpleHtmlUrl() {
            return simpleHtmlUrl;
        }

        public void setSimpleHtmlUrl(String simpleHtmlUrl) {
            this.simpleHtmlUrl = simpleHtmlUrl;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String[] getFeatures() {
            return features;
        }

        public void setFeatures(String[] features) {
            this.features = features;
        }
    }

    /**
     * 测试解析信息类
     */
    public static class TestParseInfo {
        private String testUrl;
        private String type;
        private String description;
        private String[] usageInstructions;

        // Getters and Setters
        public String getTestUrl() {
            return testUrl;
        }

        public void setTestUrl(String testUrl) {
            this.testUrl = testUrl;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String[] getUsageInstructions() {
            return usageInstructions;
        }

        public void setUsageInstructions(String[] usageInstructions) {
            this.usageInstructions = usageInstructions;
        }
    }
}