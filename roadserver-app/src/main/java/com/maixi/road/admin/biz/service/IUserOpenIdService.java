package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.UserOpenId;

/**
 * <p>
 * 用户小程序openId关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IUserOpenIdService extends IService<UserOpenId> {

    /**
     * 根据unionId和appId获取openId
     *
     * @param unionId 用户unionId
     * @param appId   小程序appId
     * @return openId
     */
    String getOpenIdByUnionIdAndAppId(String unionId, String appId);

    /**
     * 保存或更新用户openId
     *
     * @param unionId 用户unionId
     * @param appId   小程序appId
     * @param openId  openId
     */
    void saveOrUpdateOpenId(String unionId, String appId, String openId);

    /**
     * 清空用户在指定小程序的openId
     *
     * @param unionId 用户unionId
     * @param appId   小程序appId
     */
    void clearOpenId(String unionId, String appId);
}