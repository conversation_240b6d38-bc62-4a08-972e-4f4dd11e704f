package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.WxUserCpMapper;
import com.maixi.road.admin.biz.domain.WxUserCp;
import com.maixi.road.admin.biz.service.IWxUserCpService;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 微信的fromUser标识与unionId的关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class WxUserCpServiceImpl extends ServiceImpl<WxUserCpMapper, WxUserCp> implements IWxUserCpService {

    @Override
    public List<WxUserCp> getByWxUserIdList(List<String> fromUserIdList) {
        LambdaQueryWrapper<WxUserCp> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WxUserCp::getWxUserId, fromUserIdList);
        return list(wrapper);
    }

    @Override
    public WxUserCp getWxUserByUnionId(String unionId) {
        LambdaQueryWrapper<WxUserCp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxUserCp::getUnionId, unionId)
                .orderByDesc(WxUserCp::getId)
                .last("LIMIT 1");
        return getOne(wrapper);
    }
}
