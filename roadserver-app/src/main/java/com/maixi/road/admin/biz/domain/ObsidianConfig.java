package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * Obsidian配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("obsidian_config")
public class ObsidianConfig {

    /**
     * 主键 id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 文章属性
     */
    @TableField("art_info")
    private String artInfo;

    /**
     * 消息属性
     */
    @TableField("msg_info")
    private String msgInfo;

    /**
     * s3配置id
     */
    @TableField("s3_id")
    private Integer s3Id;

    /**
     * 文章保存路径
     */
    @TableField("save_root")
    private String saveRoot;

    /**
     * 资源保存路径
     */
    @TableField("att_root")
    private String attRoot;

    /**
     * 创建时间戳
     */
    @TableField("gmt_create")
    private Long gmtCreate;

    /**
     * 更新时间戳
     */
    @TableField("gmt_update")
    private Long gmtUpdate;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Integer deleted;
}