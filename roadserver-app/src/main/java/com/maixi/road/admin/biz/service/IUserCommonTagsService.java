package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.UserCommonTags;
import com.maixi.road.common.core.model.dto.UserCommonTagsDTO;

import java.util.List;

/**
 * 用户常用标签服务接口
 * 提供用户常用标签的业务逻辑处理
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface IUserCommonTagsService extends IService<UserCommonTags> {

    /**
     * 根据用户ID获取常用标签
     * 
     * @param unionId 用户唯一标识
     * @return UserCommonTagsDTO 用户常用标签信息，如果不存在则返回null
     */
    UserCommonTagsDTO getUserCommonTags(String unionId);

    /**
     * 同步用户常用标签
     * 如果用户已有常用标签记录则更新，否则新建
     * 
     * @param unionId 用户唯一标识
     * @param tags    标签列表
     * @return boolean 操作是否成功
     */
    boolean syncUserCommonTags(String unionId, List<String> tags);

    /**
     * 删除用户常用标签
     * 执行软删除操作
     * 
     * @param unionId 用户唯一标识
     * @return boolean 操作是否成功
     */
    boolean deleteUserCommonTags(String unionId);
}