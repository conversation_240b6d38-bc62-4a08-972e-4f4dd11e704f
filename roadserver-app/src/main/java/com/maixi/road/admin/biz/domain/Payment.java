package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <p>
 * 支付单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("payment")
public class Payment {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * unionId
     */
    @TableField("union_id")
    private String unionId;

    /**
     * openId
     */
    @TableField("open_id")
    private String openId;

    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 支付单号
     */
    @TableField("pay_no")
    private String payNo;

    /**
     * 支付类型
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 支付金额
     */
    @TableField("pay_amount")
    private Integer payAmount;

    /**
     * 货币类型
     */
    @TableField("currency")
    private String currency;

    /**
     * 支付状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Integer deleted;
}
