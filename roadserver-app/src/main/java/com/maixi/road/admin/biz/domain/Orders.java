package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p>
 * 订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("orders")
public class Orders {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 订单流水号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 商品编号
     */
    @TableField("goods_no")
    private String goodsNo;

    /**
     * 用户open_id
     */
    @TableField("open_id")
    private String openId;

    /**
     * 用户 union_id
     */
    @TableField("union_id")
    private String unionId;

    /**
     *  订单状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 订单价格（单位分）
     */
    @TableField("amount")
    private Integer amount;

    /**
     * 优惠金额
     */
    @TableField("discount")
    private Integer discount;

    /**
     * 实际支付金额
     */
    @TableField("pay_amount")
    private Integer payAmount;

    /**
     * 支付类型
     */
    @TableField("pay_type")
    private Integer payType;

    /**
     * 扩展信息
     */
    @TableField("extra_info")
    private String extraInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Integer deleted;
}
