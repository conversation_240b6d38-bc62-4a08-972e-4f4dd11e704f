package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 企业微信消息处理失败记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
@Getter
@Setter
@TableName("wx_message_process_error")
public class WxMessageProcessError {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * unionId
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 消息发送者ID
     */
    @TableField("from_user_id")
    private String fromUserId;

    /**
     * 消息类型
     */
    @TableField("msg_type")
    private String msgType;

    /**
     * 消息ID
     */
    @TableField("msg_id")
    private String msgId;

    /**
     * 错误信息
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}
