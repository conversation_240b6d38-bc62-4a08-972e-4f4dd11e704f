package com.maixi.road.admin.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.maixi.road.admin.biz.domain.NotionResource;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface INotionResourceService extends IService<NotionResource> {

    Integer getDiscount();

    Integer getPromotionCredit();

    Integer getForeverVipLimitCount();

    Integer getVipOpen();

    Integer getBlockSizeLimit();

    Integer getMaxImageSizeLimit();

    boolean send2NotionUseCloudFunction();

    boolean append2NotionUseCloudFunction();

    boolean update2NotionUseCloudFunction();

    boolean searchNotionUseCloudFunction();

    boolean createTokenUseCloudFunction();

    int getDayLimit();

    int getMonthLimit();

    boolean openOrderCheckJob();
}
