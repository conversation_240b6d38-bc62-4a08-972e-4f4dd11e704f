package com.maixi.road.admin.biz.service;

import com.maixi.road.admin.biz.domain.Goods;
import com.maixi.road.common.core.model.response.GoodsVo;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IGoodsService extends IService<Goods> {

    List<GoodsVo> getGoodsList();

    Goods getByNo(String goodsNo);

    List<GoodsVo> getGoodsListObsidian();

}
