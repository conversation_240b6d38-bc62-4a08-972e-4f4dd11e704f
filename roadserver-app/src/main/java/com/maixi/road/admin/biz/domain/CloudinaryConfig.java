package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("cloudinary_config")

public class CloudinaryConfig {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    @TableField("union_id")
    private String unionId;


    @TableField("cloud_name")
    private String cloudName;


    @TableField("api_key")
    private String apiKey;


    @TableField("api_secret")
    private String apiSecret;
}
