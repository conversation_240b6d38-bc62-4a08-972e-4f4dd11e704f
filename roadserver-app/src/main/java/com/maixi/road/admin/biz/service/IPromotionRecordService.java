package com.maixi.road.admin.biz.service;

import com.maixi.road.admin.biz.domain.PromotionRecord;
import com.maixi.road.common.core.model.response.PromotionData;
import com.maixi.road.common.core.model.response.PromotionVo;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 推广记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
public interface IPromotionRecordService extends IService<PromotionRecord> {

    List<PromotionVo> creditListByUnionId(String unionId);

    PromotionData promotionData(String unionIdByHeader);

}
