package com.maixi.road.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.maixi.road.admin.biz.dao.PaymentMapper;
import com.maixi.road.admin.biz.domain.Payment;
import com.maixi.road.admin.biz.service.IPaymentService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 支付单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Service
public class PaymentServiceImpl extends ServiceImpl<PaymentMapper, Payment> implements IPaymentService {

    @Override
    public Payment getByOrderNo(String orderNo) {
        LambdaQueryWrapper<Payment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Payment::getOrderNo, orderNo).eq(Payment::getDeleted, 0);
        return getOne(wrapper);
    }
}
