package com.maixi.road.admin.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户小程序openId关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-21
 */
@Getter
@Setter
@TableName("user_open_id")
public class UserOpenId {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户unionId
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 小程序appId
     */
    @TableField("app_id")
    private String appId;

    /**
     * 对应的openId
     */
    @TableField("open_id")
    private String openId;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_update")
    private LocalDateTime gmtUpdate;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    @TableField("deleted")
    private Integer deleted;
}