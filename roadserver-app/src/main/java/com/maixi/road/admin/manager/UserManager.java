package com.maixi.road.admin.manager;

import java.util.Optional;

import org.springframework.stereotype.Service;

import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.framework.web.MainUserWrapper;
import com.maixi.road.framework.web.Road;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

import jakarta.annotation.Resource;

/**
 * 用户管理器
 * 处理用户相关的业务逻辑，包括用户信息的获取和包装
 */
@Service
public class UserManager {

    // 添加Guava Cache配置
    private final Cache<String, Integer> unionIdCache = CacheBuilder.newBuilder()
            .maximumSize(100)  // 设置缓存最大容量为100
            .build();

    @Resource
    private IUserInfoService userInfoService;

    /**
     * 获取主用户包装对象
     * 该方法根据当前登录用户信息构建MainUserWrapper对象，并填充主用户相关信息
     *
     * @return MainUserWrapper 包含用户标识和主用户状态的包装对象
     */
    public MainUserWrapper getMainUserWrapper() {
        // 构建基础用户包装对象，包含当前登录用户的unionId和userId
        MainUserWrapper mainUserWrapper = MainUserWrapper.builder()
            .unionId(Road.getLoginUser().getUnionId())
            .userId(Road.getLoginUser().getUserId())
            .build();

        // 查询用户详细信息
        Optional<UserInfo> mainUserOpt = userInfoService.getUserInfoOptBySubUnionId(mainUserWrapper.getUnionId());
        
        if (mainUserOpt.isPresent()) {
            // 如果找到用户信息，设置主用户相关属性
            mainUserWrapper.setMainUnionId(mainUserOpt.get().getUnionId());
            mainUserWrapper.setMainUserId(mainUserOpt.get().getId());
            mainUserWrapper.setMainUser(false);
            return mainUserWrapper;
        }
        
        // 如果未找到用户信息，标记为非主用户
        mainUserWrapper.setMainUnionId(mainUserWrapper.getUnionId());
        mainUserWrapper.setMainUserId(mainUserWrapper.getUserId());
        mainUserWrapper.setMainUser(true);
        return mainUserWrapper;
    }

    public String getMainUnionId(String unionId) {
        Optional<UserInfo> mainUserOpt = userInfoService.getUserInfoOptBySubUnionId(unionId);
        if (mainUserOpt.isPresent()) {
            return mainUserOpt.get().getUnionId();
        }
        return unionId;
    }

    public Integer getByUnionId(String unionId) {
        // 首先尝试从缓存中获取
        Integer cachedId = unionIdCache.getIfPresent(unionId);
        if (cachedId != null) {
            return cachedId;
        }

        // 缓存中没有，则查询数据库
        Optional<UserInfo> userInfoOpt = userInfoService.getUserInfoOptByUnionId(unionId);
        if (userInfoOpt.isPresent()) {
            Integer userId = userInfoOpt.get().getId();
            // 将结果放入缓存
            unionIdCache.put(unionId, userId);
            return userId;
        }
        return null;
    }

}
