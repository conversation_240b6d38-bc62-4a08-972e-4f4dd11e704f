package com.maixi.road.wechat.manager;

import static com.maixi.road.wechat.constants.CpMsgConstants.ACTION;
import static com.maixi.road.wechat.constants.CpMsgConstants.ACTION_VALUE_SEND;
import static com.maixi.road.wechat.constants.CpMsgConstants.AGENT_ID;
import static com.maixi.road.wechat.constants.CpMsgConstants.FROM;
import static com.maixi.road.wechat.constants.CpMsgConstants.LAST_MSG_SEQ;
import static com.maixi.road.wechat.constants.CpMsgConstants.MSG_ID;
import static com.maixi.road.wechat.constants.CpMsgConstants.MSG_TYPE;
import static com.maixi.road.wechat.constants.CpMsgConstants.NOTION_MP_CLIPPER;
import static com.maixi.road.wechat.constants.CpMsgConstants.ROOM_ID;
import static com.maixi.road.wechat.constants.CpMsgConstants.TO_LIST;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.maixi.road.admin.biz.domain.Member;
import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.domain.WxMessageProcessError;
import com.maixi.road.admin.biz.domain.WxUserCp;
import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.admin.biz.service.IUserMessageService;
import com.maixi.road.admin.biz.service.IWxMessageProcessErrorService;
import com.maixi.road.admin.biz.service.IWxUserCpService;
import com.maixi.road.common.business.user.enums.VipTypeEnum;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.service.redis.constant.RedisKeys;
import com.maixi.road.framework.config.RedisManager;
import com.maixi.road.framework.config.WxCpConfiguration;
import com.maixi.road.wechat.chatdata.Processor;
import com.maixi.road.wechat.chatdata.ProcessorFactory;
import com.maixi.road.wechat.constants.CpMsgType;
import com.maixi.road.wechat.dto.UserTagDto;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpMsgAuditService;
import me.chanjar.weixin.cp.bean.msgaudit.WxCpChatDatas;

@Slf4j
@Component
public class CpWxMessageManager {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IWxUserCpService wxUserCpService;
    @Resource
    private IUserMessageService userMessageService;
    @Resource
    private ProcessorFactory processorFactory;
    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private IMemberService memberService;
    @Resource
    private RedisManager redisManager;
    @Resource
    private IWxMessageProcessErrorService wxMessageProcessErrorService;

    /**
     * 同步企业微信消息
     * <p>
     * 该方法负责从企业微信API同步获取新消息并进行处理。主要功能包括：
     * 1. 获取上次同步的消息序列号作为起始点
     * 2. 使用分布式锁确保消息处理的原子性和互斥性
     * 3. 调用企业微信消息审计服务获取新消息
     * 4. 处理获取到的消息（包括消息过滤、用户绑定关系处理、标签处理等）
     */
    public void syncWxMessages() {
        long lastSeq = Optional.ofNullable(redissonClient.<Long>getBucket(LAST_MSG_SEQ).get()).orElse(0L);
        RLock lock = redissonClient.getLock("CP_MESSAGE_PROCESS_LOCK:" + lastSeq);
        try {
            boolean isLock = lock.tryLock(0L, 5L, TimeUnit.MINUTES);
            if (!isLock) {
                log.warn("获取企微助手消息处理锁失败,lastSeq={}", lastSeq);
                return;
            }
            log.info("本次拉取消息开始,seq={}", lastSeq);
            lastSeq = fetchAndProcessWxMessages(lastSeq);
            redissonClient.<Long>getBucket(LAST_MSG_SEQ).set(lastSeq);
        } catch (Exception e) {
            log.error("processCpWxMsg occur exception", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.warn("释放企微助手消息处理锁,lastSeq={}", lastSeq);
                lock.unlock();
            }
        }
    }

    /**
     * 获取企业微信消息审计服务
     *
     * @return 企业微信消息审计服务实例
     */
    private WxCpMsgAuditService getMsgAuditService() {
        return WxCpConfiguration.getCpService(AGENT_ID).getMsgAuditService();
    }

    /**
     * 获取并处理企业微信消息
     *
     * @param lastSeq 上次处理的消息序列号
     * @return 更新后的消息序列号
     * @throws Exception 处理过程中可能抛出的异常
     */
    private long fetchAndProcessWxMessages(long lastSeq) throws Exception {
        while (true) {
            // 批量获取消息，每次最多获取50条
            WxCpChatDatas data = getMsgAuditService().getChatDatas(lastSeq, 50, null, null, 5);
            if (data.getChatData().isEmpty()) {
                // 没有新消息，退出循环
                break;
            }
            // 处理获取到的消息，并更新lastSeq
            lastSeq = processChatData(lastSeq, data);
        }
        return lastSeq;
    }

    /**
     * 处理企业微信聊天数据
     *
     * @param lastSeq 上次处理的消息序列号
     * @param data    企业微信聊天数据
     * @return 更新后的消息序列号
     */
    private long processChatData(long lastSeq, WxCpChatDatas data) {
        // 当前拉取的最大 seq
        Long maxSeq = data.getChatData().getLast().getSeq();

        // 1. 提取消息
        Map<String, Long> msgId2SeqMap = Maps.newHashMap();
        List<JSONObject> messages = extractClippableMessages(data, msgId2SeqMap);

        // 2. 获取用户绑定关系
        Map<String, String> from2UnionMap = extractUserBindingRelations(messages);

        // 3. 处理标签数据
        Map<Integer, UserTagDto> userTagsMap = extractMessageTags(messages);

        // 4. 处理消息
        lastSeq = batchProcessMessages(lastSeq, messages, msgId2SeqMap, from2UnionMap, userTagsMap,
                data);

        // 5. 更新序列号
        return Math.max(maxSeq, lastSeq);
    }

    /**
     * 提取用户绑定关系
     *
     * @param messages 消息列表
     * @return 用户ID到unionId的映射
     */
    private Map<String, String> extractUserBindingRelations(List<JSONObject> messages) {
        Map<String, String> from2UnionMap = Maps.newHashMap();

        // 提取所有发送者ID
        List<String> fromUserIdList = messages.stream()
                .map(e -> e.getString(FROM))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fromUserIdList)) {
            return from2UnionMap;
        }

        // 查询已绑定的用户
        List<WxUserCp> wxUserIdList = wxUserCpService.getByWxUserIdList(fromUserIdList);
        if (CollectionUtils.isEmpty(wxUserIdList)) {
            return from2UnionMap;
        }

        // 处理主账号映射
        Map<String, String> subMainUserIdMap = mapSubAccountsToMainAccounts(wxUserIdList);

        // 处理会员绑定关系
        linkMembersWithAccounts(wxUserIdList, subMainUserIdMap, from2UnionMap);

        return from2UnionMap;
    }

    /**
     * 映射子账号到主账号
     *
     * @param wxUserIdList 微信用户列表
     * @return 子账号到主账号的映射
     */
    private Map<String, String> mapSubAccountsToMainAccounts(List<WxUserCp> wxUserIdList) {
        Map<String, String> subMainUserIdMap = Maps.newHashMap();

        // 提取所有unionId
        List<String> unionIdList = wxUserIdList.stream()
                .map(WxUserCp::getUnionId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(unionIdList)) {
            return subMainUserIdMap;
        }

        // 查询主账号
        List<UserInfo> mainUserList = userInfoService.getBySubUserIdList(unionIdList);
        if (!CollectionUtils.isEmpty(mainUserList)) {
            subMainUserIdMap = mainUserList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(UserInfo::getSubUserId, UserInfo::getUnionId));
        }

        return subMainUserIdMap;
    }

    /**
     * 关联会员与账号
     *
     * @param wxUserIdList     微信用户列表
     * @param subMainUserIdMap 子账号到主账号的映射
     * @param from2UnionMap    用户ID到unionId的映射
     */
    private void linkMembersWithAccounts(List<WxUserCp> wxUserIdList, Map<String, String> subMainUserIdMap,
                                         Map<String, String> from2UnionMap) {
        for (WxUserCp wxUserCp : wxUserIdList) {
            String mainUnionId = subMainUserIdMap.get(wxUserCp.getUnionId());
            String bindUnionId = StringUtils.isBlank(mainUnionId) ? wxUserCp.getUnionId() : mainUnionId;

            Member member = memberService.selectByUnionId(bindUnionId);
            if (member == null) {
                continue;
            }

            // 永久会员可以子账号绑定
            if (VipTypeEnum.FOREVER_VIP.getType().equals(member.getVipType())) {
                from2UnionMap.put(wxUserCp.getWxUserId(), bindUnionId);
            }

            // 年卡会员，只能自己绑。即当前账号是年卡会员类型
            if (StringUtils.isBlank(mainUnionId) && VipTypeEnum.YEAR_VIP.getType().equals(member.getVipType())) {
                from2UnionMap.put(wxUserCp.getWxUserId(), bindUnionId);
            }
        }
    }

    /**
     * 提取消息标签
     *
     * @param messages 消息列表
     * @return 消息索引到标签的映射
     */
    private Map<Integer, UserTagDto> extractMessageTags(List<JSONObject> messages) {
        Map<Integer, UserTagDto> userTagsMap = Maps.newHashMap();

        for (int i = 0; i < messages.size(); i++) {
            JSONObject message = messages.get(i);
            String fromUserId = message.getString(FROM);

            // 只处理文本类型消息
            if (!message.getString(MSG_TYPE).equals(CpMsgType.TEXT)) {
                continue;
            }

            String text = message.getJSONObject("text").getString("content");
            if (text.trim().matches("^[#＃].*")) {
                List<String> tags = Arrays.stream(text.split("[#＃]"))
                        .filter(t -> !t.isEmpty())
                        .collect(Collectors.toList());
                userTagsMap.put(i, new UserTagDto(fromUserId, tags));
            }
        }

        log.info("本次拉取了{}条数据，其中的标签数据：userTagsMap={}", messages.size(), userTagsMap);
        return userTagsMap;
    }

    /**
     * 批量处理消息
     *
     * @param lastSeq       上次处理的消息序列号
     * @param messages      消息列表
     * @param msgId2SeqMap  消息ID到序列号的映射
     * @param from2UnionMap 用户ID到unionId的映射
     * @param userTagsMap   消息索引到标签的映射
     * @param data          企业微信聊天数据
     * @return 更新后的消息序列号
     */
    private long batchProcessMessages(
            long lastSeq, List<JSONObject> messages,
            Map<String, Long> msgId2SeqMap,
            Map<String, String> from2UnionMap,
            Map<Integer, UserTagDto> userTagsMap,
            WxCpChatDatas data) {

        for (int i = 0; i < messages.size(); i++) {
            JSONObject message = messages.get(i);
            String msgType = message.getString(MSG_TYPE);
            String fromUserId = message.getString(FROM);
            // 获取当前消息的序列号，用于日志记录
            Long msgSeq = msgId2SeqMap.get(message.getString(MSG_ID));
            log.debug("当前消息序列号: {}", msgSeq);

            log.info("正在处理第{}条消息，消息类型是{}", i, msgType);

            // 处理单条消息并更新序列号
            handleSingleMessage(lastSeq, message, msgType, fromUserId, from2UnionMap,
                    userTagsMap, i, messages, data);
        }

        return lastSeq;
    }

    /**
     * 处理单条消息
     *
     * @param lastSeq       上次处理的消息序列号
     * @param message       消息内容
     * @param msgType       消息类型
     * @param fromUserId    发送者ID
     * @param from2UnionMap 用户ID到unionId的映射
     * @param userTagsMap   消息索引到标签的映射
     * @param messageIndex  消息在列表中的索引
     * @param messages      完整的消息列表
     * @param data          企业微信聊天数据
     * @return 更新后的消息序列号
     */
    private long handleSingleMessage(long lastSeq,
                                     JSONObject message,
                                     String msgType,
                                     String fromUserId,
                                     Map<String, String> from2UnionMap,
                                     Map<Integer, UserTagDto> userTagsMap,
                                     int messageIndex,
                                     List<JSONObject> messages,
                                     WxCpChatDatas data) {
        String unionId = from2UnionMap.get(fromUserId);
        // 用户未绑定，处理绑定消息
        if (StringUtils.isBlank(unionId)) {
            handleAccountBinding(message, msgType, fromUserId);
            return lastSeq;
        }
        // 处理消息剪藏
        clipMessageContent(userTagsMap, messageIndex, fromUserId, messages, msgType, message,
                unionId, data);

        return lastSeq;
    }


    /**
     * 剪藏消息内容
     *
     * @param userTagsMap  消息索引到标签的映射
     * @param messageIndex 消息在列表中的索引
     * @param fromUserId   发送者ID
     * @param messages     消息列表
     * @param msgType      消息类型
     * @param message      消息内容
     * @param unionId      用户unionId
     * @param data         企业微信聊天数据
     */
    private void clipMessageContent(Map<Integer, UserTagDto> userTagsMap,
                                    int messageIndex,
                                    String fromUserId,
                                    List<JSONObject> messages,
                                    String msgType,
                                    JSONObject message,
                                    String unionId,
                                    WxCpChatDatas data) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("processWxCpChatDatas.doProcessMsg");
        try {
            processMessageWithTags(userTagsMap, messageIndex, fromUserId, messages, msgType, message, unionId, data);
        } finally {
            stopWatch.stop();
            log.info("[{}]. rt = {}", stopWatch.lastTaskInfo().getTaskName(), stopWatch.getTotalTimeSeconds());
        }
    }

    /**
     * 从企业微信会话数据中提取符合剪藏条件的消息
     *
     * @param data         企业微信会话数据
     * @param msgId2SeqMap 消息ID到序列号的映射，用于后续消息处理和追踪
     * @return 符合剪藏条件的消息列表，以JSONObject形式返回
     */
    private List<JSONObject> extractClippableMessages(WxCpChatDatas data,
                                                      Map<String, Long> msgId2SeqMap) {
        WxCpMsgAuditService msgAuditService = getMsgAuditService();
        return data.getChatData().stream()
                .map(e -> {
                    try {
                        // 记录消息ID与序列号的映射关系到msgId2SeqMap
                        msgId2SeqMap.put(e.getMsgId(), e.getSeq());
                        // 获取解密的聊天数据明文
                        return msgAuditService.getChatPlainText(data.getSdk(), e, 1);
                    } catch (Exception ex) {
                        log.error("获取消息原文出现异常", ex);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .map(JSONObject::parseObject)
                .filter(e -> e.getString(ROOM_ID).isEmpty() // 非群聊消息(ROOM_ID为空)
                        && e.getString(ACTION).equals(ACTION_VALUE_SEND) // 消息动作为发送(ACTION_VALUE_SEND)
                        && e.getJSONArray(TO_LIST).contains(NOTION_MP_CLIPPER) // 接收者列表中包含Notion剪藏助手(NOTION_MP_CLIPPER)
                ).collect(Collectors.toList());
    }

    /**
     * 计算并获取与当前消息相关联的标签
     * <p>
     * 该方法通过分析消息上下文来确定当前消息应该关联的标签。主要逻辑是：
     * 1. 查找同一用户在当前消息之后的最近标签消息（firstTail）
     * 2. 查找同一用户在当前消息之前的最近标签消息（firstHead）
     * 3. 优先使用之后的标签，如果之后没有合适的标签，则尝试使用之前的标签
     * 4. 只有当当前消息与标签消息之间没有同一用户的其他消息时，才会关联这个标签
     *
     * @param userTagsMap 消息索引到用户标签的映射，键为消息在列表中的索引，值为包含用户ID和标签的对象
     * @param i           当前处理的消息在消息列表中的索引
     * @param fromUserId  当前消息的发送者用户ID
     * @param messages    完整的消息列表
     * @return 计算后的标签列表，如果没有找到合适的标签，则返回输入的tags（可能为空）
     */
    private static List<String> calculateTags(Map<Integer, UserTagDto> userTagsMap, int i, String fromUserId,
                                              List<JSONObject> messages) {
        List<String> tags = Lists.newArrayList();
        if (userTagsMap.isEmpty()) {
            return tags;
        }
        try {
            final int currentIndex = i;

            // 查找当前用户在当前消息之后的最近一条标签消息的索引
            // 过滤条件：1.标签对象不为空 2.标签所属用户ID与当前消息发送者相同 3.标签消息索引大于当前消息索引
            // 然后取最小的索引值（即最接近当前消息的后续标签）
            Optional<Integer> firstTail = userTagsMap.entrySet().stream()
                    .filter(e -> e.getValue() != null && e.getValue().getUserId().equals(fromUserId))
                    .filter(e -> e.getKey() > currentIndex)
                    .map(Map.Entry::getKey)
                    .min(Comparator.naturalOrder());

            // 查找当前用户在当前消息之前的最近一条标签消息的索引
            // 过滤条件：1.标签对象不为空 2.标签所属用户ID与当前消息发送者相同 3.标签消息索引小于当前消息索引
            // 然后取最大的索引值（即最接近当前消息的前置标签）
            Optional<Integer> firstHead = userTagsMap.entrySet().stream()
                    .filter(e -> e.getValue() != null && e.getValue().getUserId().equals(fromUserId))
                    .filter(e -> e.getKey() < currentIndex)
                    .map(Map.Entry::getKey)
                    .max(Comparator.naturalOrder());

            if (firstTail.isPresent()) {
                // 从后面找到了标签数据，判断中间是否存在当前用户的其他消息
                // 如果当前消息和后续标签消息之间存在同一用户的其他消息，则不使用该标签
                Optional<JSONObject> currentUserMessage = messages.subList(currentIndex + 1, firstTail.get()).stream()
                        .filter(e -> e.getString("from").equals(fromUserId))
                        .findAny();

                // 只有当中间没有同一用户的其他消息时，才使用这个标签
                if (currentUserMessage.isEmpty()) {
                    tags = userTagsMap.get(firstTail.get()).getTags();
                }
            }

            // 如果没有找到合适的后续标签，则尝试使用前置标签
            if (CollectionUtils.isEmpty(tags) && firstHead.isPresent()) {
                // 检查当前消息和前置标签消息之间是否存在同一用户的其他消息
                Optional<JSONObject> currentUserMessage = messages.subList(firstHead.get() + 1, currentIndex).stream()
                        .filter(e -> e.getString("from").equals(fromUserId))
                        .findAny();

                // 只有当中间没有同一用户的其他消息时，才使用这个标签
                if (currentUserMessage.isEmpty()) {
                    tags = userTagsMap.get(firstHead.get()).getTags();
                }
            }

            // 如果成功找到了标签，记录日志
            if (!CollectionUtils.isEmpty(tags)) {
                log.info("当前消息{}存在标签数据，解析结果：tags={}", currentIndex, tags);
            }
        } catch (Exception ex) {
            log.error("企微助手解析标签失败", ex);
        }

        return tags;
    }

    /**
     * 处理带标签的消息
     *
     * @param userTagsMap 消息索引到标签的映射
     * @param i           消息索引
     * @param fromUserId  发送者ID
     * @param messages    消息列表
     * @param msgType     消息类型
     * @param message     消息内容
     * @param unionId     用户unionId
     * @param data        企业微信聊天数据
     */
    private void processMessageWithTags(Map<Integer, UserTagDto> userTagsMap,
                                        int i,
                                        String fromUserId,
                                        List<JSONObject> messages,
                                        String msgType,
                                        JSONObject message,
                                        String unionId,
                                        WxCpChatDatas data) {
        try {
            // 剔除标签消息
            if (userTagsMap.containsKey(i)) {
                // 说明当前是作为标签处理的
                log.warn("#开头的消息被忽略,content={}", userTagsMap.get(i));
                return;
            }

            // 查找匹配标签
            List<String> tags = calculateTags(userTagsMap, i, fromUserId, messages);

            // 处理消息, 这里会抛异常，不支持的消息类型
            WxCpMsgAuditService msgAuditService = getMsgAuditService();
            Processor processor = processorFactory.createProcessor(msgType, msgAuditService);

            // 内部 sendMessageToNotion 会抛异常
            processor.process(message, data.getSdk(), unionId, tags);
        } catch (Exception e) {
            log.error("[EXCEPTION] doProcessMsg, msgType={}, message={}", msgType, message, e);
            logMessageProcessingError(fromUserId, msgType, message, unionId, e);
        }
    }

    /**
     * 记录微信消息处理错误
     *
     * @param fromUserId 发送者ID
     * @param msgType    消息类型
     * @param message    消息内容
     * @param unionId    用户unionId
     * @param e          异常信息
     */
    private void logMessageProcessingError(String fromUserId, String msgType, JSONObject message,
                                           String unionId,
                                           Exception e) {
        try {
            // 记录用户消息异常次数，给用户发送消息
            WxMessageProcessError wxMessageProcessError = new WxMessageProcessError();
            wxMessageProcessError.setMsgId(message.getString(MSG_ID));
            wxMessageProcessError.setMsgType(msgType);
            wxMessageProcessError.setFromUserId(fromUserId);
            wxMessageProcessError.setUnionId(unionId);
            wxMessageProcessError.setErrorMsg(e.getMessage());
            wxMessageProcessError.setCreateTime(LocalDateTime.now());
            wxMessageProcessError.setUpdateTime(LocalDateTime.now());
            wxMessageProcessErrorService.save(wxMessageProcessError);
        } catch (Exception ex) {
            log.error("记录用户消息异常记录失败", ex);
        }
    }

    /**
     * 处理账号绑定
     *
     * @param json       消息JSON
     * @param msgtype    消息类型
     * @param fromUserId 发送者ID
     */
    private void handleAccountBinding(JSONObject json, String msgtype, String fromUserId) {
        log.info("processBindCase 收到绑定消息, fromUserId={}, json={}", fromUserId, json);
        String unionId;
        if (CpMsgType.TEXT.equals(msgtype)) {
            String content = json.getJSONObject("text").getString("content");
            if (content.startsWith(NotionConstants.WX_USER_SECRET_PREFIX)) {
                String secret = content.substring(NotionConstants.WX_USER_SECRET_PREFIX_LENGTH);
                unionId = redissonClient.<String>getBucket(RedisKeys.getWechatBindSecretKey(secret)).get();
                if (StringUtils.isNoneBlank(unionId)) {
                    WxUserCp wxUserCp = new WxUserCp();
                    wxUserCp.setWxUserId(fromUserId);
                    wxUserCp.setUnionId(unionId);
                    wxUserCp.setCreateTime(LocalDateTime.now());
                    wxUserCpService.save(wxUserCp);
                }
            } else {
                log.error("收到无效的绑定消息, content={}", content);
            }
        }
    }

}
