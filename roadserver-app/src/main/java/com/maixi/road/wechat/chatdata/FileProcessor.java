package com.maixi.road.wechat.chatdata;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;
import com.maixi.road.wechat.constants.CpMsgConstants;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMsgAuditService;

@Slf4j
public class FileProcessor extends BaseProcessor {

    private final NotionClient notionClient;
    private final S3Manager s3Manager;
    private final WxCpMsgAuditService msgAuditService;
    private final IChatOpenListService chatOpenListService;
    private final MarkdownOutputService markdownOutputService;
    private final ConfigQueryApi configQueryApi;

    public FileProcessor(NotionClient notionClient, S3Manager s3Manager, WxCpMsgAuditService msgAuditService,
            IChatOpenListService chatOpenListService, MarkdownOutputService markdownOutputService,
            ConfigQueryApi configQueryApi) {
        this.notionClient = notionClient;
        this.s3Manager = s3Manager;
        this.msgAuditService = msgAuditService;
        this.chatOpenListService = chatOpenListService;
        this.markdownOutputService = markdownOutputService;
        this.configQueryApi = configQueryApi;
    }

    @Override
    protected NotionClient getNotionClient() {
        return notionClient;
    }


    @Override
    protected WxCpMsgAuditService getMsgAuditService() {
        return msgAuditService;
    }

    @Override
    protected S3Manager getS3Manager() {
        return s3Manager;
    }

    @Override
    protected IChatOpenListService getChatOpenListService() {
        return chatOpenListService;
    }
    
    @Override
    protected MarkdownOutputService getMarkdownOutputService() {
        return markdownOutputService;
    }
    
    @Override
    protected ConfigQueryApi getConfigQueryApi() {
        return configQueryApi;
    }

    /**
     * 处理文件类型的消息并同步到Notion
     * 
     * 该方法接收从微信获取的文件消息，验证用户权限和消息时效性，
     * 然后构建相应的Notion Block并发送到Notion平台。
     * 
     * @param message       包含文件信息的JSON消息对象
     * @param sdk           企业微信SDK实例ID
     * @param messageConfig 消息配置信息，包含用户ID和Notion连接参数
     * @param tagList       消息标签列表
     * @throws WxErrorException 微信API调用异常
     * @throws IOException      IO异常
     */
    @Override
    public void process(JSONObject message, Long sdk, String unionId, List<String> tagList) throws WxErrorException, IOException {
        // 1. 验证用户权限
        if (!isAuthorizedUser(unionId)) {
            return;
        }

        // 2. 检查消息时效性
        boolean isValidTime = isMessageValid(message);

        // 3. 构建Block
        Block block;
        if (isValidTime) {
            block = buildFileBlock(message, unionId, sdk, false);
        } else {
            RichTexts richTexts = RichTexts.builder()
                    .rich_text(Collections.singletonList(RichText.simpleText("文件消息已过期，无法同步")))
                    .build();
            block = Block.paragraph(richTexts);
        }

        // 获取文件名称
        String fileName = message.getJSONObject("file").getString(CpMsgConstants.FILE_NAME);

        // 将构建好的Block根据用户配置发送到对应平台
        super.output(unionId, fileName, MsgTypeEnum.FILE, Collections.singletonList(block),
                tagList.toArray(new String[0]));
    }

}
