package com.maixi.road.wechat.web;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.maixi.road.admin.biz.domain.ChatOpenList;
import com.maixi.road.admin.biz.domain.Member;
import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.domain.WxUserCp;
import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.biz.service.IMemberService;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.admin.biz.service.IWxUserCpService;
import com.maixi.road.admin.web.BaseController;
import com.maixi.road.common.business.user.enums.VipTypeEnum;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.service.redis.constant.RedisKeys;
import com.maixi.road.wechat.web.request.EnhanceSwitch;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.List;
import java.util.Objects;


@RestController
@RequestMapping("/miniprogram")
public class EnhanceCopilotController extends BaseController {

    @Resource
    private IChatOpenListService chatOpenListService;
    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private IMemberService memberService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IWxUserCpService wxUserCpService;


    @GetMapping("/getEnhanceSwitch")
    public Result<Boolean> getEnhanceSwitch() {
        List<ChatOpenList> list = chatOpenListService.list();
        return Result.success(list.stream().anyMatch(e -> e.getUnionId().equals(getLoginUnionId())));
    }


    @PostMapping("enhanceSwitch")
    public Result<Boolean> enhanceSwitch(@RequestParam("enhanceSwitch") boolean enhanceSwitch) {
        return Result.success(chatOpenListService.enhanceSwitch(getLoginUnionId(), enhanceSwitch));
    }

    @PostMapping("enhanceSwitchV2")
    public Result<Boolean> enhanceSwitchV2(@RequestBody EnhanceSwitch enhanceSwitch) {
        return Result.success(chatOpenListService.enhanceSwitch(getLoginUnionId(), enhanceSwitch.getEnable()));
    }

    /**
     * 获取企业微信剪藏助手的绑定口令
     *
     * @param request request
     * @return secret
     */

    @GetMapping("getBindCpWxUserSecret")
    public Result<String> getBindCpWxUserSecret(HttpServletRequest request) {
        String unionId = getLoginUnionId();
        UserInfo mainUser = userInfoService.getUserInfoBySubUnionId(unionId);
        if (mainUser != null) {
            // 以下逻辑是为永久会员子账号定制的
            Member mainUserMember = memberService.selectByUnionId(mainUser.getUnionId());
            if (mainUserMember == null) {
                throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "请登录主账号进行操作");
            } else {
                VipTypeEnum vipType = VipTypeEnum.getByType(mainUserMember.getVipType());
                if (!VipTypeEnum.FOREVER_VIP.equals(vipType)) {
                    throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "请登录主账号进行操作");
                }
            }
        }
        String secret = redissonClient.<String>getBucket(RedisKeys.getWechatBindUnionIdKey(unionId)).get();
        if (StringUtils.isNoneBlank(secret)) {
            return Result.success(NotionConstants.WX_USER_SECRET_PREFIX + secret);
        }
        secret = NanoIdUtils.randomNanoId();
        redissonClient.getBucket(RedisKeys.getWechatBindUnionIdKey(unionId)).set(secret, Duration.ofDays(1));
        redissonClient.getBucket(RedisKeys.getWechatBindSecretKey(secret)).set(unionId, Duration.ofDays(1));
        return Result.success(NotionConstants.WX_USER_SECRET_PREFIX + secret);
    }


    /**
     * 是否以绑定企业微信剪藏助手
     *
     * @param request req
     * @return true or false
     */

    @GetMapping("getHasBindCpWxUser")
    public Result<Boolean> getHasBindCpWxUser(HttpServletRequest request) {
        String unionId = getLoginUnionId();
        UserInfo mainUser = userInfoService.getUserInfoBySubUnionId(unionId);
        if (mainUser != null) {
            Member mainUserMember = memberService.selectByUnionId(mainUser.getUnionId());
            if (mainUserMember == null) {
                throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "请登录主账号进行操作");
            } else {
                VipTypeEnum vipType = VipTypeEnum.getByType(mainUserMember.getVipType());
                if (!VipTypeEnum.FOREVER_VIP.equals(vipType)) {
                    throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "请登录主账号进行操作");
                }
            }
        }
        WxUserCp wxUser = wxUserCpService.getWxUserByUnionId(unionId);
        if (Objects.nonNull(wxUser)) {
            return Result.success(true);
        } else {
            return Result.success(false);
        }
    }
}
