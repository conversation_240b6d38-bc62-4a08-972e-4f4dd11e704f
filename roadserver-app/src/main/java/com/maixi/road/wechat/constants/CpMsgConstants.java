package com.maixi.road.wechat.constants;

public class CpMsgConstants {

    public static final Integer AGENT_ID = 2024330;

    public static final String TO_LIST = "tolist";
    public static final String NOTION_MP_CLIPPER = "notionmpclipper<PERSON>aiFaZhe";
    public static final String ACTION = "action";
    public static final String ACTION_VALUE_SEND = "send";

    public static final String ROOM_ID = "roomid";
    public static final String FROM = "from";
    public static final String MSG_ID = "msgid";
    public static final String MSG_TYPE = "msgtype";
    public static final String MSG_TIME = "msgtime";

    public static final String CHAT_RECORD = "chatrecord";
    public static final String SDK_FILE_ID = "sdkfileid";
    public static final String FILE_EXT = "fileext";
    public static final String FILE_NAME = "filename";
    public static final String FILE_SIZE = "filesize";
    public static final String PLAY_LENGTH = "play_length";
    public static final String VOICE_SIZE = "voice_size";

    public static final String CHAT_RECORD_TEXT = "ChatRecordText";
    public static final String CHAT_RECORD_LINK = "ChatRecordLink";
    public static final String CHAT_RECORD_IMAGE = "ChatRecordImage";
    public static final String CHAT_RECORD_VIDEO = "ChatRecordVideo";
    public static final String CHAT_RECORD_FILE = "ChatRecordFile";


    public static final String LAST_MSG_SEQ = "lastMsgSeq";

    public static final String VIDEO_HOME_DIR = "/home/<USER>/video";
    public static final String VOICE_HOME_DIR = "/home/<USER>/voice";
    public static final String IMAGE_HOME_DIR = "/home/<USER>/image";
    public static final String FILE_HOME_DIR = "/home/<USER>/file";

    public static final long MAX_SIZE_10M = 10L * 1024 * 1024;


}
