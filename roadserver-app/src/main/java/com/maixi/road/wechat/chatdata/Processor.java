package com.maixi.road.wechat.chatdata;

import java.io.IOException;
import java.util.List;

import com.alibaba.fastjson.JSONObject;

import me.chanjar.weixin.common.error.WxErrorException;

public interface Processor {

    /**
     * 处理消息数据
     * 
     * @param message 消息内容JSON对象
     * @param sdk 企业微信SDK实例ID
     * @param unionId 用户unionId
     * @param tagList 消息标签列表
     * @throws WxErrorException 微信API调用异常
     * @throws IOException IO异常
     */
    void process(JSONObject message, Long sdk, String unionId, List<String> tagList) throws WxErrorException, IOException;
}
