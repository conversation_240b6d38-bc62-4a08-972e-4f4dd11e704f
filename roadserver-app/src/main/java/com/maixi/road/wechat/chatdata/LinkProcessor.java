package com.maixi.road.wechat.chatdata;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.clipper.ClipperApi;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpMsgAuditService;

import java.util.List;

@Slf4j
public class LinkProcessor extends BaseProcessor {

    private final NotionClient notionClient;
    private final S3Manager s3Manager;
    private final ClipperApi clipperApi;
    private final MarkdownOutputService markdownOutputService;
    private final ConfigQueryApi configQueryApi;

    public LinkProcessor(NotionClient notionClient, S3Manager s3Manager, <PERSON><PERSON><PERSON><PERSON><PERSON> clipperApi,
                         MarkdownOutputService markdownOutputService, ConfigQueryApi configQueryApi) {
        this.notionClient = notionClient;
        this.s3Manager = s3Manager;
        this.clipperApi = clipperApi;
        this.markdownOutputService = markdownOutputService;
        this.configQueryApi = configQueryApi;
    }

    @Override
    protected NotionClient getNotionClient() {
        return notionClient;
    }


    @Override
    protected WxCpMsgAuditService getMsgAuditService() {
        return null;
    }

    @Override
    protected S3Manager getS3Manager() {
        return s3Manager;
    }

    @Override
    protected IChatOpenListService getChatOpenListService() {
        return null;
    }

    @Override
    protected MarkdownOutputService getMarkdownOutputService() {
        return markdownOutputService;
    }

    @Override
    protected ConfigQueryApi getConfigQueryApi() {
        return configQueryApi;
    }

    /**
     * 处理链接类型的消息并同步到Notion
     * <p>
     * 该方法接收从微信获取的链接消息，然后调用剪藏API解析链接并保存。
     *
     * @param message 包含链接信息的JSON消息对象
     * @param sdk     企业微信SDK实例ID
     * @param unionId 用户唯一标识
     * @param tagList 消息标签列表
     */
    @Override
    public void process(JSONObject message, Long sdk, String unionId, List<String> tagList) {
        // 从消息中提取链接URL
        String linkUrl = message.getJSONObject("link").getString("link_url");
        log.info("process link message, message={}", message.toJSONString());
        // 调用剪藏API解析链接并同步到Notion
        clipperApi.parseUrlThenSave(unionId, tagList.toArray(new String[0]), linkUrl);
    }
}
