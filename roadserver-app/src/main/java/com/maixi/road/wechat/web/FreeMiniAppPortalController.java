package com.maixi.road.wechat.web;

import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.alibaba.fastjson.JSON;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.wechat.manager.MiniAppLoginManager;
import com.maixi.road.wechat.web.request.WxUserInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("miniprogram/freehelper")
public class FreeMiniAppPortalController {

    private static final String FREE_HELPER_APP_ID = "wxe23374330660c146";
    @Resource
    private MiniAppLoginManager miniAppLoginManager;

    /**
     * 登陆接口
     * return WxMaJscode2SessionResult
     */
    @GetMapping("/login")
    public Result<String> login(@RequestParam("token") String token) {
        return miniAppLoginManager.login(token, FREE_HELPER_APP_ID);
    }

    /**
     * <pre>
     * 获取用户信息接口
     * </pre>
     * return WxMaUserInfo
     *
     */
    @Deprecated
    @PostMapping("/getUserInfo")
    public Result<WxMaUserInfo> getUserInfo(@RequestBody WxUserInfo userInfo) {
        log.error("[理应下线接口] MiniAppApi_post_userInfo={}", JSON.toJSONString(userInfo));
        return miniAppLoginManager.getUserInfo(userInfo, FREE_HELPER_APP_ID);
    }
}
