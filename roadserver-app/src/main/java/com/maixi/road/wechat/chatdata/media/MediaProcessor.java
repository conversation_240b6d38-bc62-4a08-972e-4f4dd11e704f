package com.maixi.road.wechat.chatdata.media;

import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.common.integration.notion.model.block.Block;

/**
 * 媒体处理器接口
 * 
 * 定义了处理不同类型媒体文件的通用接口，实现策略模式
 * 每种媒体类型（图片、音频、视频、文件）都有对应的实现类
 */
public interface MediaProcessor {
    
    /**
     * 获取媒体对象的键名
     * 
     * @param fromChatRecord 是否来自聊天记录
     * @return 媒体对象的键名
     */
    String getMediaObjectKey(boolean fromChatRecord);
    
    /**
     * 获取媒体文件的扩展名
     * 
     * @return 文件扩展名（不含点号）
     */
    String getFileExtension();
    
    /**
     * 获取媒体文件的临时存储目录
     * 
     * @return 临时存储目录路径
     */
    String getHomeDir();
    
    /**
     * 获取媒体文件大小的键名
     * 
     * @return 文件大小的键名
     */
    String getSizeKey();
    
    /**
     * 获取媒体文件ID的键名
     * 
     * @return 文件ID的键名
     */
    String getIdKey();
    
    /**
     * 获取媒体类型的显示名称
     * 
     * @return 媒体类型名称
     */
    String getTypeName();
    
    /**
     * 获取媒体文件大小限制（字节）
     * 
     * @return 文件大小限制
     */
    long getMaxFileSize();
    
    /**
     * 创建对应类型的Notion Block
     * 
     * @param url 媒体文件URL
     * @param fileName 文件名
     * @return Notion Block对象
     */
    Block createBlock(String url, String fileName);
    
    /**
     * 上传媒体文件到存储服务
     * 
     * @param s3Manager S3管理器
     * @param unionId 用户ID
     * @param filePath 本地文件路径
     * @param fileName 文件名
     * @return 上传后的URL
     * @throws Exception 上传异常
     */
    String uploadFile(S3Manager s3Manager, String unionId, String filePath, String fileName) throws Exception;
    
    /**
     * 创建错误提示Block
     * 
     * @param reason 错误原因
     * @return 错误提示Block
     */
    Block createErrorBlock(String reason);
}
