package com.maixi.road.wechat.chatdata;

import org.springframework.stereotype.Service;

import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.clipper.ClipperApi;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;
import com.maixi.road.wechat.constants.CpMsgType;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpMsgAuditService;

@Slf4j
@Service
public class ProcessorFactory {

    @Resource
    private NotionClient notionClient;
    @Resource
    private S3Manager s3Manager;
    @Resource
    private IChatOpenListService chatOpenListService;
    @Resource
    private ClipperApi clipperApi;
    @Resource
    private MarkdownOutputService markdownOutputService;
    @Resource
    private ConfigQueryApi configQueryApi;

    public Processor createProcessor(String msgType, WxCpMsgAuditService msgAuditService) {
        return switch (msgType) {
            case CpMsgType.TEXT ->
                new TextProcessor(notionClient, s3Manager, msgAuditService, clipperApi, markdownOutputService, configQueryApi);
            case CpMsgType.IMAGE ->
                new ImageProcessor(notionClient, s3Manager, msgAuditService, markdownOutputService, configQueryApi);
            case CpMsgType.VOICE ->
                new VoiceProcessor(notionClient, s3Manager, msgAuditService, chatOpenListService, markdownOutputService, configQueryApi);
            case CpMsgType.VIDEO ->
                new VideoProcessor(notionClient, s3Manager, msgAuditService, chatOpenListService, markdownOutputService, configQueryApi);
            case CpMsgType.FILE ->
                new FileProcessor(notionClient, s3Manager, msgAuditService, chatOpenListService, markdownOutputService, configQueryApi);
            case CpMsgType.LINK ->
                new LinkProcessor(notionClient, s3Manager, clipperApi, markdownOutputService, configQueryApi);
            case CpMsgType.CHAT_RECORD ->
                new ChatRecordProcessor(notionClient, s3Manager, msgAuditService, chatOpenListService, markdownOutputService, configQueryApi);
            default -> throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "暂不支持的消息类型");
        };
    }

}
