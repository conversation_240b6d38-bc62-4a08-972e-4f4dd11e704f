package com.maixi.road.wechat.chatdata;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;

import org.jetbrains.annotations.NotNull;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.maixi.road.admin.biz.domain.ChatOpenList;
import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.core.utils.DateUtils;
import com.maixi.road.common.core.utils.NameUtils;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.integration.notion.model.block.LinkToPage;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;
import com.maixi.road.wechat.constants.CpMsgConstants;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMsgAuditService;

@Slf4j
public class ChatRecordProcessor extends BaseProcessor {

    private final S3Manager s3Manager;
    private final WxCpMsgAuditService msgAuditService;
    private final IChatOpenListService chatOpenListService;
    private final NotionClient notionClient;
    private final MarkdownOutputService markdownOutputService;
    private final ConfigQueryApi configQueryApi;

    // executor 已移至 BaseProcessor 类中

    public ChatRecordProcessor(NotionClient notionClient, S3Manager s3Manager,
            WxCpMsgAuditService msgAuditService, IChatOpenListService chatOpenListService,
            MarkdownOutputService markdownOutputService, ConfigQueryApi configQueryApi) {
        this.notionClient = notionClient;
        this.s3Manager = s3Manager;
        this.msgAuditService = msgAuditService;
        this.chatOpenListService = chatOpenListService;
        this.markdownOutputService = markdownOutputService;
        this.configQueryApi = configQueryApi;
    }

    private static @NotNull Block buildChatTimeBlock(JSONObject json) {
        Long time = json.getLong(CpMsgConstants.MSG_TIME);
        List<RichText> timestamp = Lists.newArrayList(RichText.grayItalicText(DateUtils.formatTimeStamp(time)));
        return Block.paragraph(RichTexts.builder().rich_text(timestamp).build());
    }

    /**
     * 生成页面标题，使用统一的NameUtils工具类
     *
     * @param chatrecord 聊天记录JSON对象
     * @param msgTime    消息时间戳（毫秒）
     * @return 清理后的安全文件名
     */
    private static @NotNull String generatePageTitle(JSONObject chatrecord, Long msgTime) {
        String title = chatrecord.getString("title");
        return NameUtils.generateSafeFileName(title, msgTime);
    }

    @Override
    protected NotionClient getNotionClient() {
        return notionClient;
    }

    @Override
    protected WxCpMsgAuditService getMsgAuditService() {
        return msgAuditService;
    }

    @Override
    protected S3Manager getS3Manager() {
        return s3Manager;
    }

    @Override
    protected IChatOpenListService getChatOpenListService() {
        return chatOpenListService;
    }
    
    @Override
    protected MarkdownOutputService getMarkdownOutputService() {
        return markdownOutputService;
    }
    
    @Override
    protected ConfigQueryApi getConfigQueryApi() {
        return configQueryApi;
    }

    @Override
    public void process(JSONObject message, Long sdk, String unionId, List<String> tagList)
            throws WxErrorException, IOException {
        List<String> unionIdList = chatOpenListService.list().stream().map(ChatOpenList::getUnionId).toList();
        if (!unionIdList.contains(unionId)) {
            return;
        }
        log.info("ChatRecordProcessor process message={}", message);
        processChatRecordMsgCase(message, sdk, unionId, tagList, false);
    }

    private PageCreateRS processChatRecordMsgCase(JSONObject message, Long sdk, String unionId,
                    List<String> tagList, boolean fromChatRecord) throws WxErrorException {
        Long msgTime = message.getLong(CpMsgConstants.MSG_TIME);
        JSONObject chatRecord;
        if (fromChatRecord) {
            chatRecord = message.getJSONObject("content");
            msgTime = msgTime * 1000;
        } else {
            chatRecord = message.getJSONObject(CpMsgConstants.CHAT_RECORD);
        }
        log.info("processChatRecordMsgCase,chatRecord={}", chatRecord);

        String pageTitle = generatePageTitle(chatRecord, msgTime);
        String[] tagArr = tagList.toArray(new String[0]);
        log.info("当前聊天记录存在标签数据，tags={}", JSON.toJSONString(tagArr));

        JSONArray items = chatRecord.getJSONArray("item");
        List<Block> blocks = Lists.newArrayList();
        if (items.isEmpty() || items.size() > 2000) {
            log.error("转发消息内容过大，无法同步, item.size={}", items.size());
            blocks.add(Block.paragraph(RichTexts.builder()
                    .rich_text(Collections.singletonList(RichText.simpleRedText("转发消息内容过大，无法同步"))).build()));
            return super.output(unionId, pageTitle, MsgTypeEnum.CHAT_RECORD, blocks, null);
        }

        for (Object item : items) {
            JSONObject json = (JSONObject) item;
            log.info("processChatRecordMsgCase,item={}", json);

            Block chatTimeBlock = buildChatTimeBlock(json);
            blocks.add(chatTimeBlock);

            Long time = json.getLong(CpMsgConstants.MSG_TIME);
            LocalDateTime localDateTime = LocalDateTime.ofEpochSecond(time, 0, ZoneOffset.of("+8"));
            boolean isValidTime = !LocalDateTime.now().minusDays(5).isAfter(localDateTime);
            String type = json.getString("type");
            switch (type) {
                case CpMsgConstants.CHAT_RECORD_TEXT:
                    String text = json.getJSONObject("content").getString("content");
                    blocks.add(Block.paragraph(RichTexts.builder()
                            .rich_text(Collections.singletonList(RichText.simpleText(text))).build()));
                    break;
                case CpMsgConstants.CHAT_RECORD_LINK:
                    String linkTitle = json.getJSONObject("content").getString("title");
                    String linkUrl = json.getJSONObject("content").getString("link_url");
                    blocks.add(Block.paragraph(
                            RichTexts.build(Collections.singletonList(RichText.textWithBlueLink(linkTitle, linkUrl)))));
                    break;
                case CpMsgConstants.CHAT_RECORD_IMAGE:
                    if (isValidTime) {
                        blocks.add(buildImageBlock(json, unionId, sdk, true));
                    } else {
                        blocks.add(Block.paragraph(RichTexts.builder()
                                .rich_text(Collections.singletonList(RichText.simpleRedText("图片消息已过期，无法同步"))).build()));
                    }
                    break;
                case CpMsgConstants.CHAT_RECORD_VIDEO:
                    if (isValidTime) {
                        blocks.add(buildVideoBlock(json, unionId, sdk, true));
                    } else {
                        blocks.add(Block.paragraph(RichTexts.builder()
                                .rich_text(Collections.singletonList(RichText.simpleRedText("视频消息已过期，无法同步"))).build()));
                    }
                    break;
                case CpMsgConstants.CHAT_RECORD_FILE:
                    if (isValidTime) {
                        blocks.add(buildFileBlock(json, unionId, sdk, true));
                    } else {
                        blocks.add(Block.paragraph(RichTexts.builder()
                                .rich_text(Collections.singletonList(RichText.simpleRedText("文件消息已过期，无法同步"))).build()));
                    }
                    break;
                case CpMsgConstants.CHAT_RECORD:
                    try {
                        PageCreateRS result = processChatRecordMsgCase(json, sdk, unionId, tagList, true);
                        if (result == null) {
                            blocks.add(Block.callout(Callout.buildTip("此处疑似丢失一条聊天记录消息")));
                        } else {
                            log.info("processChatRecordMsgCase chatrecord PageCreateResult={}", JSON.toJSON(result));
                            blocks.add(Block.linkToPage(new LinkToPage(result.getId())));
                        }
                    } catch (Exception e) {
                        log.error("processChatRecordMsgCase chatrecord error", e);
                        blocks.add(Block.callout(Callout.buildTip("此处疑似丢失一条聊天记录消息")));
                    }
                    break;
                default:
                    log.error("暂不支持的消息类型,type={}, message={}", type, json);
                    blocks.add(Block.callout(Callout.buildTip("此处丢失一条暂不支持的类型的消息")));
                    break;
            }
        }
        log.info("processChatRecordMsgCase blocks={},size={}",
                JSON.toJSONString(blocks, SerializerFeature.DisableCircularReferenceDetect), blocks.size());
        // blocks 的截断逻辑已移至 BaseProcessor.sendMessageToNotion 中处理
        return super.output(unionId, pageTitle, MsgTypeEnum.CHAT_RECORD, blocks, tagArr);
    }

    // handleRetainBlocks 和 appendBlocks 方法已移至 BaseProcessor 类中
}
