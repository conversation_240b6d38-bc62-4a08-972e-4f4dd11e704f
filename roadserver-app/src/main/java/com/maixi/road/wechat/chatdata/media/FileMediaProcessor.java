package com.maixi.road.wechat.chatdata.media;

import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.File;
import com.maixi.road.wechat.constants.CpMsgConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件媒体处理器
 * 
 * 处理文件类型的媒体，实现MediaProcessor接口
 */
@Slf4j
public class FileMediaProcessor extends AbstractMediaProcessor {

    @Override
    public String getMediaObjectKey(boolean fromChatRecord) {
        return fromChatRecord ? "content" : "file";
    }

    @Override
    public String getFileExtension() {
        // 文件扩展名由消息内容决定，这里返回null
        return null;
    }

    @Override
    public String getHomeDir() {
        return CpMsgConstants.FILE_HOME_DIR;
    }

    @Override
    public String getSizeKey() {
        return CpMsgConstants.FILE_SIZE;
    }

    @Override
    public String getTypeName() {
        return "文件";
    }

    @Override
    public Block createBlock(String url, String fileName) {
        return Block.file(new File(url, fileName));
    }

    @Override
    public String uploadFile(S3Manager s3Manager, String unionId, String filePath, String fileName) throws Exception {
        String fileUrl = s3Manager.uploadFile(unionId, filePath, fileName);
        log.info("uploadFile2Oss, fileUrl={}", fileUrl);
        return fileUrl;
    }
}
