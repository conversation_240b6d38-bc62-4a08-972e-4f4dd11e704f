package com.maixi.road.wechat.chatdata.media;

import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Image;
import com.maixi.road.wechat.constants.CpMsgConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 图片媒体处理器
 * 
 * 处理图片类型的媒体，实现MediaProcessor接口
 */
@Slf4j
public class ImageMediaProcessor extends AbstractMediaProcessor {

    @Override
    public String getMediaObjectKey(boolean fromChatRecord) {
        return fromChatRecord ? "content" : "image";
    }

    @Override
    public String getFileExtension() {
        return "jpg";
    }

    @Override
    public String getHomeDir() {
        return CpMsgConstants.IMAGE_HOME_DIR;
    }

    @Override
    public String getSizeKey() {
        // 图片通常不检查大小限制
        return null;
    }

    @Override
    public String getTypeName() {
        return "图片";
    }

    @Override
    public Block createBlock(String url, String fileName) {
        return Block.image(new Image(url));
    }

    @Override
    public String uploadFile(S3Manager s3Manager, String unionId, String filePath, String fileName) throws Exception {
        String imageUrl = s3Manager.uploadImgFile(unionId, filePath, fileName);
        log.info("uploadImage2Oss, imageUrl={}", imageUrl);
        return imageUrl;
    }
}
