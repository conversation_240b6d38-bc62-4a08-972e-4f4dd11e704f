package com.maixi.road.wechat.chatdata.media;

import java.util.HashMap;
import java.util.Map;

import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;

/**
 * 媒体处理器工厂类
 * 
 * 负责创建和缓存不同类型的媒体处理器实例
 */
public class MediaProcessorFactory {
    
    private static final Map<MsgTypeEnum, MediaProcessor> PROCESSORS = new HashMap<>();
    
    static {
        // 初始化各种媒体处理器
        PROCESSORS.put(MsgTypeEnum.FILE, new FileMediaProcessor());
        PROCESSORS.put(MsgTypeEnum.VOICE, new VoiceMediaProcessor());
        PROCESSORS.put(MsgTypeEnum.VIDEO, new VideoMediaProcessor());
        PROCESSORS.put(MsgTypeEnum.IMAGE, new ImageMediaProcessor());
    }
    
    /**
     * 获取指定媒体类型的处理器
     * 
     * @param mediaType 媒体类型
     * @return 对应的媒体处理器
     * @throws IllegalArgumentException 如果媒体类型不支持
     */
    public static MediaProcessor getProcessor(MsgTypeEnum mediaType) {
        MediaProcessor processor = PROCESSORS.get(mediaType);
        if (processor == null) {
            throw new IllegalArgumentException("不支持的媒体类型: " + mediaType);
        }
        return processor;
    }
}
