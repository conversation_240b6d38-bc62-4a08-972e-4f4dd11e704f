package com.maixi.road.wechat.chatdata.media;

import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Video;
import com.maixi.road.wechat.constants.CpMsgConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 视频媒体处理器
 * 
 * 处理视频类型的媒体，实现MediaProcessor接口
 */
@Slf4j
public class VideoMediaProcessor extends AbstractMediaProcessor {

    @Override
    public String getMediaObjectKey(boolean fromChatRecord) {
        return fromChatRecord ? "content" : "video";
    }

    @Override
    public String getFileExtension() {
        return "mp4";
    }

    @Override
    public String getHomeDir() {
        return CpMsgConstants.VIDEO_HOME_DIR;
    }

    @Override
    public String getSizeKey() {
        return CpMsgConstants.FILE_SIZE;
    }

    @Override
    public String getTypeName() {
        return "视频";
    }

    @Override
    public Block createBlock(String url, String fileName) {
        return Block.video(new Video(url));
    }

    @Override
    public String uploadFile(S3Manager s3Manager, String unionId, String filePath, String fileName) throws Exception {
        String videoUrl = s3Manager.uploadFile(unionId, filePath, fileName);
        log.info("uploadVideo2Oss, videoUrl={}", videoUrl);
        return videoUrl;
    }
}
