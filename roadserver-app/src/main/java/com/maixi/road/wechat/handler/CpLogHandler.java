package com.maixi.road.wechat.handler;

import com.maixi.road.wechat.manager.CpWxMessageManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Slf4j
@Component
public class CpLogHandler extends AbstractHandler {

    @Resource
    private CpWxMessageManager cpWxMessageManager;


    @Override
    public WxCpXmlOutMessage handle(WxCpXmlMessage wxMessage, Map<String, Object> context, WxCpService cpService,
                                    WxSessionManager sessionManager) {
        if ("2000004".equals(wxMessage.getAgentId())
                && "event".equals(wxMessage.getMsgType())
                && "sys".equals(wxMessage.getFromUserName())
                && "msgaudit_notify".equals(wxMessage.getEvent())) {

            cpWxMessageManager.syncWxMessages();
        }
        return null;
    }


}
