package com.maixi.road.wechat.chatdata;

import java.io.IOException;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.clipper.ClipperApi;
import com.maixi.road.common.core.model.dto.ClipperResponse;
import com.maixi.road.common.core.model.dto.TextMessageRequest;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMsgAuditService;

@Slf4j
public class TextProcessor extends BaseProcessor {

    private final ClipperApi clipperApi;
    private final S3Manager s3Manager;
    private final WxCpMsgAuditService msgAuditService;
    private final NotionClient notionClient;
    private final MarkdownOutputService markdownOutputService;
    private final ConfigQueryApi configQueryApi;

    public TextProcessor(NotionClient notionClient, S3Manager s3Manager,
            WxCpMsgAuditService msgAuditService, ClipperApi clipperApi, 
            MarkdownOutputService markdownOutputService, ConfigQueryApi configQueryApi) {
        this.clipperApi = clipperApi;
        this.s3Manager = s3Manager;
        this.msgAuditService = msgAuditService;
        this.notionClient = notionClient;
        this.markdownOutputService = markdownOutputService;
        this.configQueryApi = configQueryApi;
    }

    @Override
    protected NotionClient getNotionClient() {
        return notionClient;
    }

    @Override
    protected WxCpMsgAuditService getMsgAuditService() {
        return this.msgAuditService;
    }

    @Override
    protected S3Manager getS3Manager() {
        return this.s3Manager;
    }

    @Override
    protected IChatOpenListService getChatOpenListService() {
        return null;
    }
    
    @Override
    protected MarkdownOutputService getMarkdownOutputService() {
        return markdownOutputService;
    }
    
    @Override
    protected ConfigQueryApi getConfigQueryApi() {
        return configQueryApi;
    }

    /**
     * 处理文本类型的消息并同步到Notion
     * 
     * 该方法接收从微信获取的文本消息，提取内容并通过剪藏API
     * 将其同步到Notion平台。
     * 
     * @param message       包含文本信息的JSON消息对象
     * @param sdk           企业微信SDK实例ID
     * @param messageConfig 消息配置信息，包含用户ID和Notion连接参数
     * @param tagList       消息标签列表
     * @throws WxErrorException 微信API调用异常
     * @throws IOException      IO异常
     */
    @Override
    public void process(JSONObject message, Long sdk, String unionId, List<String> tagList)
            throws WxErrorException, IOException {
        // 从消息JSON对象中提取文本内容
        String content = message.getJSONObject("text").getString("content");
        // 检查内容是否为空，为空则直接返回
        if (StringUtils.isBlank(content)) {
            return;
        }
        // 创建文本消息请求对象
        TextMessageRequest textMessageRequest = new TextMessageRequest();
        // 设置消息内容
        textMessageRequest.setContent(content);
        // 设置消息标签
        textMessageRequest.setTags(tagList);
        // 通过剪藏API将消息同步到Notion
        ClipperResponse<String> clipperResponse = clipperApi.saveText(textMessageRequest, unionId);
        if (!clipperResponse.isSuccess()) {
            log.error("同步文本消息失败, userId={}, clipperResponse={}", unionId, JSON.toJSONString(clipperResponse));
        }
    }

}
