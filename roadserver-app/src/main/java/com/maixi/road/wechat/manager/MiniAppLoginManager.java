package com.maixi.road.wechat.manager;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.maixi.road.framework.config.RedisManager;
import com.maixi.road.admin.biz.domain.CustomConfig;
import com.maixi.road.admin.biz.domain.UserInfo;
import com.maixi.road.admin.biz.service.ICustomConfigService;
import com.maixi.road.admin.biz.service.IUserInfoService;
import com.maixi.road.admin.biz.service.IUserOpenIdService;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.utils.JwtUtil;
import com.maixi.road.wechat.web.request.WxUserInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Slf4j
@Component
public class MiniAppLoginManager {

    @Resource
    private WxMaService wxMaService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IUserInfoService userInfoService;
    @Resource
    private IUserOpenIdService userOpenIdService;
    @Resource
    private ICustomConfigService customConfigService;
    @Resource
    private RedisManager redisManager;

    private static final String DEFAULT_AVATAR = "https://notionimg.s3.bitiful.net/nmp_avatar.png";

    /**
     * 登录
     *
     * @param code code
     * @return WxMaJscode2SessionResult
     */
    public Result<String> login(String code, String appId) {
        try {
            if (!wxMaService.switchover(appId)) {
                throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appId));
            }
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);
            String token = createLoginToken(session.getUnionid());
            // 创建用户信息
            UserInfo userInfo = userInfoService.getUserInfoByUnionId(session.getUnionid());
            if (userInfo == null) {
                createNewUser(session,appId);
            } else if (StringUtils.isBlank(userInfo.getOpenId())) {
                userInfoService.updateOpenIdByUnionId(userInfo.getUnionId(), session.getOpenid());
            }

            // 保存或更新用户在当前小程序的openId
            userOpenIdService.saveOrUpdateOpenId(session.getUnionid(), appId, session.getOpenid());
            return Result.success(token);
        } catch (Exception e) {
            log.error("用户登录异常 code={}", code, e);
            return Result.fail(e.getMessage());
        } finally {
            WxMaConfigHolder.remove();//清理ThreadLocal
        }
    }

    private void createNewUser(WxMaJscode2SessionResult session,String appId) {
        UserInfo userInfo;
        userInfo = new UserInfo();
        userInfo.setUnionId(session.getUnionid());
        userInfo.setSubUserId("");
        userInfo.setOpenId(session.getOpenid());
        String introCode = NanoIdUtils.randomNanoId();
        userInfo.setNickname("用户_" + introCode.substring(6, 12));
        userInfo.setAvatar(DEFAULT_AVATAR);
        userInfo.setActive(1);
        userInfo.setGmtCreate(LocalDateTime.now());
        userInfo.setGmtUpdate(LocalDateTime.now());
        userInfo.setPromoteCode(introCode);
        userInfo.setNotionApiKey(null);
        userInfo.setWxArticleSum(0);
        userInfo.setWxMsgSum(0);
        userInfo.setVipNo(0);
        userInfoService.save(userInfo);
        // 初始化个性配置
        if ("wxfa42c9c1b1ea3807".equals(appId)) {
            CustomConfig customConfig = new CustomConfig();
            customConfig.setUnionId(session.getUnionid());
            customConfig.setAlwaysUseCloudinary(0);
            customConfig.setAlwaysUsePicCloud(0);
            customConfig.setNoCover(1);
            customConfig.setNoIcon(1);
            customConfig.setQuickClipAsEnterPage(0);
            customConfig.setSupportAllSite(0);
            customConfig.setToObsidian(1);
            customConfig.setGmtCreate(LocalDateTime.now());
            customConfig.setGmtUpdate(LocalDateTime.now());
            customConfig.setDeleted(0);
            customConfigService.save(customConfig);
            log.info("来自 ObClipper 的新用户,unionId={}，初始化个性配置", session.getUnionid());
        } else if ("wx34b2cfe3fdbc3b61".equals(appId)) {
            CustomConfig customConfig = new CustomConfig();
            customConfig.setUnionId(session.getUnionid());
            customConfig.setAlwaysUseCloudinary(0);
            customConfig.setAlwaysUsePicCloud(1);
            customConfig.setNoCover(0);
            customConfig.setNoIcon(0);
            customConfig.setQuickClipAsEnterPage(0);
            customConfig.setSupportAllSite(0);
            customConfig.setToObsidian(0);
            customConfig.setGmtCreate(LocalDateTime.now());
            customConfig.setGmtUpdate(LocalDateTime.now());
            customConfig.setDeleted(0);
            customConfigService.save(customConfig);
            log.info("来自 NotionMpClipper 的新用户,unionId={}，初始化个性配置", session.getUnionid());
        }else {
            CustomConfig customConfig = new CustomConfig();
            customConfig.setUnionId(session.getUnionid());
            customConfig.setAlwaysUseCloudinary(0);
            customConfig.setAlwaysUsePicCloud(1);
            customConfig.setNoCover(0);
            customConfig.setNoIcon(0);
            customConfig.setQuickClipAsEnterPage(0);
            customConfig.setSupportAllSite(0);
            customConfig.setToObsidian(0);
            customConfig.setGmtCreate(LocalDateTime.now());
            customConfig.setGmtUpdate(LocalDateTime.now());
            customConfig.setDeleted(0);
            customConfigService.save(customConfig);
            log.info("来自 FreeHelper 的新用户,unionId={}，初始化个性配置", session.getUnionid());
        }

    }

    private String createLoginToken(String unionId) {
        String token = JwtUtil.createToken(unionId);
        log.info("用户登录创建 login token, unionId={}, token={}", unionId, token);
        return token;
    }

    public Result<WxMaUserInfo> getUserInfo(WxUserInfo userInfo, String appId) {
        try {
            if (!wxMaService.switchover(appId)) {
                log.error("未找到对应appid=[{}]的配置，请核实！", appId);
                throw new IllegalArgumentException("系统配置异常");
            }
            // 用户信息校验
            if (!wxMaService.getUserService().checkUserInfo(userInfo.getSessionKey(), userInfo.getRawData(), userInfo.getSignature())) {
                WxMaConfigHolder.remove();//清理ThreadLocal
                return Result.fail("user check failed");
            }

            // 解密用户信息
            WxMaUserInfo wxMaUserInfo = wxMaService.getUserService().getUserInfo(userInfo.getSessionKey(), userInfo.getEncryptedData(), userInfo.getIv());

            return Result.success(wxMaUserInfo);
        } finally {
            WxMaConfigHolder.remove();//清理ThreadLocal
        }
    }
}
