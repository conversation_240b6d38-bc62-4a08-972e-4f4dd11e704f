package com.maixi.road.wechat.chatdata.media;

import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.wechat.constants.CpMsgConstants;

import lombok.extern.slf4j.Slf4j;

/**
 * 媒体处理器抽象基类
 * 
 * 实现了MediaProcessor接口的通用功能，为具体的媒体处理器提供基础实现
 */
@Slf4j
public abstract class AbstractMediaProcessor implements MediaProcessor {

    @Override
    public String getIdKey() {
        return CpMsgConstants.SDK_FILE_ID;
    }

    @Override
    public long getMaxFileSize() {
        return CpMsgConstants.MAX_SIZE_10M;
    }

    @Override
    public Block createErrorBlock(String reason) {
        String message = "此处有一条" + getTypeName() + "消息处理失败";
        if (reason != null && !reason.isEmpty()) {
            message += ", " + reason;
        }
        return Block.callout(Callout.buildTip(message));
    }

    /**
     * 格式化文件大小为可读的MB格式
     * 
     * @param fileSize 文件大小（字节）
     * @return 格式化后的大小字符串
     */
    protected String formatFileSize(long fileSize) {
        return "size = " + (fileSize / 1024 / 1024) + "Mb";
    }
    
    /**
     * 检查文件大小是否超过限制
     * 
     * @param fileSize 文件大小（字节）
     * @return 如果超过限制返回true，否则返回false
     */
    protected boolean isFileSizeExceeded(long fileSize) {
        if (fileSize > getMaxFileSize()) {
            log.warn("{}文件太大，暂不支持，fileSize={}", getTypeName(), fileSize);
            return true;
        }
        return false;
    }
}
