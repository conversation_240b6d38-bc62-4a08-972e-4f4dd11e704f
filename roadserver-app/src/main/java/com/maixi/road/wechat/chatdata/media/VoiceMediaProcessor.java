package com.maixi.road.wechat.chatdata.media;

import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.File;
import com.maixi.road.wechat.constants.CpMsgConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 语音媒体处理器
 * 
 * 处理语音类型的媒体，实现MediaProcessor接口
 */
@Slf4j
public class VoiceMediaProcessor extends AbstractMediaProcessor {

    @Override
    public String getMediaObjectKey(boolean fromChatRecord) {
        return fromChatRecord ? "content" : "voice";
    }

    @Override
    public String getFileExtension() {
        return "mpeg";
    }

    @Override
    public String getHomeDir() {
        return CpMsgConstants.VOICE_HOME_DIR;
    }

    @Override
    public String getSizeKey() {
        return CpMsgConstants.VOICE_SIZE;
    }

    @Override
    public String getTypeName() {
        return "音频";
    }

    @Override
    public Block createBlock(String url, String fileName) {
        return Block.file(new File(url, fileName));
    }

    @Override
    public String uploadFile(S3Manager s3Manager, String unionId, String filePath, String fileName) throws Exception {
        String voiceUrl = s3Manager.uploadFile(unionId, filePath, fileName);
        log.info("uploadVoice2Oss, voiceUrl={}", voiceUrl);
        return voiceUrl;
    }
}
