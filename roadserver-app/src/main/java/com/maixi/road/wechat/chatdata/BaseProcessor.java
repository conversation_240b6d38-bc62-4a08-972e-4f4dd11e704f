package com.maixi.road.wechat.chatdata;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.InvalidPathException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.google.common.collect.Lists;
import com.maixi.road.admin.biz.domain.ChatOpenList;
import com.maixi.road.admin.biz.service.IChatOpenListService;
import com.maixi.road.admin.biz.service.IUserMessageService;
import com.maixi.road.admin.manager.S3Manager;
import com.maixi.road.clipper.output.helper.MessageBuildHelper;
import com.maixi.road.cloudfunc.notion.dto.rq.BlockAppendRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.NotionBaseRS;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.enums.error.NotionErrCodeEnum;
import com.maixi.road.common.core.exception.NotionException;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.core.model.dto.ObsidianDTO;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.core.utils.NameUtils;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.integration.notion.model.page.Page;
import com.maixi.road.common.integration.notion.model.page.Parent;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.framework.ApplicationUtils;
import com.maixi.road.markdown.dto.MarkdownSaveRQ;
import com.maixi.road.markdown.dto.MarkdownSaveRS;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;
import com.maixi.road.wechat.chatdata.media.MediaProcessor;
import com.maixi.road.wechat.chatdata.media.MediaProcessorFactory;
import com.maixi.road.wechat.constants.CpMsgConstants;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMsgAuditService;

@Slf4j
public abstract class BaseProcessor implements Processor {

    // 用于异步处理blocks的执行器
    private final ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 处理剩余的blocks，将其分批异步追加到已创建的页面中
     * 
     * @param retainBlocks 剩余的blocks列表
     * @param accessToken  访问令牌
     * @param pageId       页面ID
     * @param unionId      用户唯一标识
     */
    protected void handleRetainBlocks(List<Block> retainBlocks, String accessToken, String pageId, String unionId) {
        executor.execute(() -> {
            // 将剩余的 blocks 按单批 100 个 block 分组
            List<List<Block>> partition = Lists.partition(retainBlocks, 100);
            for (List<Block> blocks : partition) {
                boolean appendIsSuccess = appendBlocks(blocks, accessToken, pageId, unionId);
                // 如果有一次失败，就不需要继续了
                if (!appendIsSuccess) {
                    log.error("handleRetainBlocks.appendBlocks fail, pageId={},unionId={}", pageId, unionId);
                    break;
                }
            }
        });
    }

    /**
     * 将blocks追加到已创建的页面中
     * 
     * @param blocks      要追加的blocks列表
     * @param accessToken 访问令牌
     * @param pageId      页面ID
     * @param unionId     用户唯一标识
     * @return 是否追加成功
     */
    protected boolean appendBlocks(List<Block> blocks, String accessToken, String pageId,
            String unionId) {
        BlockAppendRQ request = new BlockAppendRQ();
        request.setChildren(blocks);
        try {
            NotionBaseRS result = getNotionClient().appendBlockChildren(accessToken, pageId, request, unionId);
            if (result == null || !"list".equals(result.getObject())) {
                log.error("文章追加内容失败, result={}", JSON.toJSONString(request));
                return false;
            }
        } catch (IOException e) {
            log.error("文章追加内容异常 unionId={}", unionId, e);
            return false;
        }
        return true;
    }

    /**
     * 构建文件类型的Notion Block
     * 
     * 该方法处理微信消息中的文件附件，将其下载到本地临时目录，然后上传到S3存储，
     * 最终创建一个Notion文件Block用于展示。
     * 
     * @param message        包含文件信息的JSON消息对象
     * @param unionId        用户唯一标识，用于S3上传时的路径区分
     * @param sdk            企业微信SDK实例ID，用于获取媒体文件
     * @param fromChatRecord 标识消息是否来自聊天记录
     * @return 返回构建好的Notion文件Block
     * @throws WxErrorException 微信API调用异常
     */
    protected Block buildFileBlock(JSONObject message, String unionId, Long sdk, boolean fromChatRecord)
            throws WxErrorException {
        return buildMediaBlock(message, unionId, sdk, fromChatRecord, MsgTypeEnum.FILE);
    }

    /**
     * 构建音频类型的Notion Block
     * 
     * @param message        包含音频信息的JSON消息对象
     * @param unionId        用户唯一标识
     * @param sdk            企业微信SDK实例ID
     * @param fromChatRecord 标识消息是否来自聊天记录
     * @return 返回构建好的Notion音频Block
     * @throws WxErrorException 微信API调用异常
     */
    protected Block buildVoiceBlock(JSONObject message, String unionId, Long sdk, boolean fromChatRecord)
            throws WxErrorException {
        return buildMediaBlock(message, unionId, sdk, fromChatRecord, MsgTypeEnum.VOICE);
    }

    /**
     * 构建视频类型的Notion Block
     * 
     * @param message        包含视频信息的JSON消息对象
     * @param unionId        用户唯一标识
     * @param sdk            企业微信SDK实例ID
     * @param fromChatRecord 标识消息是否来自聊天记录
     * @return 返回构建好的Notion视频Block
     * @throws WxErrorException 微信API调用异常
     */
    protected Block buildVideoBlock(JSONObject message, String unionId, Long sdk, boolean fromChatRecord)
            throws WxErrorException {
        return buildMediaBlock(message, unionId, sdk, fromChatRecord, MsgTypeEnum.VIDEO);
    }

    /**
     * 构建图片类型的Notion Block
     * 
     * @param message        包含图片信息的JSON消息对象
     * @param unionId        用户唯一标识
     * @param sdk            企业微信SDK实例ID
     * @param fromChatRecord 是否来自聊天记录
     * @return Notion Block
     * @throws WxErrorException 微信API调用异常
     */
    protected Block buildImageBlock(JSONObject message, String unionId, Long sdk, boolean fromChatRecord)
            throws WxErrorException {
        return buildMediaBlock(message, unionId, sdk, fromChatRecord, MsgTypeEnum.IMAGE);
    }

    /**
     * 将消息发送到Notion平台并创建页面
     * 
     * 该方法负责构建Notion页面对象，并通过Notion API创建页面。方法会处理创建过程中可能出现的异常，
     * 并在失败时抛出适当的NotionException异常。
     * 由于Notion API对单次请求的blocks数量有限制（最多100个），此方法会自动处理blocks的截断和分批发送。
     * 
     * @param messageConfig 消息配置信息，包含数据库ID、访问令牌等Notion连接必要参数
     * @param title         页面标题
     * @param msgType       消息类型枚举，用于标识消息的类型
     * @param blocks        页面内容块列表，构成页面的主体内容
     * @param tags          页面标签数组
     * @return PageCreateRS Notion API返回的页面创建结果对象
     * @throws NotionException 当页面创建失败或发生其他异常时抛出，包含错误码和错误信息
     */
    protected PageCreateRS sendMessageToNotion(MessageFieldDTO messageConfig, String title, MsgTypeEnum msgType,
            List<Block> blocks, String[] tags) {
        // 处理blocks数量限制（Notion API单次请求最多保存100个block）
        if (blocks.size() > 500) {
            // 如果blocks数量超过500，返回提示信息
            Block callOut = Block.callout(Callout.buildTip("消息内容过长，请联系开发者反馈"));
            blocks = Collections.singletonList(callOut);
        }

        if (blocks.size() <= 100) {
            // 如果blocks数量不超过100，直接发送
            return sendMessageToNotionInternal(messageConfig, title, msgType, blocks, tags);
        } else {
            // 如果blocks数量超过100，需要分批发送
            List<Block> first = blocks.subList(0, 100);
            List<Block> second = blocks.subList(100, blocks.size());

            // 先创建页面，包含前100个blocks
            PageCreateRS pageCreateResult = sendMessageToNotionInternal(messageConfig, title, msgType, first, tags);

            // 然后追加剩余的blocks
            if (second.size() <= 100) {
                boolean appendIsSuccess = appendBlocks(second, messageConfig.getAccessToken(), pageCreateResult.getId(),
                        messageConfig.getUnionId());
                if (!appendIsSuccess) {
                    log.error("sendMessageToNotion.appendBlocks fail, pageId={},unionId={}", pageCreateResult.getId(),
                            messageConfig.getUnionId());
                }
            } else {
                handleRetainBlocks(second, messageConfig.getAccessToken(), pageCreateResult.getId(),
                        messageConfig.getUnionId());
            }

            return pageCreateResult;
        }
    }

    /**
     * 内部方法，实际发送消息到Notion平台并创建页面
     * 
     * @param messageConfig 消息配置信息
     * @param title         页面标题
     * @param msgType       消息类型枚举
     * @param blocks        页面内容块列表（已确保不超过100个）
     * @param tags          页面标签数组
     * @return PageCreateRS Notion API返回的页面创建结果对象
     * @throws NotionException 当页面创建失败或发生其他异常时抛出
     */
    private PageCreateRS sendMessageToNotionInternal(MessageFieldDTO messageConfig, String title, MsgTypeEnum msgType,
            List<Block> blocks, String[] tags) {
        // 构建Notion页面对象，设置父级数据库、属性和内容块
        Page page = Page.builder()
                .parent(Parent.build(messageConfig.getDatabaseId()))
                .properties(MessageBuildHelper.propertiesBuild(messageConfig, title, msgType, tags))
                .children(blocks)
                .build();
        try {
            // 调用Notion API客户端创建页面，支持重试机制
            PageCreateRS result = getNotionClient()
                    .createPageWithRetry(messageConfig.getAccessToken(), page, messageConfig.getUnionId());
            // 验证API返回结果是否正确（非空且对象类型为page）
            if (result == null || !"page".equals(result.getObject())) {
                // 记录同步失败的错误日志，包含消息类型、用户ID、内容和返回结果
                log.error("[用户数据同步NOTION失败],msgType:{},unionId={},content={},result={}", msgType.getName(),
                        messageConfig.getUnionId(), title, JSON.toJSONString(result));
                String errorMsg = "Notion保存失败";
                // 如果返回结果不为空，添加错误码到错误信息中
                if (result != null) {
                    errorMsg += ", code=" + result.getCode();
                }
                // 抛出Notion创建页面失败的异常
                throw NotionException.create(NotionErrCodeEnum.NOTION_CREATE_PAGE_FAILED, errorMsg);
            }
            return result;
        } catch (Exception e) {
            // 记录同步过程中发生的异常，包含异常消息
            log.error("[用户数据同步NOTION异常],msgType:{},unionId={},content={},exceptionMsg={}", msgType.getName(),
                    messageConfig.getUnionId(), title, e.getMessage());
            // 将捕获的异常转换为NotionException并抛出
            throw NotionException.create(NotionErrCodeEnum.NOTION_CREATE_PAGE_FAILED, e.getMessage());
        }
    }

    /**
     * 将消息保存为Markdown文件
     * 
     * 该方法负责将消息内容保存为Markdown文件。
     * 
     * @param messageConfig 消息配置信息，包含用户ID等必要参数
     * @param title         页面标题
     * @param msgType       消息类型枚举，用于标识消息的类型
     * @param blocks        页面内容块列表，构成页面的主体内容
     * @param tags          页面标签数组
     * @return 保存结果，true表示成功，false表示失败
     */
    protected PageCreateRS sendMessageToMarkdown(ObsidianDTO obConfig, String unionId, String title,
            MsgTypeEnum msgType,
            List<Block> blocks, String[] tags) {
        try {
            // 获取用户配置
            UserConfig userConfig = getUserConfig(unionId);
            // 断言用户配置不为空，消除编辑器空指针警告
            Objects.requireNonNull(userConfig, "用户配置不能为空");
            String savePath = userConfig.getMarkdownSavePath();

            // 构建 ResolveFormRQ 对象
            ResolveFormRQ userForm = new ResolveFormRQ();
            userForm.setTitle(title);
            userForm.setCategory(msgType.name());
            // 如果有标签，设置标签
            if (tags != null && tags.length > 0) {
                userForm.setTags(Arrays.asList(tags));
            }
            userForm.setBlocks(blocks);

            // 构建 MarkdownSaveRQ 请求对象
            MarkdownSaveRQ request = MarkdownSaveRQ.builder()
                    .unionId(unionId)
                    .userForm(userForm)
                    .savePath(savePath)
                    .fileName(title)
                    .createAssetsDir(false)
                    .downloadImages(false)
                    .includeMetadata(true)
                    .obConfig(obConfig)
                    .isArticle(false)
                    .build();

            // 调用 MarkdownOutputService 保存 Markdown 文件
            Result<MarkdownSaveRS> result = getMarkdownOutputService().saveArticle(request);

            // 检查保存结果
            if (result != null && result.isSuccess()) {
                log.info("保存 Markdown 文件成功，filePath={}", result.getData().getFilePath());
                PageCreateRS pageCreateRS = new PageCreateRS();
                pageCreateRS.setId(result.getData().getFileName());
                return pageCreateRS;
            } else {
                log.error("保存 Markdown 文件失败，result={}", JSON.toJSONString(result));
                return null;
            }
        } catch (Exception e) {
            log.error("保存 Markdown 文件异常，unionId={}", unionId, e);
            return null;
        }
    }

    /**
     * 根据用户配置将消息输出到对应平台
     * 
     * 该方法根据用户配置的输出类型，决定将消息发送到Notion、保存为Markdown或两者都执行。
     * 
     * @param messageConfig 消息配置信息，包含用户ID等参数
     * @param title         页面标题
     * @param msgType       消息类型枚举
     * @param blocks        页面内容块列表
     * @param tags          页面标签数组
     * @return 输出结果，返回Notion创建的页面结果，如果只保存为Markdown则返回null
     */
    protected PageCreateRS output(String unionId, String title, MsgTypeEnum msgType,
            List<Block> blocks, String[] tags) {
        PageCreateRS pageResult = null;

        // 获取用户配置
        UserConfig userConfig = getUserConfig(unionId);
        // 断言用户配置不为空，消除编辑器空指针警告
        Objects.requireNonNull(userConfig, "用户配置不能为空");

        // 获取用户配置的输出类型
        String outputType = userConfig.getOutputType();

        // 如果未指定输出类型，默认为notion
        if (outputType == null || outputType.isEmpty()) {
            outputType = "notion";
        }

        MessageFieldDTO messageConfig = null;
        if (!outputType.equals("markdown")) {
            messageConfig = ApplicationUtils.getBean(IUserMessageService.class).queryMessageFieldDto(unionId);
            // 获取用户消息配置
            if (!validateMessageConfig(messageConfig)) {
                log.error("MESSAGE_SEND >>>>>> 未授权，无法进行数据同步, user_message={}", JSON.toJSONString(messageConfig));
                pageResult = new PageCreateRS();
                pageResult.setCode(ErrorCodeEnum.BIZ_ERROR.getCode().toString());
                pageResult.setMessage("未完成消息数据库配置");
                return pageResult;
            }
        }

        // 根据输出类型决定发送目标
        switch (outputType.toLowerCase()) {
            case "notion":
                // 只发送到Notion
                try {
                    pageResult = sendMessageToNotion(messageConfig, title, msgType, blocks, tags);
                } catch (Exception e) {
                    log.error("发送消息到Notion失败", e);
                }
                break;

            case "markdown":
                // 只保存为Markdown
                pageResult = sendMessageToMarkdown(userConfig.getObConfig(), unionId, title, msgType,
                        blocks,
                        tags);
                // 如果只保存为Markdown，返回null
                break;

            case "both":
                // 同时发送到Notion和保存为Markdown
                try {
                    pageResult = sendMessageToNotion(messageConfig, title, msgType, blocks, tags);
                } catch (Exception e) {
                    log.error("发送消息到Notion失败", e);
                }

                // 无论 Notion 是否成功，都尝试保存为 Markdown
                pageResult = sendMessageToMarkdown(userConfig.getObConfig(), unionId, title, msgType,
                        blocks,
                        tags);
                break;

            default:
                // 默认发送到Notion
                try {
                    pageResult = sendMessageToNotion(messageConfig, title, msgType, blocks, tags);
                } catch (Exception e) {
                    log.error("发送消息到Notion失败", e);
                }
                break;
        }

        return pageResult;
    }

    protected abstract NotionClient getNotionClient();

    /**
     * 获取Markdown输出服务
     * 子类需要实现此方法提供MarkdownOutputService实例
     * 
     * @return MarkdownOutputService实例
     */
    protected abstract MarkdownOutputService getMarkdownOutputService();

    protected abstract WxCpMsgAuditService getMsgAuditService();

    protected abstract S3Manager getS3Manager();

    protected abstract IChatOpenListService getChatOpenListService();

    /**
     * 获取配置查询API
     * 子类需要实现此方法提供ConfigQueryApi实例
     * 
     * @return ConfigQueryApi实例
     */
    protected abstract ConfigQueryApi getConfigQueryApi();

    /**
     * 获取用户配置
     * 
     * @param userId 用户ID
     * @return 用户配置
     */
    protected UserConfig getUserConfig(String userId) {
        return getConfigQueryApi().queryConfig(userId);
    }

    protected boolean isMessageValid(JSONObject message) {
        Long msgtime = message.getLong("msgtime");
        LocalDateTime localDateTime = LocalDateTime.ofEpochSecond(msgtime / 1000, 0, ZoneOffset.of("+8"));
        return !LocalDateTime.now().minusDays(5).isAfter(localDateTime);
    }

    protected boolean isAuthorizedUser(String unionId) {
        List<String> unionIdList = getChatOpenListService().list().stream().map(ChatOpenList::getUnionId).toList();
        return unionIdList.contains(unionId);
    }

    /**
     * 通用媒体处理模板方法
     * 
     * 该方法处理微信消息中的媒体附件（图片、音频、视频、文件），将其下载到本地临时目录，
     * 然后上传到S3存储，最终创建并返回对应类型的Notion Block。
     * 
     * 处理流程：
     * 1. 从消息中提取媒体信息
     * 2. 验证文件大小是否超过限制
     * 3. 下载媒体文件到本地临时目录
     * 4. 上传文件到S3存储服务
     * 5. 创建并返回对应类型的Notion Block
     * 
     * @param message        包含媒体信息的JSON消息对象
     * @param unionId        用户唯一标识
     * @param sdk            企业微信SDK实例ID
     * @param fromChatRecord 标识消息是否来自聊天记录
     * @param mediaType      媒体类型
     * @return 返回构建好的Notion Block，如处理失败则返回提示性的callout Block
     * @throws WxErrorException 微信API调用异常
     */
    protected Block buildMediaBlock(JSONObject message, String unionId, Long sdk, boolean fromChatRecord,
            MsgTypeEnum mediaType) throws WxErrorException {
        // 获取对应的媒体处理器
        MediaProcessor processor = MediaProcessorFactory.getProcessor(mediaType);

        // 记录处理开始的日志
        log.info("buildMediaBlock，mediaType={}, message={}, fromChatRecord={}", mediaType.getName(), message,
                fromChatRecord);

        // 从消息中提取媒体对象
        JSONObject mediaObj = message.getJSONObject(processor.getMediaObjectKey(fromChatRecord));

        // 提取媒体文件ID
        String sdkFileId = mediaObj.getString(processor.getIdKey());

        // 生成文件名
        String fileExt = processor.getFileExtension();
        String fileName;

        // 对于文件类型，需要特殊处理文件名和扩展名
        if (mediaType == MsgTypeEnum.FILE) {
            fileExt = mediaObj.getString(CpMsgConstants.FILE_EXT);
            fileName = mediaObj.getString(CpMsgConstants.FILE_NAME);
            // 确保文件名包含扩展名
            if (!fileName.contains(fileExt)) {
                fileName = fileName + "." + fileExt;
            }
        } else {
            // 其他媒体类型使用随机文件名
            fileName = NanoIdUtils.randomNanoId() + (fileExt != null ? "." + fileExt : "");
        }

        // 检查文件大小是否超过限制
        String sizeKey = processor.getSizeKey();
        if (sizeKey != null) {
            Long fileSize = null;
            try {
                // 尝试获取文件大小，不同媒体类型可能使用Integer或Long
                Object size = mediaObj.get(sizeKey);
                if (size instanceof Integer) {
                    fileSize = ((Integer) size).longValue();
                } else if (size instanceof Long) {
                    fileSize = (Long) size;
                }
            } catch (Exception e) {
                log.warn("获取文件大小失败: {}", e.getMessage());
            }

            if (fileSize != null && fileSize > processor.getMaxFileSize()) {
                log.warn("{}文件太大，暂不支持，fileSize={}", processor.getTypeName(), fileSize);
                return processor.createErrorBlock("size = " + (fileSize / 1024 / 1024) + "Mb");
            }
        }

        // 生成临时文件存储路径 - 使用安全的路径构建方法
        String filePath = buildSafeFilePath(processor.getHomeDir(), fileName);
        log.info("安全路径构建完成，filePath={}", filePath);

        try {
            // 下载媒体文件
            getMsgAuditService().getMediaFile(sdk, sdkFileId, null, null, 10000, filePath);
        } catch (Exception e) {
            log.error("getMediaFile, occur exception, sdk={}", sdk, e);
            return processor.createErrorBlock(null);
        }

        String mediaUrl = null;
        try {
            // 上传媒体文件到S3
            mediaUrl = processor.uploadFile(getS3Manager(), unionId, filePath, fileName);
        } catch (Exception e) {
            log.error("upload{}2Oss, occur exception, unionId={}", processor.getTypeName(), unionId, e);
        } finally {
            // 清理临时文件
            try {
                java.nio.file.Files.deleteIfExists(Path.of(filePath));
            } catch (Exception e) {
                log.warn("删除临时文件失败: {}", filePath, e);
            }
        }

        // 检查上传结果
        if (mediaUrl == null) {
            return processor.createErrorBlock(null);
        }

        // 创建并返回对应类型的Notion Block
        return processor.createBlock(mediaUrl, fileName);
    }

    /**
     * 安全构建文件路径
     * 
     * 该方法提供多层防护来处理可能的路径编码问题：
     * 1. 文件名安全化处理
     * 2. 编码验证
     * 3. 异常捕获和回退机制
     * 4. 详细的日志记录
     * 
     * @param homeDir  基础目录路径
     * @param fileName 原始文件名
     * @return 安全构建的文件路径字符串
     */
    protected String buildSafeFilePath(String homeDir, String fileName) {
        // 记录原始文件名用于调试
        log.info("开始构建安全文件路径，homeDir={}, 原始fileName={}", homeDir, fileName);

        // 第一层防护：文件名安全化处理
        String safeFileName = NameUtils.sanitizeFileName(fileName);
        log.info("文件名安全化处理完成，safeFileName={}", safeFileName);

        // 第二层防护：编码验证
        try {
            // 验证文件名是否可以正确编码为字节
            byte[] nameBytes = safeFileName.getBytes(StandardCharsets.UTF_8);
            String decodedName = new String(nameBytes, StandardCharsets.UTF_8);

            if (!safeFileName.equals(decodedName)) {
                log.warn("文件名编码验证失败，使用回退文件名。原始={}, 解码后={}", safeFileName, decodedName);
                safeFileName = generateFallbackFileName(fileName);
            }
        } catch (Exception e) {
            log.warn("文件名编码验证异常，使用回退文件名：{}", e.getMessage());
            safeFileName = generateFallbackFileName(fileName);
        }

        // 第三层防护：安全的路径构建
        String filePath = null;
        try {
            Path path = Paths.get(homeDir, safeFileName);
            filePath = path.toString();
            log.info("路径构建成功，filePath={}", filePath);

        } catch (InvalidPathException e) {
            log.error("路径构建失败，使用回退方案。错误信息：{}", e.getMessage());

            // 第四层防护：回退机制
            String fallbackFileName = generateFallbackFileName(fileName);
            try {
                Path fallbackPath = Paths.get(homeDir, fallbackFileName);
                filePath = fallbackPath.toString();
                log.info("回退路径构建成功，fallbackFilePath={}", filePath);
            } catch (Exception fallbackException) {
                log.error("回退路径构建也失败，使用最后的安全方案：{}", fallbackException.getMessage());

                // 最后的安全方案：使用时间戳文件名
                String timestampFileName = "file_" + System.currentTimeMillis() + getFileExtension(fileName);
                filePath = Paths.get(homeDir, timestampFileName).toString();
                log.info("使用时间戳安全文件名，finalFilePath={}", filePath);
            }
        } catch (Exception e) {
            log.error("路径构建出现未预期异常：{}", e.getMessage(), e);

            // 使用最基本的安全方案
            String timestampFileName = "file_" + System.currentTimeMillis() + getFileExtension(fileName);
            filePath = Paths.get(homeDir, timestampFileName).toString();
            log.info("使用时间戳安全文件名作为最终方案，finalFilePath={}", filePath);
        }

        return filePath;
    }

    /**
     * 生成回退文件名
     * 
     * 当原始文件名无法安全处理时，生成一个安全的回退文件名
     * 
     * @param originalFileName 原始文件名
     * @return 安全的回退文件名
     */
    private String generateFallbackFileName(String originalFileName) {
        String extension = getFileExtension(originalFileName);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fallbackName = "safe_file_" + timestamp + extension;

        log.info("生成回退文件名，原始={}, 回退={}", originalFileName, fallbackName);
        return fallbackName;
    }

    /**
     * 提取文件扩展名
     * 
     * @param fileName 文件名
     * @return 文件扩展名（包含点号），如果没有扩展名则返回空字符串
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }

        return "";
    }

    /**
     * 验证消息配置是否有效
     * 
     * @param messageConfig 消息配置
     * @return 是否有效
     */
    private boolean validateMessageConfig(MessageFieldDTO messageConfig) {
        return messageConfig != null &&
                messageConfig.getDatabaseId() != null &&
                messageConfig.getAccessToken() != null;
    }
}