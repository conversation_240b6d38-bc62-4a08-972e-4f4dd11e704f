package com.maixi.road.clipper.service.tagresolver;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.ListItem;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.integration.notion.tags.TextTag;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * li 标签解析器
 * 负责解析 li 元素，能够分离文本内容和嵌套的块级元素
 */
@Slf4j
@Component
public class LiTagResolver implements ContentTagResolver {

    @Resource
    private GlobalElementParser globalElementParser;

    @Override
    public boolean supports(Node element) {
        return "li".equalsIgnoreCase(element.nodeName());
    }

    /**
     * 解析
     * <li>元素.
     * 此方法将
     * <li>的直接文本内容和嵌套的块级元素（如
     * <ul>
     * ,
     * <ol>
     * , <img>）分离开来。
     * 文本内容构成本级的列表项，而块级元素则通过 globalElementParser 递归解析成独立的 Block.
     *
     * @param node The
     *             <li>node to resolve.
     * @return A list of blocks, starting with the
     *         <li>'s own content,
     *         followed by blocks from any nested elements.
     */
    @Override
    public List<Block> resolve(Node node) {
        Element liElement = (Element) node;

        log.debug("=== LiTagResolver 开始解析 <li> 元素: {} ===",
                liElement.text().substring(0, Math.min(50, liElement.text().length())));

        // 临时 Element 用于存放 <li> 的直接文本和内联子元素
        Element contentHolder = new Element("div");
        List<Node> blockLevelChildren = Lists.newArrayList();

        // 1. 分离内联内容和块级内容
        for (Node childNode : liElement.childNodes()) {
            if (isBlockLevel(childNode)) {
                blockLevelChildren.add(childNode);
                log.debug("发现块级子元素: <{}>", childNode.nodeName());
            } else {
                contentHolder.appendChild(childNode.clone());
            }
        }

        log.debug("分离结果: 块级子元素 {} 个", blockLevelChildren.size());

        // 2. 处理 <li> 的主内容
        List<TextTag> textTags = TextTagResolver.resolverTextTag(contentHolder);
        List<RichText> richTexts = textTags.stream()
                .map(TextTag::toRichText)
                .collect(Collectors.toList());

        // 清理行首可能存在的列表标记
        removeLeadingListMarkers(richTexts);

        log.debug("提取的文本内容: {}", richTexts.stream().map(RichText::getPlain_text).collect(Collectors.joining()));

        // 3. 递归处理块级子元素，生成子 Block 列表
        List<Block> childrenBlocks = Lists.newArrayList();
        for (Node blockChild : blockLevelChildren) {
            if (blockChild instanceof Element) {
                List<Block> childBlocks = globalElementParser.parseElement((Element) blockChild);
                childrenBlocks.addAll(childBlocks);
                log.debug("块级子元素 <{}> 解析出 {} 个Block", blockChild.nodeName(), childBlocks.size());
            }
        }

        log.debug("所有块级子元素共解析出 {} 个Block", childrenBlocks.size());

        // 4. 构建 ListItem，并填充 children
        ListItem listItem = ListItem.builder()
                .rich_text(richTexts)
                .children(childrenBlocks.isEmpty() ? null : childrenBlocks) // 如果没有子块，则为null
                .build();

        // 5. 根据父节点类型创建最终的 Block
        Block finalBlock;
        String parentNodeName = Optional.ofNullable(liElement.parent())
                .map(Element::nodeName).orElse("");

        if ("ol".equalsIgnoreCase(parentNodeName)) {
            finalBlock = Block.buildNumberedListItem(listItem);
            log.debug("创建 numbered_list_item Block");
        } else {
            finalBlock = Block.buildBulletedListItem(listItem);
            log.debug("创建 bulleted_list_item Block");
        }

        // 6. 改进空内容判断逻辑，避免误删有效的列表项
        boolean hasContent = hasValidContent(richTexts, childrenBlocks);
        if (!hasContent) {
            log.debug("<li> 元素确认为空，不生成任何 block: {}", liElement.outerHtml());
            return Lists.newArrayList();
        }

        log.debug("=== LiTagResolver 解析完成，返回1个Block ===");
        return Lists.newArrayList(finalBlock);
    }

    /**
     * 判断是否包含有效内容
     * 改进空内容的判断逻辑，考虑HTML实体和有意义的空白字符
     * 
     * @param richTexts      富文本列表
     * @param childrenBlocks 子块列表
     * @return 是否包含有效内容
     */
    private boolean hasValidContent(List<RichText> richTexts, List<Block> childrenBlocks) {
        // 如果有子块，则认为有内容
        if (!childrenBlocks.isEmpty()) {
            return true;
        }

        // 检查富文本内容
        if (richTexts == null || richTexts.isEmpty()) {
            return false;
        }

        for (RichText richText : richTexts) {
            if (richText == null) {
                continue;
            }

            String plainText = richText.getPlain_text();
            if (plainText == null) {
                continue;
            }

            // 移除空白字符后检查
            String trimmedText = plainText.trim();
            if (!trimmedText.isEmpty()) {
                // 检查是否只是常见的HTML实体空白字符
                if (!isOnlyWhitespaceEntities(trimmedText)) {
                    log.debug("发现有效内容: '{}'", trimmedText);
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查字符串是否只包含HTML空白实体
     * 
     * @param text 要检查的文本
     * @return 是否只包含空白实体
     */
    private boolean isOnlyWhitespaceEntities(String text) {
        // 常见的HTML空白实体：&nbsp;, &ensp;, &emsp;, &thinsp;等
        String withoutEntities = text.replaceAll("(&nbsp;|&ensp;|&emsp;|&thinsp;|&#160;|&#8194;|&#8195;|&#8201;)", "")
                .trim();
        return withoutEntities.isEmpty();
    }

    /**
     * 移除 RichText 列表第一个元素内容中可能包含的列表前缀 (e.g., "1. ", "- ").
     * 
     * @param richTexts The list of RichText objects.
     */
    private void removeLeadingListMarkers(List<RichText> richTexts) {
        if (CollectionUtils.isEmpty(richTexts)) {
            return;
        }

        RichText firstRichText = richTexts.get(0);
        if (firstRichText == null || firstRichText.getText() == null || firstRichText.getText().getContent() == null) {
            return;
        }

        String originalText = firstRichText.getText().getContent();
        // Regex to match leading numbers (e.g., "1.", "2. "), bullets (e.g., "-", "*",
        // "•")
        String cleanedText = originalText.replaceAll("^\\s*(\\d+\\.|[-•*])\\s*", "");

        if (!originalText.equals(cleanedText)) {
            firstRichText.getText().setContent(cleanedText);
            firstRichText.setPlain_text(cleanedText);
            log.debug("移除了列表项的前导标记: '{}' -> '{}'", originalText, cleanedText);
        }
    }

    /**
     * 判断一个节点是否为块级元素
     *
     * @param node The node to check.
     * @return true if the node is a block-level element, false otherwise.
     */
    private boolean isBlockLevel(Node node) {
        if (!(node instanceof Element)) {
            // TextNodes are inline
            return node instanceof TextNode ? false : true;
        }
        Element element = (Element) node;
        // 使用 jsoup 内置的 isBlock() 方法，并添加自定义标签
        // 常见块级标签, ul, ol, img, div, p, h1-h6, etc.
        return element.tag().isBlock() || "img".equalsIgnoreCase(element.tagName());
    }
}
