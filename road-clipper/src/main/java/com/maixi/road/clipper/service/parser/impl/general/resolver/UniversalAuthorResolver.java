package com.maixi.road.clipper.service.parser.impl.general.resolver;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson.JSON;
import com.maixi.road.clipper.service.parser.impl.general.dto.AuthorRule;
import com.maixi.road.common.core.model.dto.PropertyRuleDTO;

public class UniversalAuthorResolver {

    public static String parseAuthor(Document document, List<PropertyRuleDTO> list){
        String author = null;
        
        // 遍历规则，尝试提取作者信息
        for (PropertyRuleDTO authorRule : list) {
            // 将规则表达式解析为作者规则对象
            AuthorRule rule = JSON.parseObject(authorRule.getExpression(), AuthorRule.class);
            String functionName = rule.getFunction();
            
            // 根据函数名选择不同的作者提取方法
            switch (functionName) {
                case "getAuthor":
                    // 直接从CSS选择器获取作者
                    author = getAuthor(document, rule.getCssQuery());
                    break;
                case "getAuthorFromAttr":
                    // 从CSS选择器元素的指定属性获取作者
                    author = getAuthorFromAttr(document, rule.getCssQuery(), rule.getAttr());
                    break;
                case "getAuthorWithTag":
                    // 从CSS选择器元素的指定子标签获取作者
                    author = getAuthorWithTag(document, rule.getCssQuery(), rule.getTag());
                    break;
                case "getAuthorWithTagFromAttr":
                    // 从CSS选择器元素的指定子标签的指定属性获取作者
                    author = getAuthorWithTagFromAttr(document, rule.getCssQuery(), rule.getTag(), rule.getAttr());
                    break;
                default:
                    break;
            }

            // 如果找到作者，立即返回
            if (StringUtils.isNoneBlank(author)) {
                break;
            }
        }
        return author;
    }


    private static String getAuthor(Document document, String cssQuery) {
        return Optional.ofNullable(document.selectFirst(cssQuery))
                .map(Element::text)
                .map(String::trim)
                .map(e -> StringUtils.isBlank(e) ? null : e.replace("&nbsp;", ""))
                .orElse(null);
    }

    private static String getAuthorWithTag(Document document, String cssQuery, String tag) {
        return Optional.ofNullable(document.selectFirst(cssQuery))
                .map(element -> element.getElementsByTag(tag))
                .filter(elements -> !elements.isEmpty())
                .map(Elements::first)
                .map(Element::text)
                .map(String::trim)
                .map(e -> StringUtils.isBlank(e) ? null : e.replace("&nbsp;", ""))
                .orElse(null);
    }

    private static String getAuthorFromAttr(Document document, String cssQuery, String attr) {
        return Optional.ofNullable(document.selectFirst(cssQuery))
                .map(e -> e.attr(attr))
                .map(String::trim)
                .map(e -> StringUtils.isBlank(e) ? null : e.replace("&nbsp;", ""))
                .orElse(null);
    }

    private static String getAuthorWithTagFromAttr(Document document, String cssQuery, String tag, String attr) {
        return Optional.ofNullable(document.selectFirst(cssQuery))
                .map(element -> element.getElementsByTag(tag))
                .filter(elements -> !elements.isEmpty())
                .map(e -> e.attr(attr))
                .map(String::trim)
                .map(e -> StringUtils.isBlank(e) ? null : e.replace("&nbsp;", ""))
                .orElse(null);
    }
}
