package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class JiKeParser extends AbstractParser {

    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private GlobalElementParser globalElementParser;

    @Override
    public boolean supports(String url) {
        return url.contains("okjike.com/originalPosts");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = getDocument(url);
        if (url.contains("web")) {
            Elements elements = document.select("div[class=\"content_truncate__tFX8J\"]");
            Element first = elements.first();
            if (first == null) {
                throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "即刻解析异常");
            }
            return globalElementParser.parseElement(first);
        }

        Elements elements = document.select("div[class=\"jsx-1330250023 wrap\"]");
        Element first = elements.first();
        if (first == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "即刻解析异常");
        }
        return globalElementParser.parseElement(first);
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.JK.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_JK;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_JK;
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements authorElements = document.select("div[class=\"jsx-3802438259 title\"]");
        Optional<Element> first = authorElements.stream().findFirst();
        return first.map(Element::text).orElse("未知");
    }

    @Override
    protected String parsePublishTime(Document document) {
        String originUrl = document.location();
        if (originUrl.contains("web")) {
            return getPublishTimeOfWeb(document, originUrl).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        Elements scripts = document.select("script");
        Optional<Element> first = scripts.stream().filter(e -> e.attr("id").equals("__NEXT_DATA__")).findFirst();
        if (first.isPresent()) {
            String text = first.get().data();
            JSONObject jsonObject = JSON.parseObject(text);
            try {
                String createdAt = jsonObject.getJSONObject("props")
                        .getJSONObject("pageProps")
                        .getJSONObject("post").getString("createdAt");
                Instant instant = Instant.parse(createdAt);
                return LocalDateTime.ofInstant(instant, java.time.ZoneId.systemDefault())
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                log.error("即刻动态解析发布时间异常,url={}", originUrl, e);
            }
        }
        return null;
    }

    private LocalDateTime getPublishTimeOfWeb(Document document, String originUrl) {
        Elements times = document.getElementsByTag("time");
        List<String> timeList = times.stream().filter(e -> e.hasAttr("datetime")).map(e -> e.attr("datetime"))
                .collect(Collectors.toList());
        // 2024-12-06T11:07:55.211Z
        log.info("timeList.size = {}", timeList.size());
        for (String timeStr : timeList) {
            try {
                return Instant.parse(timeStr)
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
            } catch (Exception e) {
                log.error("即刻动态解析发布时间异常,url={}", originUrl, e);
            }
        }
        return null;
    }

}
