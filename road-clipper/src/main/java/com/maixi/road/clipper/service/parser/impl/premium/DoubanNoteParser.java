package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.clipper.service.tagresolver.ImageTagResolver;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("doubanNoteParser")
public class DoubanNoteParser extends AbstractParser {

    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private ImageTagResolver imgTagResolver;

    @Override
    public boolean supports(String url) {
        return url.contains("douban.com/note/");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);
        Element root = document.getElementById("link-report");
        if (root == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "豆瓣日记解析失败");
        }
        Elements elements = root.select("div[class=\"note\"]");
        Element element = elements.first();
        if (element == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "豆瓣日记解析异常");
        }
        List<Block> blocks = globalElementParser.parseElement(element);
        return blocks;
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.DOUBAN_NOTE.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_DOUBAN;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_DOUBAN;
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements elements = document.select("span[class=\"pub-date\"]");
        if (elements.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        String publicTime = Optional.ofNullable(elements.first()).map(Element::text).orElse(null);
        if (StringUtils.isBlank(publicTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return publicTime.trim();
    }

    @Override
    protected String parseTitle(Document document) {
        Elements elements = document.getElementsByTag("h1");
        if (elements.isEmpty()) {
            return "豆瓣日记-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return Optional.ofNullable(elements.first()).map(Element::text).orElse(null);
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements elements = document.select("a[class=\"note-author\"]");
        if (elements.isEmpty()) {
            return "未知";
        }
        Element first = elements.first();
        return Optional.ofNullable(first).map(Element::text).orElse("未知");
    }

}
