package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class XiaoYuZhouParser extends AbstractParser {

    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private GlobalElementParser globalElementParser;

    @Override
    public boolean supports(String url) {
        return url.contains("xiaoyuzhoufm.com/episode");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = getDocument(url);
        Elements elements = document.select("div[class=\"sn-content\"]");
        Element element = elements.first();
        if (element == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "小宇宙文章解析异常");
        }
        List<Block> blocks = globalElementParser.parseElement(element);
        return blocks;
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.XyuZhou.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_XYZ;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_XYZ;
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements times = document.getElementsByTag("time");
        if (times.isEmpty()) {
            return null;
        }
        List<String> timeList = times.stream()
                .filter(e -> e.hasAttr("datetime"))
                .map(e -> e.attr("datetime")).collect(Collectors.toList());

        for (String timeStr : timeList) {
            try {
                return Instant.parse(timeStr)
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                log.error("小宇宙解析发布时间异常,url={}", document.location(), e);
            }
        }
        return null;
    }

    @Override
    protected String parseAuthor(Document document) {
        Element element = document.select("div.podcast-title > a.name").first();
        if (element == null) {
            return "小宇宙";
        }
        return element.text();
    }

}
