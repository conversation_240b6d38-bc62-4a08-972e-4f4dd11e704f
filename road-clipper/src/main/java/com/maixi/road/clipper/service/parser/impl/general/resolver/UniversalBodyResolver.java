package com.maixi.road.clipper.service.parser.impl.general.resolver;

import java.util.List;

import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;

import com.alibaba.fastjson.JSON;
import com.maixi.road.clipper.service.parser.impl.general.dto.ArticleRuleObject;
import com.maixi.road.common.core.model.dto.PropertyRuleDTO;
import com.maixi.road.common.integration.notion.enums.PropertyRuleExpressionTypeEnum;

public class UniversalBodyResolver {

    /**
     * 估算候选文章元素
     * <p>
     * 根据预定义的属性规则，尝试从文档中找出最可能包含文章内容的元素
     * </p>
     *
     * @param document JSoup文档对象
     * @return 找到的文章元素，如果未找到则返回null
     */
    public static Element estimateCandidateArticleElement(Document document,List<PropertyRuleDTO> list) {
        Element articleElement = null;

        // 遍历规则，按照ID、TAG、CLASS三种类型尝试查找元素
        for (PropertyRuleDTO rule : list) {
            // 将规则表达式解析为文章规则对象
            ArticleRuleObject articleRuleObject = JSON.parseObject(rule.getExpression(), ArticleRuleObject.class);

            // 根据规则类型选择不同的查找方式
            if (PropertyRuleExpressionTypeEnum.ID.getType().equals(articleRuleObject.getType())) {
                // 通过ID查找元素
                articleElement = document.getElementById(articleRuleObject.getValue());
            } else if (PropertyRuleExpressionTypeEnum.TAG.getType().equals(articleRuleObject.getType())) {
                // 通过标签名查找元素
                articleElement = document.getElementsByTag(articleRuleObject.getValue()).first();
            } else if (PropertyRuleExpressionTypeEnum.CLAZZ.getType().equals(articleRuleObject.getType())) {
                // 通过类名查找元素
                articleElement = document.getElementsByClass(articleRuleObject.getValue()).first();
            }

            // 如果找到元素，立即返回
            if (articleElement != null) {
                break;
            }
        }
        return articleElement;
    }
}
