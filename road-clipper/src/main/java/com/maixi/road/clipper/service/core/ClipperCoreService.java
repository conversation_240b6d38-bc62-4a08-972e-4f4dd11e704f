package com.maixi.road.clipper.service.core;

import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.ResolveRS;

/**
 * 剪藏服务接口
 */
public interface ClipperCoreService {

    /**
     * 解析URL内容
     * 
     * @param url    要解析的URL
     * @param userId 用户ID
     * @return 解析结果
     */
    ResolveRS parseUrl(String url, String userId);

    /**
     * 提交文章
     * 
     * @param article 文章参数
     * @param userId  用户ID
     * @return 是否成功
     */
    boolean saveContent(ResolveFormRQ article, String userId);

    /**
     * 保存文本消息
     * 
     * @param content 消息内容
     * @param tags    标签数组
     * @param userId  用户ID
     * @return 保存结果消息
     */
    String saveTextMessage(String content, String[] tags, String userId);

}
