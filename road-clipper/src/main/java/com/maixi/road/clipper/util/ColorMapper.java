package com.maixi.road.clipper.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.maixi.road.common.integration.notion.enums.ColorEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 颜色映射工具类
 * 负责将CSS颜色解析并映射到Notion支持的颜色
 */
@Slf4j
public class ColorMapper {

    /**
     * RGB颜色类
     */
    public static class RGB {
        public final int r, g, b;

        public RGB(int r, int g, int b) {
            this.r = Math.max(0, Math.min(255, r));
            this.g = Math.max(0, Math.min(255, g));
            this.b = Math.max(0, Math.min(255, b));
        }

        /**
         * 计算与另一个RGB颜色的欧几里得距离
         */
        public double distanceTo(RGB other) {
            return Math.sqrt(
                    Math.pow(this.r - other.r, 2) +
                            Math.pow(this.g - other.g, 2) +
                            Math.pow(this.b - other.b, 2));
        }

        /**
         * 计算颜色的饱和度（0-1）
         */
        public double getSaturation() {
            int max = Math.max(Math.max(r, g), b);
            int min = Math.min(Math.min(r, g), b);

            if (max == 0)
                return 0;
            return (double) (max - min) / max;
        }

        /**
         * 计算颜色的亮度（0-1）
         */
        public double getBrightness() {
            return Math.max(Math.max(r, g), b) / 255.0;
        }
    }

    // Notion颜色与RGB值的映射表（基于常见的颜色定义）
    private static final Map<ColorEnum, RGB> NOTION_COLOR_MAP = new HashMap<>();

    static {
        NOTION_COLOR_MAP.put(ColorEnum._default, new RGB(0, 0, 0)); // 黑色
        NOTION_COLOR_MAP.put(ColorEnum._blue, new RGB(0, 123, 255)); // 蓝色
        NOTION_COLOR_MAP.put(ColorEnum._brown, new RGB(139, 69, 19)); // 棕色
        NOTION_COLOR_MAP.put(ColorEnum._gray, new RGB(128, 128, 128)); // 灰色
        NOTION_COLOR_MAP.put(ColorEnum._green, new RGB(40, 167, 69)); // 绿色
        NOTION_COLOR_MAP.put(ColorEnum._orange, new RGB(255, 165, 0)); // 橙色
        NOTION_COLOR_MAP.put(ColorEnum._pink, new RGB(255, 105, 180)); // 粉色
        NOTION_COLOR_MAP.put(ColorEnum._purple, new RGB(138, 43, 226)); // 紫色
        NOTION_COLOR_MAP.put(ColorEnum._red, new RGB(220, 53, 69)); // 红色
        NOTION_COLOR_MAP.put(ColorEnum._yellow, new RGB(255, 235, 59)); // 黄色
    }

    // 常见CSS命名颜色映射
    private static final Map<String, RGB> NAMED_COLORS = new HashMap<>();

    static {
        // 基础颜色
        NAMED_COLORS.put("red", new RGB(255, 0, 0));
        NAMED_COLORS.put("green", new RGB(0, 128, 0));
        NAMED_COLORS.put("blue", new RGB(0, 0, 255));
        NAMED_COLORS.put("yellow", new RGB(255, 255, 0));
        NAMED_COLORS.put("orange", new RGB(255, 165, 0));
        NAMED_COLORS.put("purple", new RGB(128, 0, 128));
        NAMED_COLORS.put("pink", new RGB(255, 192, 203));
        NAMED_COLORS.put("brown", new RGB(165, 42, 42));
        NAMED_COLORS.put("gray", new RGB(128, 128, 128));
        NAMED_COLORS.put("grey", new RGB(128, 128, 128));
        NAMED_COLORS.put("black", new RGB(0, 0, 0));
        NAMED_COLORS.put("white", new RGB(255, 255, 255));

        // 扩展颜色
        NAMED_COLORS.put("darkred", new RGB(139, 0, 0));
        NAMED_COLORS.put("darkgreen", new RGB(0, 100, 0));
        NAMED_COLORS.put("darkblue", new RGB(0, 0, 139));
        NAMED_COLORS.put("lightblue", new RGB(173, 216, 230));
        NAMED_COLORS.put("lightgreen", new RGB(144, 238, 144));
        NAMED_COLORS.put("lightgray", new RGB(211, 211, 211));
        NAMED_COLORS.put("darkgray", new RGB(169, 169, 169));
    }

    // 正则表达式模式
    private static final Pattern HEX_PATTERN = Pattern.compile("#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})");
    private static final Pattern RGB_PATTERN = Pattern
            .compile("rgb\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)");
    private static final Pattern RGBA_PATTERN = Pattern
            .compile("rgba\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*([0-9]*\\.?[0-9]+)\\s*\\)");

    /**
     * 从CSS样式中解析颜色并映射到Notion颜色
     * 
     * @param cssValue CSS颜色值（支持hex、rgb、rgba、命名颜色）
     * @return 对应的Notion颜色枚举，解析失败时返回null
     */
    public static ColorEnum parseAndMapColor(String cssValue) {
        if (StringUtils.isBlank(cssValue)) {
            return ColorEnum._default;
        }

        String normalizedValue = cssValue.trim().toLowerCase();
        log.debug("解析CSS颜色值: {}", normalizedValue);

        // 解析CSS颜色为RGB
        RGB rgb = parseColorToRGB(normalizedValue);
        if (rgb == null) {
            log.debug("无法解析颜色值: {}", cssValue);
            return ColorEnum._default;
        }

        // 映射到最接近的Notion颜色
        ColorEnum notionColor = mapRGBToNotionColor(rgb);
        log.debug("颜色 {} -> RGB({},{},{}) -> {}", cssValue, rgb.r, rgb.g, rgb.b, notionColor);

        return notionColor;
    }

    /**
     * 解析CSS颜色字符串为RGB对象
     */
    private static RGB parseColorToRGB(String colorValue) {
        // 1. 尝试解析十六进制颜色
        Matcher hexMatcher = HEX_PATTERN.matcher(colorValue);
        if (hexMatcher.find()) {
            return parseHexColor(hexMatcher.group(1));
        }

        // 2. 尝试解析RGB颜色
        Matcher rgbMatcher = RGB_PATTERN.matcher(colorValue);
        if (rgbMatcher.find()) {
            int r = Integer.parseInt(rgbMatcher.group(1));
            int g = Integer.parseInt(rgbMatcher.group(2));
            int b = Integer.parseInt(rgbMatcher.group(3));
            return new RGB(r, g, b);
        }

        // 3. 尝试解析RGBA颜色（忽略透明度）
        Matcher rgbaMatcher = RGBA_PATTERN.matcher(colorValue);
        if (rgbaMatcher.find()) {
            int r = Integer.parseInt(rgbaMatcher.group(1));
            int g = Integer.parseInt(rgbaMatcher.group(2));
            int b = Integer.parseInt(rgbaMatcher.group(3));
            // 透明度太低时返回null，表示近似透明
            double alpha = Double.parseDouble(rgbaMatcher.group(4));
            if (alpha < 0.3) {
                return null;
            }
            return new RGB(r, g, b);
        }

        // 4. 尝试解析命名颜色
        return NAMED_COLORS.get(colorValue);
    }

    /**
     * 解析十六进制颜色
     */
    private static RGB parseHexColor(String hex) {
        try {
            if (hex.length() == 3) {
                // 3位十六进制，如 #f0a -> #ff00aa
                int r = Integer.parseInt(hex.substring(0, 1) + hex.substring(0, 1), 16);
                int g = Integer.parseInt(hex.substring(1, 2) + hex.substring(1, 2), 16);
                int b = Integer.parseInt(hex.substring(2, 3) + hex.substring(2, 3), 16);
                return new RGB(r, g, b);
            } else if (hex.length() == 6) {
                // 6位十六进制，如 #ff00aa
                int r = Integer.parseInt(hex.substring(0, 2), 16);
                int g = Integer.parseInt(hex.substring(2, 4), 16);
                int b = Integer.parseInt(hex.substring(4, 6), 16);
                return new RGB(r, g, b);
            }
        } catch (NumberFormatException e) {
            log.warn("解析十六进制颜色失败: {}", hex, e);
        }
        return null;
    }

    /**
     * 将RGB颜色映射到最接近的Notion颜色
     */
    private static ColorEnum mapRGBToNotionColor(RGB targetRGB) {
        // 特殊情况处理

        // 1. 低饱和度且中等亮度 -> 灰色
        if (targetRGB.getSaturation() < 0.15 && targetRGB.getBrightness() > 0.3 && targetRGB.getBrightness() < 0.8) {
            return ColorEnum._gray;
        }

        // 2. 非常暗的颜色 -> 默认（黑色）
        if (targetRGB.getBrightness() < 0.15) {
            return ColorEnum._default;
        }

        // 3. 非常亮且低饱和度 -> 默认（接近白色）
        if (targetRGB.getBrightness() > 0.9 && targetRGB.getSaturation() < 0.1) {
            return ColorEnum._default;
        }

        // 4. 找到距离最近的Notion颜色
        ColorEnum closestColor = ColorEnum._default;
        double minDistance = Double.MAX_VALUE;

        for (Map.Entry<ColorEnum, RGB> entry : NOTION_COLOR_MAP.entrySet()) {
            double distance = targetRGB.distanceTo(entry.getValue());
            if (distance < minDistance) {
                minDistance = distance;
                closestColor = entry.getKey();
            }
        }

        return closestColor;
    }

    /**
     * 从元素的内联样式中解析颜色
     * 
     * @param style 内联样式字符串
     * @return 解析到的Notion颜色，未找到时返回null
     */
    public static ColorEnum parseColorFromStyle(String style) {
        if (StringUtils.isBlank(style)) {
            return ColorEnum._default;
        }

        String lowerStyle = style.toLowerCase();

        // 查找color属性
        if (lowerStyle.contains("color:")) {
            String colorValue = extractCSSProperty(lowerStyle, "color");
            if (StringUtils.isNotBlank(colorValue)) {
                return parseAndMapColor(colorValue);
            }
        }

        return ColorEnum._default;
    }

    /**
     * 从CSS样式字符串中提取指定属性的值
     */
    private static String extractCSSProperty(String style, String property) {
        try {
            String searchPattern = property + ":";
            int startIndex = style.indexOf(searchPattern);
            if (startIndex == -1) {
                return "";
            }

            // 跳过属性名和冒号
            startIndex += searchPattern.length();

            // 跳过空格
            while (startIndex < style.length() && Character.isWhitespace(style.charAt(startIndex))) {
                startIndex++;
            }

            // 找到值的结束位置
            int endIndex = startIndex;
            while (endIndex < style.length() && style.charAt(endIndex) != ';') {
                endIndex++;
            }

            return style.substring(startIndex, endIndex).trim();
        } catch (Exception e) {
            log.warn("解析CSS属性{}时出错: {}", property, style, e);
            return "";
        }
    }
}