package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("unTagParser")
public class UnTagParser extends AbstractParser {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final Pattern timePattern = Pattern.compile("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}");

    
    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private ConfigQueryApi configQueryApi;
    
    
    @Override
    public boolean supports(String url) {
        return url.contains("utgd.net");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);
        Element element = document.selectFirst("div[class=\"article_body\"]");
        if (element == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "Utag文章解析异常");
        }
        return globalElementParser.parseElement(element);
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.UNTAG.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_UTAG;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_UTAG;
    }

    @Override
    protected String parseAuthor(Document document) {
        Element element = document.selectFirst("p[class=\"author\"]");
        String author = Optional.ofNullable(element).map(e -> e.getElementsByTag("a")).map(Elements::text).orElse(null);
        if (author != null) {
            return author;
        }
        log.error("作者内容解析失败,url={}", document.location());
        return "未知";
    }

    @Override
    protected String parseTitle(Document document) {
        Element element = document.selectFirst("h1[class=\"article_title\"]");
        String title = Optional.ofNullable(element).map(Element::text).orElse(null);
        if (StringUtils.isBlank(title)) {
            return super.parseTitle(document);
        }
        return title;
    }

    @Override
    protected String parsePublishTime(Document document) {
        Element element = document.selectFirst("p[class=\"published_time\"]");
        if (element == null) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        Elements spans = element.getElementsByTag("span");
        if (spans.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        for (Element span : spans) {
            String text = span.text();
            if (StringUtils.isNoneBlank(text) && timePattern.matcher(text.trim()).find()) {
                return LocalDateTime.parse(text.trim(), formatter).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

}
