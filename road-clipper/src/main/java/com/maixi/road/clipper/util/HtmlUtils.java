package com.maixi.road.clipper.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;

/**
 * HTML工具类
 */
public class HtmlUtils {
    
    private HtmlUtils() {
        // 私有构造函数，防止实例化
    }
    
    /**
     * 将HTML元素转换为富文本格式
     * 
     * @param element HTML元素
     * @return 富文本列表
     */
    public static List<Map<String, Object>> convertToRichText(Element element) {
        List<Map<String, Object>> richTexts = new ArrayList<>();
        processNode(element, richTexts, new TextStyle());
        return richTexts;
    }
    
    /**
     * 处理节点
     * 
     * @param node 节点
     * @param richTexts 富文本列表
     * @param style 文本样式
     */
    private static void processNode(Node node, List<Map<String, Object>> richTexts, TextStyle style) {
        if (node instanceof TextNode) {
            String text = ((TextNode) node).text();
            if (!text.isEmpty()) {
                richTexts.add(createRichTextItem(text, style));
            }
        } else if (node instanceof Element) {
            Element element = (Element) node;
            TextStyle newStyle = applyElementStyle(element, style);
            
            // 处理子节点
            for (Node childNode : element.childNodes()) {
                processNode(childNode, richTexts, newStyle);
            }
        }
    }
    
    /**
     * 应用元素样式
     * 
     * @param element 元素
     * @param style 当前样式
     * @return 新样式
     */
    private static TextStyle applyElementStyle(Element element, TextStyle style) {
        TextStyle newStyle = style.clone();
        
        switch (element.tagName().toLowerCase()) {
            case "b":
            case "strong":
                newStyle.bold = true;
                break;
            case "i":
            case "em":
                newStyle.italic = true;
                break;
            case "u":
                newStyle.underline = true;
                break;
            case "s":
            case "strike":
            case "del":
                newStyle.strikethrough = true;
                break;
            case "code":
                newStyle.code = true;
                break;
            case "a":
                newStyle.link = element.attr("href");
                break;
        }
        
        return newStyle;
    }
    
    /**
     * 创建富文本项
     * 
     * @param content 内容
     * @param style 样式
     * @return 富文本项
     */
    private static Map<String, Object> createRichTextItem(String content, TextStyle style) {
        Map<String, Object> richTextItem = new HashMap<>();
        richTextItem.put("type", "text");
        
        Map<String, Object> textContent = new HashMap<>();
        textContent.put("content", content);
        
        if (style.link != null && !style.link.isEmpty()) {
            Map<String, String> linkData = new HashMap<>();
            linkData.put("url", style.link);
            textContent.put("link", linkData);
        }
        
        richTextItem.put("text", textContent);
        
        Map<String, Object> annotations = new HashMap<>();
        annotations.put("bold", style.bold);
        annotations.put("italic", style.italic);
        annotations.put("strikethrough", style.strikethrough);
        annotations.put("underline", style.underline);
        annotations.put("code", style.code);
        annotations.put("color", "default");
        
        richTextItem.put("annotations", annotations);
        
        return richTextItem;
    }
    
    /**
     * 文本样式类
     */
    private static class TextStyle implements Cloneable {
        boolean bold = false;
        boolean italic = false;
        boolean underline = false;
        boolean strikethrough = false;
        boolean code = false;
        String link = null;
        
        @Override
        public TextStyle clone() {
            try {
                return (TextStyle) super.clone();
            } catch (CloneNotSupportedException e) {
                TextStyle clone = new TextStyle();
                clone.bold = this.bold;
                clone.italic = this.italic;
                clone.underline = this.underline;
                clone.strikethrough = this.strikethrough;
                clone.code = this.code;
                clone.link = this.link;
                return clone;
            }
        }
    }
    
    /**
     * 清理HTML内容
     * 
     * @param html HTML内容
     * @return 清理后的HTML内容
     */
    public static String cleanHtml(String html) {
        if (html == null || html.isEmpty()) {
            return "";
        }
        
        // 移除脚本和样式标签
        html = html.replaceAll("<script[^>]*>.*?</script>", "")
                .replaceAll("<style[^>]*>.*?</style>", "")
                .replaceAll("<!--.*?-->", "");
        
        return html;
    }
}
