package com.maixi.road.clipper.service.parser;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.tagresolver.TextTagResolver;
import com.maixi.road.common.core.enums.ThreadLocalKeys;
import com.maixi.road.common.core.utils.ThreadLocalUtils;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.ColorEnum;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.integration.notion.tags.TextTag;
import com.maixi.road.common.integration.notion.tags.TextTagProperty;
import com.maixi.road.common.integration.notion.tags.WrapBlock;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class InlineElementParser {

    @Resource
    private GlobalElementParser globalElementParser;


    public List<Block> parseElement(Element element) {

        // 不确定的tag，需要循环遍历处理，合并属性等操作
        List<WrapBlock> wrapBlocks = resolver(element);

        // 为了防止块膨胀，合并连续的文本块
        List<Block> blocks = combineTextBlocks(wrapBlocks);
        return blocks;
    }

    private static TextTagProperty blueColor() {
        TextTagProperty textTagProperty = new TextTagProperty();
        textTagProperty.setColor(ColorEnum._blue.getColor());
        textTagProperty.setAnnotations(0);
        return textTagProperty;
    }

    private static TextTagProperty defaultStyle() {
        TextTagProperty textTagProperty = new TextTagProperty();
        textTagProperty.setColor(ColorEnum._default.getColor());
        textTagProperty.setAnnotations(0);
        return textTagProperty;
    }

    private List<WrapBlock> resolver(Element element) {
        List<WrapBlock> wrapBlocks = Lists.newArrayList();
        List<String> childNodeNames = element.childNodes().stream().map(Node::nodeName).toList();
        if (childNodeNames.contains("p") || childNodeNames.contains("h2") || childNodeNames.contains("hr") || childNodeNames.contains("img")) {
            for (Node childNode : element.childNodes()) {
                if (childNode instanceof TextNode) {
                    wrapBlocks.add(WrapBlock.wrapTextTag(TextTag.buildTextTag(((TextNode) childNode).text().replaceAll(NotionConstants.UTFSpace, " "), TextTagProperty.defaultProperty())));
                } else if (childNode instanceof Element) {
                    List<Block> blocks = globalElementParser.parseElement((Element)childNode);
                    wrapBlocks.addAll(blocks.stream().map(WrapBlock::wrapBlock).toList());
                } else {
                    log.error("不支持的节点类型:{}", childNode.nodeName());
                }
            }
            return wrapBlocks;
        }

        // 记录当前标签属性
        TextTagProperty currentTextTagProperty = currentTextTag(element);
        if ("br".equals(element.nodeName())) {
            // 如果当前标签是 br，则添加一个换行
            return Collections.singletonList(WrapBlock.wrapTextTag(TextTag.buildTextTag("\n", currentTextTagProperty)));
        }

        for (Node node : element.childNodes()) {
            if (node instanceof TextNode) {
                String text = ((TextNode) node).getWholeText();
                if (StringUtils.isNoneBlank(text)) {
                    String origin = ThreadLocalUtils.get(ThreadLocalKeys.CLIPPER_ORIGIN.getKey());
                    if (StringUtils.isNotBlank(origin) && OriginTypeEnum.RB.getName().equals(origin)) {
                        // 特殊处理下小红书的标签
                        if (text.startsWith("#")) {
                            wrapBlocks.add(WrapBlock.wrapTextTag(TextTag.buildTextTag(text, blueColor())));
                            wrapBlocks.add(WrapBlock.wrapTextTag(TextTag.buildTextTag(" ", defaultStyle())));
                        } else {
                            wrapBlocks.add(WrapBlock.wrapTextTag(TextTag.buildTextTag(text, currentTextTagProperty)));
                        }
                    } else if (StringUtils.isNotBlank(origin) && OriginTypeEnum.DOUBAN_GROUP.getName().equals(origin)) {
                        wrapBlocks.add(WrapBlock.wrapTextTag(TextTag.buildTextTag(text, currentTextTagProperty)));
                    } else {
                        wrapBlocks.add(WrapBlock.wrapTextTag(
                                TextTag.buildTextTag(((TextNode) node).text().replaceAll(NotionConstants.UTFSpace, " "),
                                        currentTextTagProperty)));
                    }
                }
            } else if (node instanceof Element) {
                Element childElement = (Element) node;
                Set<String> childNodeNameSet = node.childNodes().stream().map(Node::nodeName).collect(Collectors.toSet());
                // 当前标签的子标签的子标签还包含一些块级标签，返回到 commonResolver 去处理
                if (childNodeNameSet.contains("div") || childNodeNameSet.contains("p") || childNodeNameSet.contains("section")) {
                    List<Block> blocks = globalElementParser.parseElement(childElement);
                    wrapBlocks.addAll(blocks.stream().map(WrapBlock::wrapBlock).collect(Collectors.toList()));
                } else {
                    List<WrapBlock> childTextTags = resolver(childElement);
                    for (WrapBlock item : childTextTags) {
                        if (Boolean.TRUE.equals(item.getIsTextTag())) {
                            // 父标签属性合并到子标签中
                            TextTagResolver.combineSourceToChild(item.getTextTag(), currentTextTagProperty);
                        }
                        wrapBlocks.add(item);
                    }
                    if ("p".equals(node.nodeName())) {
                        // 如果当前标签是 p，则添加一个换行
                        wrapBlocks.add(WrapBlock.wrapTextTag(TextTag.buildTextTag("\n\n", currentTextTagProperty)));
                    }
                }
            }
        }
        return wrapBlocks;
    }

    private static List<RichText> richTextCombine(List<RichText> richTexts) {
        if (CollectionUtils.isEmpty(richTexts) || richTexts.size() == 1) {
            return richTexts;
        }
        List<RichText> newRichTexts = Lists.newArrayList();
        RichText startRichText = richTexts.getFirst();
        for (int j = 1; j < richTexts.size(); j++) {
            RichText currentRichText = richTexts.get(j);
            if (RichText.compare(startRichText, currentRichText)) {
                startRichText.getText().setContent(startRichText.getText().getContent() + currentRichText.getText().getContent());
            } else {
                newRichTexts.add(RichText.copy(startRichText));
                startRichText = currentRichText;
            }
        }
        newRichTexts.add(startRichText);
        return newRichTexts;
    }

    private static TextTagProperty currentTextTag(Element element) {
        // 记录当前标签属性
        TextTagProperty currentTextTag = new TextTagProperty();
        currentTextTag.setColor(ColorEnum._default.getColor());
        int annotations = TextTagResolver.calculateAnnotations(element, currentTextTag);
        currentTextTag.setAnnotations(annotations);
        return currentTextTag;
    }

    private static List<Block> combineTextBlocks(List<WrapBlock> wrapBlocks) {
        List<Block> blocks = Lists.newArrayList();
        int startOffset = -1;
        int endOffset = -1;
        for (int i = 0; i < wrapBlocks.size(); i++) {
            WrapBlock wrap = wrapBlocks.get(i);
            if (Boolean.TRUE.equals(wrap.getIsBlock())) {
                // 处理合并
                if (endOffset > startOffset) {
                    List<RichText> richTexts = wrapBlocks.subList(startOffset, endOffset).stream().map(WrapBlock::getTextTag).map(TextTag::toRichText).collect(Collectors.toList());
                    List<RichText> newRichTexts = richTextCombine(richTexts);
                    blocks.add(Block.paragraph(RichTexts.build(newRichTexts)));
                    startOffset = -1;
                    endOffset = -1;
                }
                blocks.add(wrap.getBlock());
            } else {
                if (startOffset == -1) {
                    startOffset = i;
                    endOffset = i;
                }
                endOffset++;
            }
        }
        if (startOffset != -1) {
            List<RichText> richTexts = wrapBlocks.subList(startOffset, endOffset).stream().map(WrapBlock::getTextTag).map(TextTag::toRichText).collect(Collectors.toList());
            List<RichText> newRichTexts = richTextCombine(richTexts);
            blocks.add(Block.paragraph(RichTexts.build(newRichTexts)));
        }
        return blocks;
    }

}
