    package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("juejinParser")
public class JuejinParser extends AbstractParser {

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private ConfigQueryApi configQueryApi;

    @Override
    public boolean supports(String url) {
        return url.contains("juejin.cn/post");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);
        Element element = document.getElementById("article-root");
        if (element == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "掘金文章解析异常");
        }
        return globalElementParser.parseElement(document);
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.JUEJIN.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_JUEJIN;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_JUEJIN;
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements elements = document.select("time[class=\"time\"]");
        if (elements.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        LocalDateTime publishTime = Optional.ofNullable(elements.first()).map(e -> e.attr("datetime"))
                .map(e -> LocalDateTime.parse(e, formatter))
                .orElse(LocalDateTime.now());
        return publishTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements elements = document.select("div[class=\"author-name\"]");
        if (elements.isEmpty()) {
            return "";
        }
        return Optional.ofNullable(elements.first()).map(Element::text).orElse("");
    }

    @Override
    protected String parseTitle(Document document) {
        Elements title_elements = document.getElementsByTag("title");
        String title = Optional.ofNullable(title_elements.first()).map(Element::text).orElse(null);
        if (StringUtils.isNoneBlank(title)) {
            return title;
        }

        Elements h1Elements = document.select("h1[class=\"article-title\"]");
        title = Optional.ofNullable(h1Elements.first()).map(Element::text).orElse(null);
        if (title == null) {
            log.error("标题内容解析失败,url={}", document.location());
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "标题内容解析失败");
        }
        return title;
    }

}
