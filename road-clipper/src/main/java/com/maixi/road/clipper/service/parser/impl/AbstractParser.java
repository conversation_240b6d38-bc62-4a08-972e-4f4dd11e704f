package com.maixi.road.clipper.service.parser.impl;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.HttpStatusException;
import org.jsoup.Jsoup;
import org.jsoup.UnsupportedMimeTypeException;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.util.concurrent.UncheckedExecutionException;
import com.maixi.road.clipper.service.parser.ContentParser;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.integration.notion.model.block.Block;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractParser implements ContentParser {

    // 预编译正则表达式，用于匹配标准微信文章链接并提取核心部分
    private static final Pattern WEIXIN_MP_URL_PATTERN = Pattern
        .compile("^(https://mp\\.weixin\\.qq\\.com/s/[A-Za-z0-9_-]{22})");

    // 使用Guava Cache缓存文档
    // 设置最大缓存大小为100，过期时间为10分钟
    private final Cache<String, Document> documentCache = CacheBuilder.newBuilder()
            .maximumSize(10) // 最多缓存10个文档
            .expireAfterWrite(10, TimeUnit.MINUTES) // 写入后10分钟过期
            .build();

    /**
     * 获取指定URL的文档
     * 优先从缓存获取，如果缓存中不存在则从网络获取并缓存
     * 
     * @param url 要获取的URL
     * @return 文档对象
     */
    protected Document getDocument(String url) {
        try {
            // 从缓存获取，如果不存在则调用fetchDocument获取并缓存
            Document doc = documentCache.get(url, () -> fetchDocument(url));
            return doc;
        } catch (UncheckedExecutionException | ExecutionException e) {
            Throwable cause = e.getCause();
            if (cause instanceof ClipperException) {
                throw (ClipperException) cause;
            } else {
                log.error("获取文档缓存异常, url={}, error={}", url, e.getMessage());
                throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "获取文档失败");
            }
        }
    }

    /**
     * 抽象类实现解析基础信息接口，
     * 子类需要实现具体的解析逻辑，例如标题，作者，链接等
     */
    @Override
    public ResolveRS parseBasicInfo(String url, String userId) {
        // 获取文档，这会设置 document 字段
        String processedUrl = url;
        // 使用正则表达式校验并提取标准的微信文章链接
        if (url != null) {
            Matcher matcher = WEIXIN_MP_URL_PATTERN.matcher(url);
            if (matcher.find()) {
                // 如果匹配成功，直接从捕获组1中获取干净的URL
                processedUrl = matcher.group(1);
                if (!url.equals(processedUrl)) {
                    log.info("检测到微信文章链接包含多余参数，已自动移除。处理后链接: {}", processedUrl);
                }
            }
        }
        Document document = getDocument(processedUrl);
        ResolveRS basicResult = new ResolveRS();
        basicResult.setTitle(parseTitle(document));
        basicResult.setAuthor(parseAuthor(document));
        basicResult.setLink(minLengthLink(url, parseLink(document)));
        basicResult.setIcon(parseIcon(document));
        basicResult.setCover(parseCover(document));
        basicResult.setDescription(parseDescription(document));
        basicResult.setSiteName(parseSiteName(document));
        basicResult.setPublishTime(parsePublishTime(document));
        basicResult.setOrigin(parseOrigin(document));
        log.info("解析基础信息, url={}, basicResult={}", url, JSON.toJSONString(basicResult));
        return basicResult;
    }

    protected abstract boolean usePicCloud(String unionId);

    /**
     * 实现网络请求获取Document对象
     * 如果特殊网站需要特殊处理，可以在子类中重写该方法
     *
     * @param url
     * @return Document对象
     */
    protected Document fetchDocument(String url) throws ClipperException {
        try {
            Document document = Jsoup.connect(url)
                    .timeout(5000)
                    .get();
            if (StringUtils.isBlank(document.title()) || "微信公众平台".equals(document.title())) {
                document = Jsoup.connect(url)
                        .timeout(5000)
                        .userAgent(
                                "Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1")
                        .get();
            }
            return document;
        } catch (MalformedURLException malformedURLException) {
            log.error("获取文档失败, Invalid Url. url={}, error={}", url, malformedURLException.getMessage());
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR);
        } catch (HttpStatusException statusException) {
            log.error("获取文档失败, Http Status Error. url={}, error={}", url, statusException.getMessage());
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR);
        } catch (UnsupportedMimeTypeException unsupportedMimeTypeException) {
            log.error("获取文档失败, Unsupported Mime Type. url={}, error={}", url,
                    unsupportedMimeTypeException.getMessage());
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR);
        } catch (SocketTimeoutException socketTimeoutException) {
            log.error("获取文档失败, Socket Timeout. url={}, error={}，将进行重试", url, socketTimeoutException.getMessage());
            // 如果再试一次还是超时，则抛出异常
            try {
                return Jsoup.connect(url)
                        .timeout(5000)
                        .get();
            } catch (Exception e) {
                log.error("再次尝试获取文档失败, Socket Timeout. url={}, error={}", url, e.getMessage());
                throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR);
            }
        } catch (IOException e) {
            log.error("获取文档失败, url={}, error={}", url, e.getMessage());
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR);
        }
    }

    /**
     * 解析标题
     *
     * @param document
     * @return
     */
    protected String parseTitle(Document document) {
        // 社交媒体分享场景的标题，从meta标签中获取
        // 例如：<meta property="og:title" content="标题" />
        // 例如：<meta name="twitter:title" content="标题" />
        Elements elements = document.getElementsByAttributeValue("property", "og:title");
        if (!elements.isEmpty()) {
            String title = elements.first().attr("content");
            if (StringUtils.isNotBlank(title)) {
                return title;
            }
        }
        // 标准方式获取标题
        // 例如：<title>标题</title>
        String title = "未知标题_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return StringUtils.isBlank(document.title()) ? title : document.title();
    }

    /**
     * 解析作者
     *
     * @param document
     * @return
     */
    protected String parseAuthor(Document document) {
        Elements elements = document.getElementsByAttributeValue("property", "author");
        if (!elements.isEmpty()) {
            String author = elements.first().attr("content");
            if (StringUtils.isNotBlank(author)) {
                return author;
            }
        }
        return "未知";
    }

    /**
     * 解析链接
     *
     * @param document
     * @return
     */
    protected String parseLink(Document document) {
        Elements elements = document.getElementsByAttributeValue("property", "og:url");
        if (!elements.isEmpty()) {
            String url = elements.first().attr("content");
            if (StringUtils.isNotBlank(url)) {
                return url;
            }
        }
        // 返回文档的URL，如果文档是从字符串解析而来（而不是从URL获取），则返回空字符串。
        return document.location();
    }

    private String minLengthLink(String url, String parseUrl) {
        if (StringUtils.isBlank(parseUrl)) {
            return url;
        }
        if (url.length() < parseUrl.length()) {
            return url;
        }
        return parseUrl;
    }

    /**
     * 解析图标
     *
     * @param document
     * @return
     */
    protected String parseIcon(Document document) {
        // 不解析图标
        // 直接返回默认图标
        return defaultLogoUrl();
    }

    /**
     * 解析封面
     *
     * @param document
     * @return
     */
    protected String parseCover(Document document) {
        Elements elements = document.getElementsByAttributeValue("property", "og:image");
        if (elements.isEmpty()) {
            elements = document.getElementsByAttributeValue("name", "og:image");
            if (elements.isEmpty()) {
                return defaultHeadImgUrl();
            }
        }
        String imageUrl = elements.first().attr("content");
        if (StringUtils.isBlank(imageUrl)) {
            return defaultHeadImgUrl();
        }
        // 把后面的参数去掉
        String[] split = imageUrl.split("\\?");
        return split[0];
    }

    /**
     * 解析描述
     *
     * @param document
     * @return
     */
    protected String parseDescription(Document document) {
        Elements elements = document.getElementsByAttributeValue("property", "og:description");
        if (!elements.isEmpty()) {
            String description = elements.first().attr("content");
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }
        elements = document.getElementsByAttributeValue("name", "description");
        if (!elements.isEmpty()) {
            String description = elements.first().attr("content");
            if (StringUtils.isNotBlank(description)) {
                return description;
            }
        }
        return null;
    }

    /**
     * 解析网站名称
     *
     * @param document
     * @return
     */
    protected String parseSiteName(Document document) {
        Elements elements = document.getElementsByAttributeValue("property", "og:site_name");
        if (!elements.isEmpty()) {
            String siteName = elements.first().attr("content");
            if (StringUtils.isNotBlank(siteName)) {
                return siteName;
            }
        }
        return null;
    }

    /**
     * 解析来源
     *
     * @param document
     * @return
     */
    protected abstract String parseOrigin(Document document);

    protected abstract String defaultHeadImgUrl();

    protected abstract String defaultLogoUrl();

    /**
     * 解析发布时间
     *
     * @param document
     * @return
     */
    protected abstract String parsePublishTime(Document document);

    /**
     * 解析正文内容的默认实现
     * 子类应该覆盖这个方法实现具体的解析逻辑
     * 
     * @param url    要解析的URL
     * @param userId 用户ID
     * @return 解析结果
     */
    @Override
    public List<Block> parseContent(String url, String userId) {
        // 获取文档，这会设置 document 字段
        getDocument(url);
        // 子类需要实现具体的解析逻辑
        throw new UnsupportedOperationException("子类需要实现 parseContent 方法");
    }

    /**
     * 清理资源的方法
     * 在使用缓存完毕后可以调用此方法
     * 当前实现下没有需要清理的资源，但保留此方法以便子类可以覆盖
     */
    public void cleanup() {
        // 当前实现下没有需要清理的资源
    }
}
