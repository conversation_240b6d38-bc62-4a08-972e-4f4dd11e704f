package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.clipper.service.tagresolver.weixin.MpImgTagResolver;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.core.utils.DateUtils;
import com.maixi.road.common.core.utils.ImageUtil;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Image;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("mpParser")
public class MpParser extends AbstractParser {

    // 预编译正则表达式，用于匹配标准微信文章链接并提取核心部分
    private static final Pattern WEIXIN_MP_URL_PATTERN = Pattern
            .compile("^(https://mp\\.weixin\\.qq\\.com/s/[A-Za-z0-9_-]{22})");

    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private GlobalElementParser glonalElementParser;

    @Override
    public boolean supports(String url) {
        return url.contains("mp.weixin.qq.com")||url.contains("localhost");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        log.info("parseContent, url={}, userId={}", url, userId);
        String processedUrl = url;
        // 使用正则表达式校验并提取标准的微信文章链接
        if (url != null) {
            Matcher matcher = WEIXIN_MP_URL_PATTERN.matcher(url);
            if (matcher.find()) {
                // 如果匹配成功，直接从捕获组1中获取干净的URL
                processedUrl = matcher.group(1);
                if (!url.equals(processedUrl)) {
                    log.info("检测到微信文章链接包含多余参数，已自动移除。处理后链接: {}", processedUrl);
                }
            }
        }
        Document document = getDocument(processedUrl);
        if (document == null) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "document is null");
        }
        Element element = document.getElementById("js_content");
        if (element == null) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "微信文章解析异常");
        }
        // 判断是否是新图文模式
        if (document.getElementById("js_name") == null) {
            Elements elements = document.getElementsByAttributeValue("property", "og:description");
            String description = Optional.ofNullable(elements.first()).map(e -> e.attr("content")).orElse("");
            String[] split = description.split("\\\\x0a");
            List<RichText> richTexts = Lists.newArrayList();
            for (String str : split) {
                richTexts.add(RichText.simpleText(str));
                richTexts.add(RichText.simpleText("\n"));
            }
            Block paragraph = Block.paragraph(RichTexts.build(richTexts));
            List<Block> blocks = handleSwiperItem(document);
            List<Block> blockList = Lists.newArrayList(paragraph);
            blockList.addAll(blocks);
            return blockList;
        }

        List<Block> blockList = glonalElementParser.parseElement(element);
        return blockList;
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return Optional.ofNullable(configQueryApi.queryConfig(unionId))
                .map(UserConfig::getAlwaysUseCloudinary)
                .map(e -> e == 1)
                .orElse(false);
    }

    public List<Block> handleSwiperItem(Document document) {
        Elements scripts = document.select("script");
        List<String> imgUrls = Lists.newArrayList();
        for (Element script : scripts) {
            String html = script.html();
            Matcher matcher = WEIXIN_MP_URL_PATTERN.matcher(html);
            int matcher_start = 0;
            while (matcher.find(matcher_start)) {
                String imgUrl = matcher.group(1);
                String imageType = MpImgTagResolver.analyzerImageType(imgUrl);
                String imageSrc = MpImgTagResolver.buildCorrectImageSrc(imgUrl, imageType);
                String[] split = imageSrc.split("\\?");
                imgUrls.add(split[0]);
                matcher_start = matcher.end();
            }
        }
        return imgUrls.stream().map(url -> Block.image(new Image(ImageUtil.transform2SecureUrl(url))))
                .collect(Collectors.toList());
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements scripts = document.select("script");
        for (Element script : scripts) {
            String html = script.html();
            if (html.contains("var createTime =")) {
                int index = html.indexOf("var createTime = '");
                if (index != -1) {
                    String substring = html.substring(index);
                    String substring1 = substring.substring(0, substring.indexOf("';"));
                    String[] split = substring1.split("'");
                    return DateUtils.formatLocalDateTime(
                            LocalDateTime.parse(split[1], DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                }
            }
        }
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }

    @Override
    protected String parseAuthor(Document document) {
        Element element = document.getElementById("js_name");
        if (element != null && StringUtils.isNoneBlank(element.text())) {
            return element.text();
        }
        Elements followNickname = document.getElementsByClass("wx_follow_nickname");
        if (!followNickname.isEmpty()) {
            return Objects.requireNonNull(followNickname.first()).text();
        }
        return "未知";
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.WX.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_MP;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_MP; // Provide a default logo URL
    }

}
