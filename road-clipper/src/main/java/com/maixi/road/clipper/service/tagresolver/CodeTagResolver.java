package com.maixi.road.clipper.service.tagresolver;

import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Code;
import com.maixi.road.common.integration.notion.model.common.RichText;

import lombok.extern.slf4j.Slf4j;

/**
 * 代码标签解析器，处理 <code>,<samp>,<kbd>,<var> 标签
 * pre 标签要单独处理，因为 pre 不一定是代码块
 */
@Slf4j
@Component
public class CodeTagResolver implements ContentTagResolver {

    public static final List<String> codeTags = List.of("code", "samp", "kbd", "var");

    /**
     * 判断是否为代码标签
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return codeTags.contains(node.nodeName().toLowerCase());
    }

    /**
     * 解析标签为 Block
     * @param element DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        // 解析代码语言类型
        String lang = getCodeLang(element);
        StringBuilder builder = new StringBuilder();
            resolveCode(element, builder);
            String codeStr = builder.toString();
            // if (codeStr.length() > 2000) {
            //     return Collections
            //             .singletonList(Block.callout(Callout.buildTip("内容长度超出 block limit:2000. 请手动补充该部分内容！")));
            // }
            return Collections.singletonList(Block.buildCode(Code.builder()
                    .rich_text(Collections.singletonList(RichText.simpleText(codeStr))).language(lang).build()));
    }

    private static String getCodeLang(Element element) {
        String lang = element.attr("data-lang");
        if (StringUtils.isBlank(lang)) {
            Elements pres = element.getElementsByTag("pre");
            for (Element pre : pres) {
                lang = pre.attr("data-lang");
                if (StringUtils.isNoneBlank(lang)) {
                    break;
                }
            }
        }
        if (!NotionConstants.LANGUAGE_LIST.contains(lang)) {
            lang = "plain text";
        }
        return lang;
    }

    private void resolveCode(Node node,StringBuilder builder) {
        List<Node> nodes = node.childNodes();
        if (nodes.isEmpty()) {
            if (node.nodeName().equals("br")) {
                builder.append("\n");
            } else if (node instanceof TextNode) {
                builder.append(((TextNode) node).text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE).replaceAll("&nbsp;", NotionConstants.SPACE));
            } else {
                builder.append(((Element) node).text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE).replaceAll("&nbsp;", NotionConstants.SPACE));
            }
            return;
        }
        for (Node itemNode : nodes) {
            if (itemNode instanceof TextNode) {
                builder.append(((TextNode) itemNode).text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE).replaceAll("&nbsp;", NotionConstants.SPACE));
            } else if (itemNode instanceof Element) {
                if (!itemNode.childNodes().isEmpty()) {
                    for (Node childNode : itemNode.childNodes()) {
                        resolveCode(childNode, builder);
                    }
                } else {
                    if (itemNode.nodeName().equals("br")) {
                        builder.append("\n");
                    } else {
                        Element nodeElement = (Element) itemNode;
                        builder.append(nodeElement.text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE).replaceAll("&nbsp;", NotionConstants.SPACE));
                    }
                }
            }
        }
    }
}
