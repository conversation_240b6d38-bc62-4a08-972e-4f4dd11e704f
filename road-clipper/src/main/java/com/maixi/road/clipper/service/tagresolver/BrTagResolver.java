package com.maixi.road.clipper.service.tagresolver;
import java.util.List;

import org.jsoup.nodes.Node;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.RichTexts;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class BrTagResolver implements ContentTagResolver {
    /**
     * 判断是否为换行标签
     * @param node DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "br".equalsIgnoreCase(node.nodeName());
    }

    /**
     * 解析换行标签
     * @param node DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        return Lists.newArrayList(Block.paragraph(RichTexts.EMPTY));
    }

}
