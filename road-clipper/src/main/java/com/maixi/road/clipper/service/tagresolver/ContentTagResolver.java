package com.maixi.road.clipper.service.tagresolver;

import java.util.List;

import org.jsoup.nodes.Node;

import com.maixi.road.common.integration.notion.model.block.Block;

/**
 * 内容标签解析器接口，定义标签解析的通用方法
 */
public interface ContentTagResolver {
    
    /**
     * 判断当前解析器是否支持该节点
     * @param element DOM 元素
     * @return 是否支持
     */
    boolean supports(Node element);

    /**
     * 解析节点为 Block
     * @param element DOM 元素
     * @return Block 结构
     */
    List<Block> resolve(Node element);
}
