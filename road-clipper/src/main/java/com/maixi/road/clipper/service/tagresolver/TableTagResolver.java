package com.maixi.road.clipper.service.tagresolver;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.integration.notion.model.block.Table;
import com.maixi.road.common.integration.notion.model.block.TableRow;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.integration.notion.tags.TextTag;

import lombok.extern.slf4j.Slf4j;

@Slf4j
/**
 * 视频标签解析器
 * 解析 <video> 标签为 Notion 的视频块
 */
@Component("tableTagResolver")
public class TableTagResolver implements ContentTagResolver {
    /**
     * 判断是否为表格标签
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "table".equalsIgnoreCase(node.nodeName());
    }

    /**
     * 解析表格标签
     * @param element DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        boolean hasColumnHeader = false;
        int columnCount = 0;
        List<JSONObject> rows = Lists.newArrayList();

        // 如果有表头，处理表头
        Elements head_elements = element.getElementsByTag("thead");
        if (!head_elements.isEmpty()) {
            try {
                JSONObject headRow = thead(Objects.requireNonNull(head_elements.first()));
                hasColumnHeader = true;
                rows.add(headRow);
            } catch (Exception e) {
                log.error("解析 thead 失败", e);
                return Lists.newArrayList(Block.callout(Callout.buildTip(e.getMessage())));
            }
        }

        Elements body_elements = element.getElementsByTag("tbody");
        Element body_element = body_elements.first();
        if (body_element == null) {
            String qid = NanoIdUtils.randomNanoId();
            log.error("处理表格标签时，tbody.first is null. qid={}", qid);
            return Lists.newArrayList(Block.callout(Callout.buildTip("此处疑似丢失表格内容，如确有丢失，请联系开发者处理qid(" + qid + ")")));
        }

        Elements tr_elements = body_element.getElementsByTag("tr");
        if (tr_elements.isEmpty()) {
            String qid = NanoIdUtils.randomNanoId();
            log.error("处理表格标签时，tbody is empty. qid={}", qid);
            return Lists.newArrayList(Block.callout(Callout.buildTip("此处疑似丢失表格内容，如确有丢失，请联系开发者处理qid(" + qid + ")")));
        }

        for (Element tr : tr_elements) {
            Elements th_elements = tr.getElementsByTag("th");
            if (!th_elements.isEmpty()) {
                hasColumnHeader = true;
                rows.add(TableRow.buildRow(th_elements.stream().map(TableTagResolver::resolverThOrTdTag).collect(Collectors.toList())));
                continue;
            }
            Elements td_elements = tr.getElementsByTag("td");
            columnCount = td_elements.size();
            rows.add(TableRow.buildRow(td_elements.stream().map(TableTagResolver::resolverThOrTdTag).collect(Collectors.toList())));
        }
        if (columnCount == 0) {
            return null;
        }
        Table table = Table.builder().table_width(columnCount).has_column_header(hasColumnHeader).has_row_header(false).children(rows).build();
        boolean columnCheck = true;
        for (JSONObject row : table.getChildren()) {
            if (!Objects.equals(row.getJSONObject("table_row").getJSONArray("cells").size(), columnCount)) {
                columnCheck = false;
            }
        }
        if (!columnCheck) {
            String qid = NanoIdUtils.randomNanoId();
            return Lists.newArrayList(Block.callout(Callout.buildTip("疑似存在合并单元格，表格内容无法展现（Notion 暂不支持合并单元格）。qid(" + qid + ")")));
        }
        return Lists.newArrayList(Block.table(table));
    }

    private static JSONObject thead(Element headElement) {
        Elements tr_elements = headElement.getElementsByTag("tr");
        Element tr_element = tr_elements.first();
        if (tr_element == null) {
            String qid = NanoIdUtils.randomNanoId();
            log.error("处理表格标签时，thead.tr.first is null. qid={}", qid);
            throw new RuntimeException("此处疑似丢失表格内容，如确有丢失，请联系开发者处理qid(" + qid + ")");
        }

        Elements th_elements = tr_element.getElementsByTag("th");
        return TableRow.buildRow(th_elements.stream().map(TableTagResolver::resolverThOrTdTag).collect(Collectors.toList()));
    }

    /**
     * 暂时只能处理文本标签
     *
     * @param element th td
     * @return List<RichText>
     */
    public static List<RichText> resolverThOrTdTag(Element element) {
        List<TextTag> textTags = TextTagResolver.resolverTextTag(element);
        return textTags.stream().map(TextTag::toRichText).collect(Collectors.toList());
    }

}
