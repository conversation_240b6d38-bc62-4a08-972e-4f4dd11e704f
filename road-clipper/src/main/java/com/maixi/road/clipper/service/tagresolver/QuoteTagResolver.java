package com.maixi.road.clipper.service.tagresolver;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 引用标签解析器，处理 <blockquote> 标签
 */
@Slf4j
@Component("quoteTagResolver")
public class QuoteTagResolver implements ContentTagResolver {

    private static final List<String> tags = List.of("blockquote", "quote", "q", "cite");

    @Resource
    private GlobalElementParser globalElementParser;

    /**
     * 判断是否为引用标签
     * 
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return tags.contains(node.nodeName().toLowerCase());
    }

    /**
     * 解析引用标签为 Block
     * 
     * @param element DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;

        // 先判断是否存在子元素，如果不存在，则直接获取内容
        if (element.childNodes().isEmpty()) {
            String quoteContent = element.text();
            if (StringUtils.isBlank(quoteContent)) {
                return List.of();
            }
            return List.of(Block.quote(RichTexts.build(List.of(RichText.simpleText(quoteContent)))));
        }

        List<Block> blocks = new ArrayList<>();
        for (Element childElement : element.children()) {
            // 使用 GlobalElementParser 解析子元素
            List<Block> childBlocks = globalElementParser.parseElement(childElement);

            if (childBlocks.isEmpty()) {
                log.debug("Quote 元素没有解析出任何子 block，使用简单文本解析");
                String quoteContent = element.text();
                if (StringUtils.isBlank(quoteContent)) {
                    return List.of();
                }
                blocks.add(Block.quote(RichTexts.build(List.of(RichText.simpleText(quoteContent)))));
            } else {
                blocks.addAll(childBlocks);
            }
        }

        // 将所有 block 的内容合并为一个段落的 RichText 列表
        List<RichText> richTexts = new ArrayList<>();

        for (Block block : blocks) {
            if (block == null) {
                continue;
            }

            // 处理段落类型的 block
            if ("paragraph".equals(block.getType()) && block.getParagraph() != null) {
                List<RichText> paragraphRichTexts = block.getParagraph().getRich_text();
                if (paragraphRichTexts != null && !paragraphRichTexts.isEmpty()) {
                    // 判断是否为空段落（用于换行）
                    boolean isEmpty = paragraphRichTexts.stream()
                            .allMatch(rt -> rt.getText() == null || StringUtils.isBlank(rt.getText().getContent()));

                    if (!isEmpty) {
                        // 添加段落内容，并在结尾加空格
                        richTexts.addAll(paragraphRichTexts);
                        log.info("Quote 中添加段落内容，共 {} 个 RichText", paragraphRichTexts.size());
                    }
                }
            }
            if (richTexts.size() > 0 && !richTexts.get(richTexts.size() - 1).getText().getContent().startsWith("\n")) {
                // 获取最后一个 RichText 的当前内容并添加换行符
                RichText lastRichText = richTexts.get(richTexts.size() - 1);
                String currentContent = lastRichText.getText().getContent();
                lastRichText.getText().setContent(currentContent + "\n");
                log.debug("为 Quote 段落末尾添加换行符");
            }
        }

        // 如果没有收集到任何内容，返回空列表
        if (richTexts.isEmpty()) {
            log.info("Quote 解析后没有有效内容");
            return List.of();
        }

        // 移除最后一个多余的空格（如果存在）
        if (!richTexts.isEmpty()) {
            RichText lastRichText = richTexts.get(richTexts.size() - 1);
            if (lastRichText.getText() != null && " ".equals(lastRichText.getText().getContent())) {
                richTexts.remove(richTexts.size() - 1);
                log.info("移除 Quote 末尾多余的空格");
            }
        }

        // 组装成引用块
        log.info("Quote 解析完成，共生成 {} 个 RichText", richTexts.size());
        return List.of(Block.quote(RichTexts.build(richTexts)));
    }
}
