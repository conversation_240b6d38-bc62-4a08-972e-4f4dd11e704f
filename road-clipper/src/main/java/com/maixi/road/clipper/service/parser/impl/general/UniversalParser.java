package com.maixi.road.clipper.service.parser.impl.general;

import java.util.List;

import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.clipper.service.parser.impl.general.resolver.UniversalAuthorResolver;
import com.maixi.road.clipper.service.parser.impl.general.resolver.UniversalBodyResolver;
import com.maixi.road.clipper.service.parser.impl.general.resolver.UniversalTitleResolver;
import com.maixi.road.common.core.constant.CommonConstants;
import com.maixi.road.common.core.model.dto.PropertyRuleDTO;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.PropertyRuleTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Bookmark;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.service.config.ConfigQueryApi;

import cn.hutool.core.map.MapUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用解析器
 * 用于解析没有特定解析器的网站
 */
@Slf4j
@Component("universalParser")
public class UniversalParser extends AbstractParser {

    @Resource
    private ConfigQueryApi configQueryApi;

    @Resource
    private GlobalElementParser globalElementParser;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public boolean supports(String url) {
        return true;
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);
        RMap<Integer, List<PropertyRuleDTO>> redisMap = redissonClient.getMap(CommonConstants.PROPERTY_RULES_RMAP_KEY);
        List<PropertyRuleDTO> list = redisMap.get(PropertyRuleTypeEnum.ARTICLE.getType());
        if (CollectionUtils.isEmpty(list)) {
            return globalElementParser.parseElement(document);
        }
        Element articleElement = UniversalBodyResolver.estimateCandidateArticleElement(document,list);
        if (articleElement == null) {
            log.error("通用文章解析异常,originUrl={}", url);
            // 其他网站的兜底处理 - 添加提示信息和书签
            Block callOut = Block.callout(Callout.buildInfo("通用网页剪藏还在 Beta 测试中，当前网页剪藏暂时无法成功剪藏，若您有意协助完善优化，请联系开发者进行反馈～"));
            return Lists.newArrayList(callOut, Block.buildBookmark(new Bookmark(url, document.title())));
        }
        return globalElementParser.parseElement(articleElement);
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return document.location();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_DEFAULT;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_DEFAULT;
    }

    /**
     * 默认返回null
     */
    @Override
    protected String parsePublishTime(Document document) {
        return null;
    }

    @Override
    protected String parseAuthor(Document document) {
        RMap<Integer, List<PropertyRuleDTO>> redisMap = redissonClient.getMap(CommonConstants.PROPERTY_RULES_RMAP_KEY);
        if (MapUtil.isEmpty(redisMap)) {
            return "未知";
        }
        List<PropertyRuleDTO> list = redisMap.get(PropertyRuleTypeEnum.AUTHOR.getType());
        if (CollectionUtils.isEmpty(list)) {
            return "未知";
        }
        return UniversalAuthorResolver.parseAuthor(document, list);
    }

    @Override
    protected String parseCover(Document document) {
        return defaultHeadImgUrl();
    }



    @Override
    protected String parseIcon(Document document) {
        return defaultLogoUrl();
    }

    @Override
    protected String parseTitle(Document document) {
        RMap<Integer, List<PropertyRuleDTO>> redisMap = redissonClient.getMap(CommonConstants.PROPERTY_RULES_RMAP_KEY);
        if (MapUtil.isEmpty(redisMap)) {
            return "未知";
        }
        List<PropertyRuleDTO> list = redisMap.get(PropertyRuleTypeEnum.TITLE.getType());
        if (CollectionUtils.isEmpty(list)) {
            return "未知";
        }
        
        return UniversalTitleResolver.parseTitle(document, list);
    }
   
}
