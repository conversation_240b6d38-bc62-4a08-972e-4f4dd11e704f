package com.maixi.road.clipper.image;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.maixi.road.cloudfunc.s3.S3CloudFunctionApi;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.s3proxy.S3ProxyService;

import cn.hutool.core.map.MapUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 图片上传辅助工具类
 * <p>
 * 该类负责处理剪藏过程中的图片处理相关功能，包括：
 * 1. 从 Notion 块中提取图片链接
 * 2. 格式化图片链接（相对路径转绝对路径）
 * 3. 替换图片块中的图片 URL
 * 4. 判断块是否为图片类型
 * </p>
 */
@Slf4j
@Component
public class ImageUploadHelper {

    /**
     * Notion 支持的图片后缀列表
     * 用于验证图片格式是否被 Notion 支持
     */
    private static final List<String> notion_support_img_suffix = Lists.newArrayList(".jpg", ".jpeg", ".png", ".svg",
            ".gif", ".heic", ".bmp", ".tif", ".tiff");

    /**
     * S3 云函数 API，用于图片上传到 S3 存储
     */
    @Resource
    private S3CloudFunctionApi s3CloudFunctionApi;

    /**
     * S3 代理服务，用于处理 S3 相关操作
     */
    @Resource
    private S3ProxyService s3ProxyService;

    /**
     * 配置查询 API，用于获取系统配置
     */
    @Resource
    private ConfigQueryApi configQueryApi;

    /**
     * 从 Notion 块列表中提取图片链接
     * <p>
     * 该方法遍历所有块，找出图片类型的块，并提取其中的图片 URL。
     * 如果图片 URL 是相对路径，会自动补全域名。
     * 如果图片 URL 不合法，会将该块替换为提示信息的 Callout 块。
     * </p>
     *
     * @param resolveVo 解析结果对象，包含站点名称等信息
     * @param blocks    Notion 块列表
     * @return 提取的图片 URL 集合
     */
    @NotNull
    public static Set<String> extractImgList(ResolveRS resolveVo, List<Block> blocks) {
        Set<String> imgList = Sets.newHashSet(); // 使用HashSet存储图片URL，自动去重
        for (int i = 0; i < blocks.size(); i++) {
            Block block = blocks.get(i);
            if (block == null) {
                continue;
            }
            if (block.getType().equals("image")) { // 判断当前块是否为图片类型
                String imgUrl = block.getImage().getExternal().getUrl(); // 获取图片块中的图片URL
                if (imgUrl.startsWith("http")) { // 如果URL已经是绝对路径（以http开头），直接添加到结果集
                    imgList.add(imgUrl);
                } else if (imgUrl.startsWith("/")) { // 如果URL是相对路径（以/开头），需要补全域名
                    imgUrl = resolveVo.getSiteName() + imgUrl; // 使用站点名称补全域名
                    if (!imgUrl.startsWith("http")) { // 确保URL以http开头
                        imgUrl = "http://" + imgUrl;
                    }
                    log.info("图片地址为相对路径，自动补全域名,imgUrl={}, result={}", block.getImage().getExternal().getUrl(), imgUrl);
                    block.getImage().getExternal().setUrl(imgUrl); // 更新图片块中的URL为补全后的URL
                    imgList.add(imgUrl);
                } else { // URL格式不合法（既不是http开头也不是/开头）
                    log.warn("图片地址不合法, link={}, imgUrl={}", resolveVo.getLink(), imgUrl);
                    // 图片无法访问，提示
                    // 把这个 block 的属性改掉
                    // 把这个 block 改成 callout 类型的 block
                    blocks.set(i, Block.callout(Callout.buildInfo("图片无法访问,此处缺失一张图片"))); // 将不合法的图片块替换为提示信息的Callout块
                }
            }
        }
        return imgList;
    }

    /**
     * 判断块是否为图片类型
     *
     * @param block Notion 块对象
     * @return 如果块是图片类型返回 true，否则返回 false
     */
    public static boolean isImageBlock(Block block) {
        return block != null && block.getType().equals("image"); // 判断块是否为图片类型，需要确保块不为null且类型为image
    }

    /**
     * 格式化图片链接，将相对路径转换为绝对路径
     * <p>
     * 该方法将图片链接列表中的相对路径转换为绝对路径。
     * 如果链接以 '/' 开头，会自动补全域名。
     * 如果补全后的 URL 不以 'http' 开头，会自动添加 'http://' 前缀。
     * </p>
     * 
     * @param domain  域名
     * @param imgList 图片链接列表
     * @return 返回处理后URL和原始URL的映射关系，键为处理后URL，值为原始URL
     */
    @NotNull
    public static Map<String, String> formatImgLink(String domain, List<String> imgList) {
        return imgList.stream().collect(Collectors.toMap( // 使用Stream API将图片链接列表转换为映射关系
                // 键为处理后的URL
                e -> {
                    if (e.startsWith("/")) {
                        String url = domain + e; // 将相对路径与域名拼接
                        if (!url.startsWith("http")) {
                            url = "http://" + url;
                        }
                        log.info("图片地址为相对路径，自动补全域名,imgUrl={}, result={}", e, url);
                        return url;
                    }
                    return e;
                },
                // 值为原始URL
                e -> e,
                // 如果有重复键，保留第一个
                (existing, replacement) -> existing)); // 如果有重复键，保留第一个值，避免覆盖
    }

    /**
     * 替换图片块中的图片 URL
     * <p>
     * 该方法根据提供的映射关系，替换解析结果中的封面图片和块列表中的图片 URL。
     * 首先检查封面图片是否需要替换，然后筛选出所有图片类型的块进行处理。
     * </p>
     *
     * @param resolveVo 解析结果对象，包含封面图片等信息
     * @param blocks    Notion 块列表
     * @param resultMap 图片 URL 映射关系，键为原始 URL，值为新 URL
     */
    public static void replaceImageBlock(ResolveRS resolveVo, List<Block> blocks, Map<String, String> resultMap) {
        if (MapUtil.isEmpty(resultMap)) { // 如果映射关系为空，无需处理
            return;
        }
        if (StringUtils.isNotBlank(resultMap.get(resolveVo.getCover()))) { // 检查封面图片是否需要替换
            resolveVo.setCover(resultMap.get(resolveVo.getCover())); // 替换封面图片URL
        }

        // 分离替换逻辑：先筛选出所有图片类型的块
        List<Block> imageBlocks = blocks.stream()
                .filter(ImageUploadHelper::isImageBlock) // 使用isImageBlock方法过滤出图片类型的块
                .toList();

        for (Block block : imageBlocks) {
            // 这里修改的是原 blocks 列表中对应对象的属性
            replaceImageUrl(block, resultMap); // 替换每个图片块中的URL
        }

        long processCount = imageBlocks.size(); // 统计处理的图片数量
        log.info("图片转存成功, processCount={}", processCount); // 记录处理成功的日志
    }

    /**
     * 替换单个图片块中的图片 URL
     * <p>
     * 该方法根据提供的映射关系，替换单个图片块中的图片 URL。
     * 只有当新 URL 格式正确且以 Notion 支持的图片后缀结尾时，才会进行替换。
     * </p>
     *
     * @param block     图片类型的 Notion 块
     * @param resultMap 图片 URL 映射关系，键为原始 URL，值为新 URL
     */
    private static void replaceImageUrl(Block block, Map<String, String> resultMap) {
        String url = block.getImage().getExternal().getUrl(); // 获取原始图片URL
        String newUrl = resultMap.get(url); // 从映射关系中获取新的URL
        if (StringUtils.isNotBlank(newUrl) // 确保新URL不为空
                && newUrl.length() > "http://".length() // 确保新URL长度合理
                && notion_support_img_suffix.stream().anyMatch(newUrl::endsWith)) { // 确保新URL以Notion支持的图片后缀结尾
            block.getImage().getExternal().setUrl(newUrl); // 更新图片块中的URL为新URL
        } else { // 新URL格式不符合要求，不进行替换
            log.warn("转换后的 newUrl 格式不对,不进行替换。newUrl={},originUrl={}", newUrl, block.getImage().getExternal().getUrl()); // 记录警告日志
        }
    }
}
