package com.maixi.road.clipper.service.tagresolver;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.integration.notion.model.block.Code;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 代码标签解析器，处理 <code>,<samp>,<kbd>,<var> 标签
 * pre 标签要单独处理，因为 pre 不一定是代码块
 */
@Slf4j
@Component("preTagResolver")
public class PreTagResolver implements ContentTagResolver {

    @Resource
    private GlobalElementParser globalElementParser;

    /**
     * 判断是否为 pre 标签
     * 
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "pre".equalsIgnoreCase(node.nodeName());
    }

    /**
     * 解析 pre 标签
     * 
     * @param element DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        // code samp 代码块处理
        Block block = singleCodeTag(element);
        if (block != null) {
            return Collections.singletonList(block);
        }

        // 解析代码语言类型
        String lang = getCodeLang(element);

        // 解析代码块
        Elements elements = element.getElementsByTag("code");
        if (elements.isEmpty()) {
            elements = element.getElementsByTag("samp");
        }
        if (elements.isEmpty()) {
            elements = element.getElementsByTag("p");
        }
        if (elements.size() > 1) {
            log.warn("pre 标签中存在多个code块");
            List<RichText> richTexts = elements.stream().map(Element::text)
                    .map(e -> e.replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE))
                    .map(e -> e.replaceAll("&nbsp;", NotionConstants.SPACE))
                    .map(RichText::simpleText)
                    .collect(Collectors.toList());
            for (RichText richText : richTexts) {
                if (!richText.getText().getContent().endsWith("\n")) {
                    richText.getText().setContent(richText.getText().getContent() + "\n");
                }
            }
            return Collections
                    .singletonList(Block.buildCode(Code.builder().rich_text(richTexts).language(lang).build()));
        } else if (elements.size() == 1) {
            StringBuilder builder = new StringBuilder();
            resolveCode(element, builder);
            String codeStr = builder.toString();
            return Collections.singletonList(Block.buildCode(Code.builder()
                    .rich_text(Collections.singletonList(RichText.simpleText(codeStr))).language(lang).build()));
        } else {
            // pre 标签中没有code块
            List<Block> blocks = new ArrayList<>();
            for (Node childNode : element.childNodes()) {
                if (childNode instanceof TextNode) {
                    blocks.add(Block.paragraph(RichTexts
                            .build(Collections.singletonList(RichText.simpleText(((TextNode) childNode).text())))));
                } else if (childNode instanceof Element) {
                    List<Block> blockList = globalElementParser.parseElement((Element) childNode);
                    blocks.addAll(blockList);
                }
            }
            // 获取到了内容，则返回，否则走兜底提示
            if (!blocks.isEmpty()) {
                return blocks;
            }
        }

        String qid = NanoIdUtils.randomNanoId();
        log.error("pre 标签中没有code块,qid={}", qid);
        return Collections.singletonList(Block.callout(Callout.buildTip("此处疑似丢失内容块，如确有丢失烦请联系开发者优化qid(" + qid + ")")));
    }

    private static String getCodeLang(Element element) {
        String lang = element.attr("data-lang");
        if (StringUtils.isBlank(lang)) {
            Elements pres = element.getElementsByTag("pre");
            for (Element pre : pres) {
                lang = pre.attr("data-lang");
                if (StringUtils.isNoneBlank(lang)) {
                    break;
                }
            }
        }
        if (!NotionConstants.LANGUAGE_LIST.contains(lang)) {
            lang = "plain text";
        }
        return lang;
    }

    private Block singleCodeTag(Element element) {
        if (element.nodeName().equals("code") || element.nodeName().equals("samp")) {
            StringBuilder builder = new StringBuilder();
            resolveCode(element, builder);
            return Block.buildCode(Code.builder()
                    .rich_text(Collections.singletonList(RichText.simpleText(builder.toString())))
                    .language("plain text")
                    .build());
        }
        return null;
    }

    private void resolveCode(Node node, StringBuilder builder) {
        List<Node> nodes = node.childNodes();
        if (nodes.isEmpty()) {
            if (node.nodeName().equals("br")) {
                builder.append("\n");
            } else if (node instanceof TextNode) {
                builder.append(((TextNode) node).text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE)
                        .replaceAll("&nbsp;", NotionConstants.SPACE));
            } else {
                builder.append(((Element) node).text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE)
                        .replaceAll("&nbsp;", NotionConstants.SPACE));
            }
            return;
        }
        for (Node itemNode : nodes) {
            if (itemNode instanceof TextNode) {
                builder.append(((TextNode) itemNode).text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE)
                        .replaceAll("&nbsp;", NotionConstants.SPACE));
            } else if (itemNode instanceof Element) {
                if (!itemNode.childNodes().isEmpty()) {
                    for (Node childNode : itemNode.childNodes()) {
                        resolveCode(childNode, builder);
                    }
                } else {
                    if (itemNode.nodeName().equals("br")) {
                        builder.append("\n");
                    } else {
                        Element nodeElement = (Element) itemNode;
                        builder.append(nodeElement.text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE)
                                .replaceAll("&nbsp;", NotionConstants.SPACE));
                    }
                }
            }
        }
    }

}
