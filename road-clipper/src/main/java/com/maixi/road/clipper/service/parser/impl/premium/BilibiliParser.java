package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.clipper.service.tagresolver.ImageTagResolver;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Bookmark;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("bilibiliParser")
public class BilibiliParser extends AbstractParser {

    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private ImageTagResolver imgTagResolver;

    @Override
    public boolean supports(String url) {
        return url.contains("bilibili.com/video") || url.contains("b23.tv");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);
        return Lists.newArrayList(Block.buildBookmark(new Bookmark(url, parseTitle(document))));
    }
    
    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.BILIBILI.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_BILIBILI;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_BILIBILI;
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements elements = document.select("div[class=\"pubdate-ip-text\"]");
        if (elements.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        String publicTime = Optional.ofNullable(elements.first()).map(Element::text).orElse(null);
        if (StringUtils.isBlank(publicTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return publicTime;
    }

    @Override
    protected String parseCover(Document document) {
        Elements elements = document.select("meta[property=\"og:image\"]");
        String imageUrl = Optional.ofNullable(elements.first()).map(e -> e.attr("content")).orElse(null);
        if (StringUtils.isBlank(imageUrl)) {
            return defaultHeadImgUrl();
        }
        imageUrl = imageUrl.replace("//", "https://");
        String okUrl = imageUrl.substring(0, imageUrl.indexOf("@"));
        return okUrl;
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements elements = document.select("meta[name=\"author\"]");
        if (elements.isEmpty()) {
            return "";
        }
        Optional<String> authorOpt = elements.stream()
                .filter(element -> element.hasAttr("content"))
                .findAny()
                .map(e -> e.attr("content"));
        return authorOpt.orElse("未知");
    }

}
