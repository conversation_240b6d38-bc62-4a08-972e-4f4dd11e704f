package com.maixi.road.clipper.service.parser;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.tagresolver.ContentTagResolver;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class GlobalElementParser {

    @Resource
    private InlineElementParser inlineElementParser;

    @Resource
    private Map<String, ContentTagResolver> tagResolverMap;

    public List<Block> parseElement(Element element) {
        // 先处理当前 element 元素
        List<Block> specialTagResolverList = tagResolverMap.values().stream()
                .filter(e -> e.supports(element))
                .findFirst()
                .map(e -> e.resolve(element))
                .orElse(null);
        if (specialTagResolverList != null) {
            return specialTagResolverList;
        }
        // 如果当前元素不是特殊标签，遍历处理子节点
        List<Block> blockList = Lists.newArrayList();
        for (Node child : element.childNodes()) {
            // 确定标签的解析逻辑
            specialTagResolverList = tagResolverMap.values().stream()
                    .filter(e -> e.supports(child))
                    .findFirst()
                    .map(e -> e.resolve(child))
                    .orElse(null);
            if (specialTagResolverList != null) {
                blockList.addAll(specialTagResolverList);
            } else {
                // 不确定标签的解析逻辑
                if (child instanceof Element) {
                    List<Node> childNodes = child.childNodes();
                    // 含有效文本元素（非空串）
                    long textNodeCount = childNodes.stream()
                            .filter(TextNode.class::isInstance)
                            .filter(e -> StringUtils.isNoneBlank(((TextNode) (e)).text()))
                            .count();
                    if (textNodeCount > 0) {
                        blockList.addAll(inlineElementParser.parseElement((Element) child));
                    } else {
                        Element inlineElement = ((Element) child).children().stream().filter(e -> !e.isBlock())
                                .findAny().orElse(null);
                        if (inlineElement == null) {
                            blockList.addAll(parseElement((Element) child));
                        } else {
                            blockList.addAll(inlineElementParser.parseElement((Element) child));
                        }
                    }
                } else {
                    log.info("一般不会走到这里，除非是正文根节点下直接有文本节点 nodeName:{} ", element.nodeName());
                    if (child instanceof TextNode textNode) {
                        blockList.add(Block
                                .paragraph(RichTexts.build(List.of(RichText.simpleText(textNode.getWholeText())))));
                    }
                }
            }
        }
        // 如果 richText 中只包含空格或换行符，则将其内容替换为单个换行符
        blockList.forEach(block -> {
            if (block.getParagraph() != null) {
                List<RichText> richTextList = block.getParagraph().getRich_text();
                if (!CollectionUtils.isEmpty(richTextList)) {
                    richTextList.forEach(this::normalizeWhitespaceRichText);
                }
            }
        });
        // 如果 block 中的 content 只包含空格或换行符，则删除这个 block
        blockList.removeIf(e -> e.getParagraph() != null
                && !CollectionUtils.isEmpty(e.getParagraph().getRich_text())
                && StringUtils.isBlank(e.getParagraph().getRich_text().stream()
                        .map(RichText::getPlain_text).collect(Collectors.joining())));
        // 找出顺序项 block，如果顺序项 block 的内容是空的，则删除这个 block
        blockList.removeIf(e -> e.getNumbered_list_item() != null
                && CollectionUtils.isEmpty(e.getNumbered_list_item().getRich_text()));
        // 找出列表项 block，如果列表项 block 的内容是空的，则删除这个 block
        blockList.removeIf(e -> e.getBulleted_list_item() != null
                && CollectionUtils.isEmpty(e.getBulleted_list_item().getRich_text()));
        return blockList;
    }

    /**
     * 如果 richText 中只包含空格或换行符，则将其内容替换为单个换行符
     *
     * @param richText a RichText object
     */
    private void normalizeWhitespaceRichText(RichText richText) {
        if (richText != null && richText.getText() != null) {
            String plainText = richText.getPlain_text();
            if (StringUtils.isNotBlank(plainText) && StringUtils.isBlank(plainText)) {
                richText.setPlain_text("\n");
                richText.getText().setContent("\n");
            }
        }
    }

}
