package com.maixi.road.clipper.service.tagresolver;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.model.block.Audio;
import com.maixi.road.common.integration.notion.model.block.Block;

import lombok.extern.slf4j.Slf4j;

import java.util.Collections;

/**
 * 音频标签解析器，处理 <audio> 标签
 */
@Slf4j
@Component
public class AudioTagResolver implements ContentTagResolver {
    /**
     * 判断是否为音频标签
     * 
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node element) {
        return "audio".equalsIgnoreCase(element.nodeName());
    }

    /**
     * 解析音频标签为 Block
     * 
     * @param element DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        // 获取音频地址
        String src = element.attr("src");
        if (StringUtils.isBlank(src)) {
            // 判断是否有 source 子标签
            Element sourceElement = element.selectFirst("source");
            if (sourceElement != null) {
                src = sourceElement.attr("src");
            }
            if (StringUtils.isBlank(src)) {
                return Collections.emptyList();
            }
        }
        // 组装成audio-block
        return Lists.newArrayList(Block.audio(new Audio(src)));
    }
}
