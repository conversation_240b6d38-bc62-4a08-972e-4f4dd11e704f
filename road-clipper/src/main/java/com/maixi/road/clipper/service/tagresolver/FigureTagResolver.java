package com.maixi.road.clipper.service.tagresolver;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Code;
import com.maixi.road.common.integration.notion.model.common.RichText;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class FigureTagResolver implements ContentTagResolver {

    @Resource
    private ImageTagResolver imageTagResolver;
    @Resource
    private VideoTagResolver videoTagResolver;
    @Resource
    private AudioTagResolver audioTagResolver;
    @Resource
    private CodeTagResolver codeTagResolver;
    @Resource
    private TableTagResolver tableTagResolver;
    /**
     * 判断是否为figure标签
     * 
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "figure".equalsIgnoreCase(node.nodeName());
    }

    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        // 判断 figure 标签内包含的是什么元素。
        boolean img = !element.select("img").isEmpty();
        boolean video = !element.select("video").isEmpty();
        boolean audio = !element.select("audio").isEmpty();
        boolean code = !element.select("code").isEmpty();
        boolean table = !element.getElementsByTag("table").isEmpty();

        if (img) {
            // 处理图片
            return processImage(element);
        } else if (video) {
            // 处理视频
            return processVideo(element);
        } else if (audio) {
            // 处理音频
            return processAudio(element);
        } else if (code) {
            // 处理代码块
            return processCode(element);
        } else if (table) {
            // 处理代码块
            return processTable(element);
        } else {
            // 处理其他情况
            log.warn("Unsupported figure tag content: {}", element);
            return null;
        }
    }

    private List<Block> processCode(Element element) {
        // code samp 代码块处理
        Block block = singleCodeTag(element);
        if (block != null) {
            return Collections.singletonList(block);
        }

        // 解析代码语言类型
        String lang = getCodeLang(element);

        // 解析代码块
        Elements elements = element.getElementsByTag("code");
        if (elements.isEmpty()) {
            elements = element.getElementsByTag("samp");
        }
        if (elements.isEmpty()) {
            elements = element.getElementsByTag("p");
        }
        if (elements.size() > 1) {
            log.warn("pre 标签中存在多个code块");
            List<RichText> richTexts = elements.stream().map(Element::text)
                    .map(e -> e.replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE))
                    .map(e -> e.replaceAll("&nbsp;", NotionConstants.SPACE))
                    .map(RichText::simpleText)
                    .collect(Collectors.toList());
            for (RichText richText : richTexts) {
                if (!richText.getText().getContent().endsWith("\n")) {
                    richText.getText().setContent(richText.getText().getContent() + "\n");
                }
            }
            return Collections
                    .singletonList(Block.buildCode(Code.builder().rich_text(richTexts).language(lang).build()));
        } else if (elements.size() == 1) {
            StringBuilder builder = new StringBuilder();
            resolveCode(element, builder);
            String codeStr = builder.toString();
            return Collections.singletonList(Block.buildCode(Code.builder()
                    .rich_text(Collections.singletonList(RichText.simpleText(codeStr))).language(lang).build()));
        }

        // Return an empty list if no conditions are met
        return Collections.emptyList();
    }

    /**
     * 处理音频,可能会有多个音频
     * 
     * @param element DOM 元素
     * @return Block 结构
     */
    private List<Block> processAudio(Element element) {
        List<Element> audioElements = element.select("audio");
        return audioElements.stream()
                            .flatMap(e -> audioTagResolver.resolve(e).stream())
                            .collect(Collectors.toList());
    }

    /**
     * 处理视频,可能会有多个视频
     * 
     * @param element DOM 元素
     * @return Block 结构
     */
    private List<Block> processVideo(Element element) {
        List<Element> videoElements = element.select("video");
        return videoElements.stream()
                            .flatMap(e -> videoTagResolver.resolve(e).stream())
                            .collect(Collectors.toList());
    }

    /**
     * 处理图片,可能会有多张图片
     * 
     * @param element DOM 元素
     * @return Block 结构
     */
    private List<Block> processImage(Element element) {
        // 找出 element 内部所有的图片元素
        List<Element> imgElements = element.select("img");
        return imgElements.stream()
                          .flatMap(e -> imageTagResolver.resolve(e).stream())
                          .collect(Collectors.toList());
    }

    private List<Block> processTable(Element element) {

        return Lists.newArrayList();
    }


    private static String getCodeLang(Element element) {
        String lang = element.attr("data-lang");
        if (StringUtils.isBlank(lang)) {
            Elements pres = element.getElementsByTag("pre");
            for (Element pre : pres) {
                lang = pre.attr("data-lang");
                if (StringUtils.isNoneBlank(lang)) {
                    break;
                }
            }
        }
        if (!NotionConstants.LANGUAGE_LIST.contains(lang)) {
            lang = "plain text";
        }
        return lang;
    }

    private Block singleCodeTag(Element element) {
        if (element.nodeName().equals("code") || element.nodeName().equals("samp")) {
            StringBuilder builder = new StringBuilder();
            resolveCode(element,builder);
            return Block.buildCode(Code.builder()
                    .rich_text(Collections.singletonList(RichText.simpleText(builder.toString())))
                    .language("plain text")
                    .build());
        }
        return null;
    }

    private void resolveCode(Node node,StringBuilder builder) {
        List<Node> nodes = node.childNodes();
        if (nodes.isEmpty()) {
            if (node.nodeName().equals("br")) {
                builder.append("\n");
            } else if (node instanceof TextNode) {
                builder.append(((TextNode) node).text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE).replaceAll("&nbsp;", NotionConstants.SPACE));
            } else {
                builder.append(((Element) node).text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE).replaceAll("&nbsp;", NotionConstants.SPACE));
            }
            return;
        }
        for (Node itemNode : nodes) {
            if (itemNode instanceof TextNode) {
                builder.append(((TextNode) itemNode).text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE).replaceAll("&nbsp;", NotionConstants.SPACE));
            } else if (itemNode instanceof Element) {
                if (!itemNode.childNodes().isEmpty()) {
                    for (Node childNode : itemNode.childNodes()) {
                        resolveCode(childNode, builder);
                    }
                } else {
                    if (itemNode.nodeName().equals("br")) {
                        builder.append("\n");
                    } else {
                        Element nodeElement = (Element) itemNode;
                        builder.append(nodeElement.text().replaceAll(NotionConstants.UTFSpace, NotionConstants.SPACE).replaceAll("&nbsp;", NotionConstants.SPACE));
                    }
                }
            }
        }
    }

}
