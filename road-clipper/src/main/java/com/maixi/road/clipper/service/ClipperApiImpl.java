package com.maixi.road.clipper.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.maixi.road.clipper.ClipperApi;
import com.maixi.road.clipper.output.helper.MessageBuildHelper;
import com.maixi.road.clipper.service.core.ClipperCoreService;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.core.model.dto.ClipperResponse;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.dto.TextMessageRequest;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Bookmark;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.integration.notion.model.page.Page;
import com.maixi.road.common.integration.notion.model.page.Parent;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.markdown.dto.MarkdownSaveRQ;
import com.maixi.road.markdown.dto.MarkdownSaveRS;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.NotionClient;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 剪藏API实现
 */
@Slf4j
@Service
public class ClipperApiImpl implements ClipperApi {

    @Resource
    private ClipperCoreService clipperService;
    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private NotionClient notionClient;
    @Resource
    private MarkdownOutputService markdownOutputService;
    private static final Pattern URL_PATTERN = Pattern.compile("(https?://[^\\s]+)");
    private static final ExecutorService EXECUTOR = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 处理来自小程序的URL解析请求
     * <p>
     * 解析URL内容
     *
     * @param url    要解析的URL
     * @param userId 用户ID
     * @return 解析结果
     */
    @Override
    public ClipperResponse<ResolveRS> parseUrl(String url, String userId) {
        try {
            ResolveRS result = clipperService.parseUrl(url, userId);
            return ClipperResponse.success(result);
        } catch (ClipperException e) {
            log.error("解析URL失败, url={}, userId={}, error={}", url, userId, e.getMessage());
            return ClipperResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("解析URL异常, url={}, userId={}", url, userId, e);
            return ClipperResponse.fail("解析失败: " + e.getMessage());
        }
    }

    /**
     * 处理来自收藏助手的 Link 消息
     *
     * @param unionId 用户ID
     * @param array   标签数组
     * @param linkUrl 链接URL
     */
    @Override
    public void parseUrlThenSave(String unionId, String[] tags, String link) {
        // 校验参数不能为空
        try {
            // 解析链接
            ResolveRS result = clipperService.parseUrl(link, unionId);

            // 构建文章请求
            ResolveFormRQ rq = new ResolveFormRQ(result);

            log.info("解析链接成功, \nlink={}, \nuserId={}，\nresult={}", link, unionId, JSON.toJSONString(rq));

            // 添加标签
            List<String> tagList = new ArrayList<>();
            if (tags != null && tags.length > 0) {
                tagList.addAll(Arrays.asList(tags));
            }
            rq.setTags(tagList);

            Thread.sleep(10000);
            // 提交文章
            clipperService.saveContent(rq, unionId);
        } catch (Exception e) {
            log.error("解析链接失败, link={}, userId={}", link, unionId, e);
            List<Block> errorBlocks = getBookmarkBlockWrapper(link, e);
            fallbackOutput(link, tags, unionId, errorBlocks);

        }
    }

    @Override
    public ClipperResponse<Boolean> saveContent(ResolveFormRQ article, String userId) {
        try {
            boolean result = clipperService.saveContent(article, userId);
            return ClipperResponse.success(result);
        } catch (ClipperException e) {
            log.error("提交文章失败, title={}, link={}, userId={}, error={}",
                    article.getTitle(), article.getLink(), userId, e.getMessage());
            return ClipperResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("提交文章异常, title={}, link={}, userId={}",
                    article.getTitle(), article.getLink(), userId, e);
            return ClipperResponse.fail("提交失败: " + e.getMessage());
        }
    }

    @Override
    public ClipperResponse<String> saveText(TextMessageRequest message, String userId) {
        if (message == null || StringUtils.isBlank(message.getContent())) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "消息内容不能为空");
        }
        // 将List<String>类型的tags转换为字符串数组（提前转换，避免重复代码）
        // 处理可能为null的tags列表，安全地转换为字符串数组
        final String[] tags = message.getTags() != null ? message.getTags().toArray(new String[0]) : new String[0];

        // 尝试从文本中提取链接
        String link = extractLink(message.getContent());
        if (StringUtils.isNotBlank(link)) {
            // 异步解析链接
            EXECUTOR.submit(() -> {
                try {
                    parseUrlThenSave(link, tags, userId);
                } catch (Exception e) {
                    log.error("异步解析链接失败, link={}, userId={}", link, userId, e);
                }
            });
        }

        // 保存消息到核心服务
        String saveResult = clipperService.saveTextMessage(message.getContent(), tags, userId);
        if (saveResult.contains("失败")) {
            return ClipperResponse.fail(ClipperErrCodeEnum.CLIPPER_ERROR.getCode(), saveResult);
        }
        return ClipperResponse.success(saveResult);
    }

    private void fallbackOutput(String link, String[] tags, String userId, List<Block> errorBlocks) {
        // 查询 user 的保存方式
        UserConfig userConfig = configQueryApi.queryConfig(userId);
        String outputType = userConfig.getOutputType();
        if ("notion".equalsIgnoreCase(outputType) || "both".equalsIgnoreCase(outputType)
                || StringUtils.isBlank(outputType)) {
            MessageFieldDTO userMessage = configQueryApi.getMessageFieldDTO(userId);
            // 保存到Notion
            String title = "温馨提示-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            Page page = Page.builder()
                    .parent(Parent.build(userMessage.getDatabaseId()))
                    .properties(MessageBuildHelper.propertiesBuild(userMessage, title, MsgTypeEnum.TEXT, tags))
                    .children(errorBlocks)
                    .build();
            try {
                notionClient.createPageWithRetry(userMessage.getAccessToken(), page, userId);
            } catch (InterruptedException er) {
                log.error("保存失败提示消息到Notion失败, link={}, userId={}", link, userId, er);
            }
        } else {
            // 保存到Markdown
            String savePath = userConfig.getMarkdownSavePath();

            // 构建 ResolveFormRQ 对象
            ResolveFormRQ userForm = new ResolveFormRQ();
            userForm.setTitle("温馨提示-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
            userForm.setCategory(MsgTypeEnum.TEXT.name());
            // 如果有标签，设置标签
            if (tags != null && tags.length > 0) {
                userForm.setTags(Arrays.asList(tags));
            }
            userForm.setBlocks(errorBlocks);

            // 构建 MarkdownSaveRQ 请求对象
            MarkdownSaveRQ request = MarkdownSaveRQ.builder()
                    .unionId(userId)
                    .userForm(userForm)
                    .savePath(savePath)
                    .fileName(userForm.getTitle())
                    .createAssetsDir(false)
                    .downloadImages(false)
                    .includeMetadata(true)
                    .obConfig(userConfig.getObConfig())
                    .isArticle(false)
                    .build();

            // 调用 MarkdownOutputService 保存 Markdown 文件
            Result<MarkdownSaveRS> result = markdownOutputService.saveArticle(request);
            log.info("保存失败提示消息到Markdown,  markdownSaveResult={}", result);
        }
    }

    private List<Block> getBookmarkBlockWrapper(String link, Exception e) {
        List<Block> errorBlocks = Lists.newArrayList();
        // 创建错误信息的Callout Block
        Callout calloutBlock = Callout.buildTip(e.getMessage());
        Block calloutBlockWrapper = Block.callout(calloutBlock);
        errorBlocks.add(calloutBlockWrapper);

        // 创建原文链接的Bookmark Block
        Bookmark bookmarkBlock = new Bookmark(link, link);
        Block bookmarkBlockWrapper = Block.buildBookmark(bookmarkBlock);
        errorBlocks.add(bookmarkBlockWrapper);
        return errorBlocks;
    }

    /**
     * 从文本中提取链接
     * 
     * @param text 文本
     * @return 链接
     */
    private String extractLink(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }

        Matcher matcher = URL_PATTERN.matcher(text);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

}
