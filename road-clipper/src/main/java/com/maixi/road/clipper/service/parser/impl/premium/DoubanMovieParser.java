package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.clipper.service.tagresolver.ImageTagResolver;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("doubanMovieParser")
public class DoubanMovieParser extends AbstractParser {

    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private ImageTagResolver imgTagResolver;

    @Override
    public boolean supports(String url) {
        return url.contains("movie.douban.com/review");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);
        Elements elements = document.select("div[class=\"main-bd\"]");
        Element element = elements.first();
        if (element == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "豆瓣电影解析异常");
        }
        List<Block> blocks = globalElementParser.parseElement(element);
        return blocks;
    }
    
    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.DOUBAN_MOVIE.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_DOUBAN;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_DOUBAN;
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements elements = document.select("div[class=\"main-meta\"]");
        if (elements.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        Element first = elements.first();
        if (first == null) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        Elements span = first.getElementsByTag("span");
        if (span.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        String publicTime = span.text();
        if (StringUtils.isBlank(publicTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return publicTime.trim();
    }

    @Override
    protected String parseTitle(Document document) {
        Elements elements = document.getElementsByTag("h1");
        if (elements.isEmpty()) {
            return "豆瓣电影-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return Optional.ofNullable(elements.first()).map(Element::text).orElse(null);
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements header = document.getElementsByTag("header");
        if (header.hasClass("main-hd")) {
            Elements links = header.select("a");
            if (!links.isEmpty()) {
                Element firstLink = links.first();
                if (firstLink != null) {
                    String text = firstLink.text();
                    if (StringUtils.isNotBlank(text)) {
                        return text;
                    }
                }
            }
        }
        return "未知";
    }

}
