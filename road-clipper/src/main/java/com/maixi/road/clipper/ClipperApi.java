package com.maixi.road.clipper;

import com.maixi.road.common.core.model.dto.ClipperResponse;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.core.model.dto.TextMessageRequest;

/**
 * 剪藏服务API接口
 * 
 * <p>
 * 该接口提供了完整的内容剪藏服务功能，包括：
 * </p>
 * <ul>
 * <li>URL内容解析与提取</li>
 * <li>文章内容的保存与管理</li>
 * <li>文本消息的同步处理</li>
 * <li>支持用户身份识别和权限控制</li>
 * </ul>
 * 
 * <p>
 * 所有操作都支持用户级别的隔离，确保数据安全性。
 * </p>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ClipperApi {

    /**
     * 解析URL内容
     * 
     * <p>
     * 从指定的URL中提取并解析网页内容，包括标题、正文、图片等信息。
     * 解析结果会返回结构化的数据，便于后续处理和展示。
     * </p>
     * 
     * @param url    要解析的URL地址，必须是有效的HTTP/HTTPS链接
     * @param userId 用户ID，用于权限验证和数据隔离
     * @return 包含解析结果的响应对象，解析成功时包含网页的结构化数据
     */
    ClipperResponse<ResolveRS> parseUrl(String url, String userId);

    /**
     * 解析链接并保存内容
     * 
     * <p>
     * 解析指定链接的内容并自动保存到用户的剪藏库中。
     * 支持为内容添加标签，便于后续的分类和检索。
     * </p>
     * 
     * <p>
     * 该方法为异步操作，会在后台完成内容解析和保存。
     * </p>
     * 
     * @param unionId 用户唯一标识ID，用于数据隔离和权限控制
     * @param array   内容标签数组，可以为空，用于内容分类
     * @param linkUrl 要解析和保存的链接URL
     */
    void parseUrlThenSave(String unionId, String[] array, String linkUrl);

    /**
     * 提交并保存文章内容
     * 
     * <p>
     * 将用户提交的文章内容保存到剪藏库中。
     * 支持富文本内容，包括格式化文本、图片、链接等。
     * </p>
     * 
     * @param article 文章内容参数对象，包含标题、正文、标签等信息
     * @param unionId 用户唯一标识ID，用于数据隔离和权限控制
     * @return 包含保存结果的响应对象，成功时返回true
     */
    ClipperResponse<Boolean> saveContent(ResolveFormRQ article, String unionId);

    /**
     * 同步并保存文本消息
     * 
     * <p>
     * 处理和保存来自各种渠道的文本消息，如微信、邮件等。
     * 支持消息的格式化处理和自动分类。
     * </p>
     * 
     * @param message 文本消息请求对象，包含消息内容、来源等信息
     * @param unionId 用户唯一标识ID，用于数据隔离和权限控制
     * @return 包含处理结果的响应对象，成功时返回处理状态信息
     */
    ClipperResponse<String> saveText(TextMessageRequest message, String unionId);

}
