package com.maixi.road.clipper.image;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.maixi.road.cloudfunc.s3.S3CloudFunctionApi;
import com.maixi.road.cloudfunc.s3.dto.S3CloudRQ;
import com.maixi.road.cloudfunc.s3.dto.S3CloudRS;
import com.maixi.road.common.core.utils.UrlUtils;
import com.maixi.road.common.integration.s3.config.S3Config;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.s3proxy.S3ProxyService;
import com.maixi.road.s3proxy.dto.UploadResult;

import cn.hutool.core.map.MapUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 图片处理服务实现
 * 负责将外部图片下载并上传到用户配置的S3存储中
 * 支持单张图片处理和批量图片处理
 */
@Slf4j
@Service
public class ImageProcessService {

    /**
     * S3代理服务，用于下载和上传图片
     */
    @Resource
    private S3ProxyService s3ProxyService;

    /**
     * 配置查询API，用于获取用户的S3配置和全局配置
     */
    @Resource
    private ConfigQueryApi configQueryApi;

    /**
     * S3云函数API，用于批量处理图片
     */
    @Resource
    private S3CloudFunctionApi s3CloudFunctionApi;

    /**
     * 处理单张图片
     * 将外部图片下载并上传到用户配置的S3存储中
     * 
     * @param imageUrl 需要处理的图片URL
     * @param userId   用户ID，用于获取用户的S3配置
     * @return 处理后的图片URL，如果处理失败则返回原图片URL
     */
    public String processImage(String imageUrl, String userId) {
        // 参数校验，如果图片URL为空则直接返回null
        if (StringUtils.isBlank(imageUrl)) {
            return null;
        }

        try {
            // 获取用户的S3配置，包含图床的访问凭证和存储路径等信息
            S3Config s3Config = configQueryApi.getImgS3Config(userId);
            // 如果用户未配置图床，则记录警告日志并返回原图片URL
            if (s3Config == null) {
                log.warn("用户未配置图床, userId={}", userId);
                return imageUrl;
            }

            // 调用S3代理服务下载外部图片并上传到用户配置的S3存储中
            UploadResult uploadResult = s3ProxyService.downloadAndUpload(s3Config, imageUrl);

            // 根据上传结果返回相应的URL
            if (uploadResult.isSuccess()) {
                // 图片处理成功，记录日志并返回新的图片URL
                log.info("图片处理成功, imageUrl={}, processedUrl={}", imageUrl, uploadResult.getUrl());
                return uploadResult.getUrl();
            } else {
                // 图片处理失败，记录警告日志并返回原图片URL
                log.warn("图片处理失败, imageUrl={}, errorMessage={}", imageUrl, uploadResult.getErrorMessage());
                return imageUrl;
            }
        } catch (Exception e) {
            // 处理过程中发生异常，记录错误日志并返回原图片URL
            log.error("图片处理异常, imageUrl={}", imageUrl, e);
            return imageUrl;
        }
    }

    /**
     * 批量处理图片
     * 将多个外部图片下载并上传到用户配置的S3存储中
     * 支持单张图片和多张图片的不同处理逻辑
     * 
     * @param imageUrls 需要处理的图片URL列表
     * @param userId    用户ID，用于获取用户的S3配置
     * @return 处理后的图片URL映射，key为原图片URL，value为处理后的图片URL
     */
    public Map<String, String> processImages(String link, List<String> imageUrls, String userId) {
        // 参数校验，如果图片URL列表为空则直接返回空映射
        if (CollectionUtils.isEmpty(imageUrls)) {
            return Map.of();
        }

        // 单张图片处理优化：如果只有一张图片，直接调用单张图片处理方法
        if (imageUrls.size() == 1) {
            // 获取处理后的URL和原始URL
            String processedUrl = imageUrls.getFirst();
            // 创建结果映射并添加处理结果
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put(processedUrl, processImage(processedUrl, userId));
            return resultMap;
        }

        // 检查图片数量是否超过限制
        // 获取全局配置中的最大图片数量限制
        int maxImageSizeLimit = configQueryApi.gloablConfig().getMaxImageSizeLimit();
        // 如果图片数量超过限制，则记录警告日志并返回空映射
        if (imageUrls.size() > maxImageSizeLimit) {
            log.warn("图片数量为{}，超过最大限制{}，未进行图片转存,userId={}", imageUrls.size(), maxImageSizeLimit, userId);
            return Map.of();
        }

        try {
            // 获取用户的S3配置，包含图床的访问凭证和存储路径等信息
            S3Config s3Config = configQueryApi.getImgS3Config(userId);
            // 如果用户未配置图床，则记录警告日志并返回空映射
            if (s3Config == null) {
                log.warn("用户未配置图床, userId={}", userId);
                return Map.of();
            }

            // 构建S3云函数请求对象，包含S3配置和图片URL列表
            S3CloudRQ s3Request = S3CloudRQ.builder()
                    .s3Config(s3Config) // 设置S3配置
                    .urls(imageUrls) // 设置图片URL列表
                    .build();

            // 设置 referer
            String referer = UrlUtils.getReferer(link);
            if (StringUtils.isNotBlank(referer)) {
                s3Request.setExtraParams(Map.of("referer", UrlUtils.getReferer(link)));
            }

            // 调用S3云函数API批量转存图片
            S3CloudRS s3Response = s3CloudFunctionApi.transformPicture(userId, s3Request);

            // 检查响应结果是否有效
            if (s3Response == null || MapUtil.isEmpty(s3Response.getResultMap())) {
                // 转存失败，记录错误日志并返回空映射
                log.error("转存图片失败,s3Response is empty, userId={}", userId);
                return Map.of();
            }

            // 获取转存结果映射
            Map<String, String> s3ResultMap = s3Response.getResultMap();
            // 记录处理完成日志，包含总数和成功数
            log.info("批量处理图片完成, count={}, success={}, userId={}", imageUrls.size(), s3ResultMap.size(), userId);
            return s3ResultMap;
        } catch (Exception e) {
            // 处理过程中发生异常，记录错误日志并返回空映射
            log.error("批量处理图片异常, userId={}", userId, e);
            return Map.of();
        }
    }
}
