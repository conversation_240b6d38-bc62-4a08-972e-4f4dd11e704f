package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("sspaiParser")
public class SspaiParser extends AbstractParser {

    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private ConfigQueryApi configQueryApi;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
    
    
    @Override
    public boolean supports(String url) {
        return url.contains("sspai.com");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);
        Elements elements = document.select("div[class=\"article-body\"]");
        Element element = elements.first();
        if (element == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "少数派文章解析异常");
        }
        return globalElementParser.parseElement(element);
    }
    
    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.SSPAI.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_SSPAI;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_SSPAI;
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements elements = document.select("meta[name=\"weibo:article:create_at\"]");
        if (elements.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        Optional<String> publishTimeOpt = elements.stream()
                .filter(element -> element.hasAttr("content"))
                .findAny()
                .map(e -> e.attr("content"));
        return publishTimeOpt.map(s -> LocalDateTime.from(formatter.parse(s))).orElseGet(LocalDateTime::now).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    protected String parseLink(Document document) {
        Elements elements = document.select("meta[property=\"og:url\"]");
        String url = Optional.ofNullable(elements.first()).map(e -> e.attr("content")).orElse(null);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        if (!url.contains("sspai.com")) {
            return "https://sspai.com" + (url.startsWith("/") ? url : "/" + url);
        }
        return null;
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements elements = document.select("meta[name=\"author\"]");
        if (elements.isEmpty()) {
            return "";
        }
        Optional<String> authorOpt = elements.stream()
                .filter(element -> element.hasAttr("content"))
                .findAny()
                .map(e -> e.attr("content"));
        return authorOpt.orElse("未知");
    }

}
