package com.maixi.road.clipper.output;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.maixi.road.clipper.image.ImageUploadHelper;
import com.maixi.road.clipper.output.helper.ArticleBuildHelper;
import com.maixi.road.cloudfunc.notion.dto.rq.BlockAppendRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.NotionBaseRS;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.ArticleFieldDTO;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.utils.MDCRunnable;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.integration.notion.model.page.Cover;
import com.maixi.road.common.integration.notion.model.page.Page;
import com.maixi.road.common.integration.notion.model.page.Parent;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.notion.remote.NotionClient;
import com.maixi.road.notion.remote.dto.request.PageCreateRQ;
import com.maixi.road.notion.remote.manager.Article2NotionPageAdapter;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class NotionOutputService {

    @Resource
    private NotionClient notionClient;

    @Resource
    private ConfigQueryApi configQueryApi;

    @Resource
    private Article2NotionPageAdapter article2NotionPageAdapter;

    private final ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * Notion 支持的图片后缀列表
     * 用于验证图片格式是否被 Notion 支持
     */
    private static final List<String> NOTION_SUPPORT_IMG_SUFFIX = Lists.newArrayList(
            ".jpg", ".jpeg", ".png", ".svg", ".gif", ".heic", ".bmp", ".tif", ".tiff");

    /**
     * 创建Notion页面（高级方法）
     * <p>
     * 完整的页面创建方法，包含属性构建、元数据设置、块处理、异步追加和详细的错误处理
     * 这是客户端的核心方法，实现了完整的文章到Notion页面的转换和保存逻辑
     * </p>
     *
     * @param request 页面创建请求对象，包含用户表单、用户ID等信息
     * @return Result<PageCreateRS> 包含创建结果的响应对象
     */
    public Result<PageCreateRS> saveArticle(PageCreateRQ request) {

        // 获取用户文章字段配置
        ArticleFieldDTO userArticle = configQueryApi.getArticleFieldDTO(request.getUnionId());
        // 构建页面属性，包含标题、作者、发布时间等
        JSONObject propertiesBuild = ArticleBuildHelper.propertiesBuild(userArticle, request.getUserForm());
        // 创建页面对象，设置父级和属性
        Page page = Page.builder()
                .parent(Parent.build(userArticle.getDatabaseId()))
                .properties(propertiesBuild)
                .build();
        // 如果请求中指定了需要图标，则设置页面图标
        if (!request.isNoIcon()) {
            page.setIcon(
                    ArticleBuildHelper.buildIcon(request.getUserForm().getIcon(), request.getUserForm().getOrigin()));
        }
        // 如果请求中指定了需要封面，则设置页面封面
        if (!request.isNoCover()) {
            page.setCover(Cover.build(request.getUserForm().getCover()));
        }

        // 检查并替换无效的图片块
        checkAndReplaceInvalidImageBlock(request.getUserForm());

        // 处理块内容
        // 判断 block个数是否超过限制，Notion API 限制 createPage 和 appendBlock 的 block 个数为 100 个
        // 返回的是超出限制的块，需要在页面创建后异步追加
        List<Block> retainBlocks = article2NotionPageAdapter.handleBlocks(request.getUserForm(), page,
                request.getBlockSizeLimit());

        try {
            // 调用创建页面的API
            PageCreateRS createResult = notionClient.createPage(userArticle.getAccessToken(), page,
                    request.getUnionId());
            // 检查创建结果是否成功
            if (createResult != null && "page".equals(createResult.getObject())) {
                // 页面保存成功，获取页面ID
                String pageId = createResult.getId();
                // 如果有超出限制的块，继续异步追加到页面中
                if (!CollectionUtils.isEmpty(retainBlocks)) {
                    handleRetainBlocks(retainBlocks, userArticle.getAccessToken(), pageId, request.getUnionId());
                }
                // 返回成功结果
                return Result.success(createResult);
            } else {
                // 创建失败，构建错误信息
                String message = "Notion保存异常";
                if (createResult != null) {
                    message = createResult.getCode() + ":" + createResult.getMessage();
                }
                // 抛出异常
                throw RoadException.create(ErrorCodeEnum.FALLBACK, message);
            }

        } catch (Exception e) {
            log.error("NotionOutputService.saveArticle.error: {}, unionId={}, link={}", e.getMessage(), request.getUnionId(), request.getUserForm().getLink());
            // 如果是自定义异常，进行特殊处理
            if (e instanceof RoadException exception) {
                String message = exception.getMessage();
                log.error("url={}, message={}", request.getUserForm().getLink(), message);
                // 根据错误信息内容进行分类处理，转换为用户友好的错误提示
                if (message.contains("is not a property that exists.")) {
                    throw RoadException.create(ErrorCodeEnum.FALLBACK, "请检查文章数据库字段是否被修改");
                } else if(message.contains("Invalid image url.")){
                    throw RoadException.create(ErrorCodeEnum.FALLBACK, "图片保存异常，建议配置图床进行转存替换");
                } else if (message.contains("API token is invalid.")) {
                    throw RoadException.create(ErrorCodeEnum.FALLBACK, "授权异常，请重新授权");
                } else if (message.contains("Failed to connect to api.notion.com")) {
                    throw RoadException.create(ErrorCodeEnum.FALLBACK, "连接超时,请稍后重试1-2次");
                } else if (message.contains("image.external.url should be defined")) {
                    throw RoadException.create(ErrorCodeEnum.FALLBACK, "请检查图床配置");
                } else if (message.contains("validation_error")) {
                    throw RoadException.create(ErrorCodeEnum.FALLBACK, "是否修改数据库？或者反馈给开发者看看");
                } else if (message.contains("object_not_found")) {
                    throw RoadException.create(ErrorCodeEnum.FALLBACK, "请检查是否移动数据库");
                }
                // 如果不属于上述分类，直接抛出原异常
                throw exception;
            }
            // 如果是其他异常，封装为通用异常抛出
            throw RoadException.create(ErrorCodeEnum.FALLBACK, e.getMessage());
        }
    }

    /**
     * 处理保留块（超出限制的块）
     * <p>
     * 异步处理超出 Notion API 限制的块内容，将其分批追加到创建好的页面中
     * 使用线程池异步执行，避免阻塞主流程，提高用户体验
     * </p>
     *
     * @param retainBlocks 需要追加的块列表
     * @param accessToken  Notion访问令牌
     * @param pageId       目标页面ID
     * @param unionId      用户唯一标识
     */
    private void handleRetainBlocks(List<Block> retainBlocks, String accessToken, String pageId, String unionId) {
        // 使用线程池异步执行，并保留MDC上下文信息（用于日志跟踪）
        executor.execute(new MDCRunnable(() -> {
            // 将剩余的 blocks 按单批 100 个 block 分组，避免超过Notion API的限制
            List<List<Block>> partition = Lists.partition(retainBlocks, 100);
            // 逐个处理分组
            for (List<Block> blocks : partition) {
                // 追加当前批次的块
                boolean appendIsSuccess = appendBlocks(blocks, accessToken, pageId, unionId);
                // 如果有一次追加失败，就停止后续处理以避免无意义的重试
                if (!appendIsSuccess) {
                    break;
                }
            }
        }));
    }

    /**
     * 追加块到指定页面
     * <p>
     * 将一组块追加到Notion页面中，并处理可能出现的异常情况
     * 这是内部辅助方法，由handleRetainBlocks调用
     * </p>
     *
     * @param blocks      要追加的块列表
     * @param accessToken Notion访问令牌
     * @param pageId      目标页面ID
     * @param unionId     用户唯一标识
     * @return boolean 追加是否成功
     */
    private boolean appendBlocks(List<Block> blocks, String accessToken, String pageId, String unionId) {
        // 创建追加请求对象
        BlockAppendRQ request = new BlockAppendRQ();
        // 设置要追加的子块
        request.setChildren(blocks);
        try {
            // 调用API追加块内容
            NotionBaseRS result = notionClient.appendBlockChildren(accessToken, pageId, request, unionId);
            // 检查响应结果是否有效
            if (result == null || !"list".equals(result.getObject())) {
                // 记录失败日志，包含请求内容便于排查
                log.error("文章追加内容失败, result={}", JSON.toJSONString(request));
                return false;
            }
        } catch (IOException e) {
            // 记录IO异常，可能是网络问题或API调用失败
            log.error("文章追加内容异常 unionId={}", unionId, e);
            return false;
        }
        // 追加成功
        return true;
    }

    /**
     * 检查并替换无效的图片块
     * <p>
     * 检查图片链接是否以 https:// 或 http:// 开头，以及是否包含 Notion 支持的图片后缀。
     * 如果发现不符合条件的图片，将其替换为 callout 块，并将图片链接作为 callout 的文本内容。
     * </p>
     * 
     * @param basicResult 解析结果，包含需要检查的块列表
     */
    private void checkAndReplaceInvalidImageBlock(ResolveFormRQ basicResult) {
        if (basicResult == null || CollectionUtils.isEmpty(basicResult.getBlocks())) {
            return;
        }

        List<Block> blocks = basicResult.getBlocks();
        for (int i = 0; i < blocks.size(); i++) {
            Block block = blocks.get(i);
            if (block == null || !ImageUploadHelper.isImageBlock(block)) {
                continue;
            }

            // 获取图片 URL
            String imageUrl = block.getImage().getExternal().getUrl();
            if (StringUtils.isBlank(imageUrl)) {
                // 图片 URL 为空，替换为 callout
                blocks.set(i, Block.quote(RichTexts.build(Collections.singletonList(RichText.simpleText("图片链接为空，无法显示图片")))));
                log.warn("发现空的图片链接，已替换为 callout 块");
                continue;
            }

            // 检查图片 URL 是否有效
            if (!isValidImageUrl(imageUrl)) {
                // 无效的图片 URL，替换为 callout
                blocks.set(i, Block.quote(RichTexts.build(Collections.singletonList(RichText.textWithLink("这里有张图片转存失败,点击可下载",imageUrl)))));
                log.warn("发现无效的图片链接，已替换为 callout 块，imageUrl={}", imageUrl);
            }
        }
    }

    /**
     * 检查图片 URL 是否有效
     * <p>
     * 有效的图片 URL 需要满足以下条件：
     * 1. 以 https:// 或 http:// 开头
     * 2. 包含 Notion 支持的图片后缀
     * </p>
     * 
     * @param imageUrl 图片 URL
     * @return 如果 URL 有效返回 true，否则返回 false
     */
    private boolean isValidImageUrl(String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            return false;
        }

        // 检查协议是否正确
        if (!imageUrl.startsWith("https://") && !imageUrl.startsWith("http://")) {
            log.debug("图片 URL 协议不正确，imageUrl={}", imageUrl);
            return false;
        }

        // 检查是否包含有效的图片后缀
        if (!hasValidImageExtension(imageUrl)) {
            log.debug("图片 URL 不包含有效的图片后缀，imageUrl={}", imageUrl);
            return false;
        }

        return true;
    }

    /**
     * 检查 URL 是否包含有效的图片后缀
     * <p>
     * 该方法会正确处理 URL 参数和锚点，从 URL 中提取文件路径部分，
     * 然后检查是否包含 Notion 支持的图片后缀。
     * </p>
     * 
     * @param url 图片 URL
     * @return 如果包含有效的图片后缀返回 true，否则返回 false
     */
    private boolean hasValidImageExtension(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }

        try {
            // 移除查询参数和锚点，只保留路径部分
            String pathPart = url;

            // 移除锚点（#后面的部分）
            int hashIndex = pathPart.indexOf('#');
            if (hashIndex != -1) {
                pathPart = pathPart.substring(0, hashIndex);
            }

            // 移除查询参数（?后面的部分）
            int queryIndex = pathPart.indexOf('?');
            if (queryIndex != -1) {
                pathPart = pathPart.substring(0, queryIndex);
            }

            // 将路径转换为小写进行比较
            String lowerCasePath = pathPart.toLowerCase();

            // 检查是否包含任何支持的图片后缀
            return NOTION_SUPPORT_IMG_SUFFIX.stream()
                    .anyMatch(suffix -> lowerCasePath.contains(suffix));

        } catch (Exception e) {
            log.warn("检查图片后缀时发生异常，url={}", url, e);
            return false;
        }
    }
}
