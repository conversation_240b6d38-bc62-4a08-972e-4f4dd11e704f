package com.maixi.road.clipper.service.tagresolver;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Heading;
import com.maixi.road.common.integration.notion.model.block.ListItem;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.RichText;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 无序列表标签解析器
 * 解析
 * <ul>
 * 标签，支持嵌套列表处理
 */
@Component
@Slf4j
public class BulletedListItemTagResolver implements ContentTagResolver {

    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private NumberedListItemTagResolver numberedListItemTagResolver;

    /**
     * 判断是否为无序列表标签
     * 
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "ul".equalsIgnoreCase(node.nodeName());
    }

    /**
     * 解析无序列表标签
     * 支持同级列表的收养机制：当ul下出现li+ul结构时，将同级的ul收养为li的children
     * 
     * @param element DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        Elements children = element.children();
        List<Block> blocks = Lists.newArrayList();

        // 使用索引遍历以支持"向前看"和跳过已处理元素
        for (Element currentChild : children) {
            String childTagName = currentChild.nodeName().toLowerCase();

            switch (childTagName) {
                case "li" -> {
                    // 判断如果 li 包含ul 或者ol 子元素，则提取 ownerText 作为li的rich_text，ul 和 ol 的内容作为li的children
                    if (currentChild.childrenSize() > 0
                            && (!currentChild.select("ul").isEmpty() || !currentChild.select("ol").isEmpty())) {
                        String ownerText = currentChild.ownText();
                        List<RichText> richTexts;

                        // 确保至少有一个富文本内容，即使 ownerText 为空
                        if (StringUtils.isNotBlank(ownerText)) {
                            richTexts = List.of(RichText.simpleText(ownerText));
                            log.debug("提取li的ownerText作为主内容: {}", ownerText);
                        } else {
                            richTexts = List.of(RichText.simpleText(""));
                            log.debug("li的ownerText为空，使用空字符串作为主内容");
                        }

                        // 创建 liBlock，确保不为 null
                        Block liBlock = Block.buildBulletedListItem(ListItem.builder().rich_text(richTexts).build());
                        blocks.add(liBlock);

                        // 处理 ul 子元素
                        currentChild.select("ul").forEach(ul -> {
                            List<Block> childBlocks = this.resolve(ul);
                            if (!childBlocks.isEmpty()) {
                                safeAddChildrenToBulletedItem(blocks, childBlocks);
                                log.debug("添加 {} 个 ul 子块到 li 的 children 中", childBlocks.size());
                            }
                        });

                        // 处理 ol 子元素
                        currentChild.select("ol").forEach(ol -> {
                            List<Block> childBlocks = numberedListItemTagResolver.resolve(ol);
                            if (!childBlocks.isEmpty()) {
                                safeAddChildrenToNumberedItem(blocks, childBlocks);
                                log.debug("添加 {} 个 ol 子块到 li 的 children 中", childBlocks.size());
                            }
                        });

                        log.debug("成功创建包含嵌套列表的 li 块");
                    } else {
                        // 优化的li元素处理逻辑
                        Block liBlock = createOptimizedListItem(currentChild);
                        if (liBlock != null) {
                            blocks.add(liBlock);
                        }
                    }
                }
                case "ul" -> {
                    List<Block> childBlocks = this.resolve(currentChild);
                    // 修复NoSuchElementException：先检查blocks是否为空，再调用getLast()
                    if (!childBlocks.isEmpty() && !blocks.isEmpty() && blocks.getLast() != null) {
                        safeAddChildrenToBulletedItem(blocks, childBlocks);
                        log.debug("将ul子块收养到前一个li元素的children中，子块数量: {}", childBlocks.size());
                    } else {
                        log.warn("无法收养ul子块：blocks为空或childBlocks为空，childBlocks数量: {}, blocks数量: {}",
                                childBlocks.size(), blocks.size());
                    }
                }
                case "ol" -> {
                    List<Block> childBlocks = numberedListItemTagResolver.resolve(currentChild);
                    // 修复NoSuchElementException：先检查blocks是否为空，再调用getLast()
                    if (!childBlocks.isEmpty() && !blocks.isEmpty() && blocks.getLast() != null) {
                        safeAddChildrenToNumberedItem(blocks, childBlocks);
                        log.debug("将ol子块收养到前一个li元素的children中，子块数量: {}", childBlocks.size());
                    } else {
                        log.warn("无法收养ol子块：blocks为空或childBlocks为空，childBlocks数量: {}, blocks数量: {}",
                                childBlocks.size(), blocks.size());
                    }
                }
                default ->
                    // 非li元素直接解析，不参与收养逻辑
                    // List<Block> childBlocks = globalElementParser.parseElement(currentChild);
                    // if (!childBlocks.isEmpty()) {
                    // finalBlocks.addAll(childBlocks);
                    // log.debug("处理非li子元素 <{}>，生成 {} 个Block", childTagName, childBlocks.size());
                    // }
                        log.warn("-----------------特殊元素：{}", currentChild.nodeName());
            }
        }

        return blocks;
    }

    /**
     * 安全地将子块添加到无序列表项的 children 中
     * 进行空指针检查和必要的初始化，并将所有子块转换为 paragraph block
     * 
     * @param blocks      父级块列表
     * @param childBlocks 要添加的子块列表
     */
    private void safeAddChildrenToBulletedItem(List<Block> blocks, List<Block> childBlocks) {
        if (blocks == null || blocks.isEmpty() || childBlocks == null || childBlocks.isEmpty()) {
            log.warn("无法添加子块：blocks或childBlocks为空");
            return;
        }

        Block lastBlock = blocks.get(blocks.size() - 1);
        if (lastBlock == null) {
            log.warn("无法添加子块：最后一个Block为null");
            return;
        }

        ListItem bulletedListItem = lastBlock.getNumbered_list_item();
        if (bulletedListItem == null) {
            bulletedListItem = lastBlock.getBulleted_list_item();
            if (bulletedListItem == null) {
                log.warn("无法添加子块：NumberedListItem为null");
                return;
            }
        }


        // 初始化children列表如果为null
        List<Block> children = bulletedListItem.getChildren();
        if (children == null) {
            children = Lists.newArrayList();
            bulletedListItem.setChildren(children);
            log.debug("初始化BulletedListItem的children列表");
        }

        // 将所有子块转换为 paragraph block 后添加
        List<Block> paragraphBlocks = convertBlocksToParagraphs(childBlocks);
        children.addAll(paragraphBlocks);
        log.debug("成功添加 {} 个转换后的paragraph子块到BulletedListItem的children中", paragraphBlocks.size());
    }

    /**
     * 安全地将子块添加到有序列表项的 children 中
     * 进行空指针检查和必要的初始化，并将所有子块转换为 paragraph block
     * 
     * @param blocks      父级块列表
     * @param childBlocks 要添加的子块列表
     */
    private void safeAddChildrenToNumberedItem(List<Block> blocks, List<Block> childBlocks) {
        if (blocks == null || blocks.isEmpty() || childBlocks == null || childBlocks.isEmpty()) {
            log.warn("无法添加子块：blocks或childBlocks为空");
            return;
        }

        Block lastBlock = blocks.get(blocks.size() - 1);
        if (lastBlock == null) {
            log.warn("无法添加子块：最后一个Block为null");
            return;
        }

        ListItem numberedListItem = lastBlock.getNumbered_list_item();
        if (numberedListItem == null) {
            numberedListItem = lastBlock.getBulleted_list_item();
            if (numberedListItem == null) {
                log.warn("无法添加子块：NumberedListItem为null");
                return;
            }
        }

        // 初始化children列表如果为null
        List<Block> children = numberedListItem.getChildren();
        if (children == null) {
            children = Lists.newArrayList();
            numberedListItem.setChildren(children);
            log.debug("初始化NumberedListItem的children列表");
        }

        // 将所有子块转换为 paragraph block 后添加
        List<Block> paragraphBlocks = convertBlocksToParagraphs(childBlocks);
        children.addAll(paragraphBlocks);
        log.debug("成功添加 {} 个转换后的paragraph子块到NumberedListItem的children中", paragraphBlocks.size());
    }

    /**
     * 将Block列表转换为paragraph block列表
     * 提取每个Block的文本内容，转换为paragraph block
     * 
     * @param blocks 原始Block列表
     * @return 转换后的paragraph block列表
     */
    private List<Block> convertBlocksToParagraphs(List<Block> blocks) {
        if (blocks == null || blocks.isEmpty()) {
            return Lists.newArrayList();
        }

        List<Block> paragraphBlocks = Lists.newArrayList();

        for (Block block : blocks) {
            if (block == null) {
                continue;
            }

            // 提取Block的文本内容
            String textContent = extractTextFromBlock(block);

            if (StringUtils.isNotBlank(textContent)) {
                // 创建 paragraph block
                List<RichText> richTexts = Lists.newArrayList(RichText.simpleText(textContent));
                Block paragraphBlock = Block.paragraph(RichTexts.build(richTexts));
                paragraphBlocks.add(paragraphBlock);
                log.debug("转换Block为paragraph: {} -> {}", block.getType(), textContent);
            }
        }

        return paragraphBlocks;
    }

    /**
     * 从Block中提取文本内容
     * 根据不同的Block类型提取相应的文本内容
     * 
     * @param block 待提取的Block
     * @return 提取的文本内容
     */
    private String extractTextFromBlock(Block block) {
        if (block == null || StringUtils.isBlank(block.getType())) {
            return "";
        }

        switch (block.getType()) {
            case "paragraph":
                return extractRichTextContent(block.getParagraph());
            case "bulleted_list_item":
                return extractRichTextContent(block.getBulleted_list_item());
            case "numbered_list_item":
                return extractRichTextContent(block.getNumbered_list_item());
            case "heading_1":
                return extractRichTextContent(block.getHeading_1());
            case "heading_2":
                return extractRichTextContent(block.getHeading_2());
            case "heading_3":
                return extractRichTextContent(block.getHeading_3());
            case "quote":
                return extractRichTextContent(block.getQuote());
            case "code":
                return block.getCode() != null ? block.getCode().getRich_text().stream()
                        .map(rt -> rt.getText() != null ? rt.getText().getContent() : "")
                        .collect(Collectors.joining()) : "";
            default:
                log.warn("未知的Block类型，无法提取文本: {}", block.getType());
                return "";
        }
    }

    /**
     * 从RichTexts对象中提取文本内容
     * 
     * @param richTexts RichTexts对象
     * @return 提取的文本内容
     */
    private String extractRichTextContent(RichTexts richTexts) {
        if (richTexts == null || richTexts.getRich_text() == null) {
            return "";
        }

        return richTexts.getRich_text().stream()
                .map(rt -> rt.getText() != null ? rt.getText().getContent() : "")
                .collect(Collectors.joining());
    }

    /**
     * 从ListItem对象中提取文本内容
     * 
     * @param listItem ListItem对象
     * @return 提取的文本内容
     */
    private String extractRichTextContent(ListItem listItem) {
        if (listItem == null || listItem.getRich_text() == null) {
            return "";
        }

        return listItem.getRich_text().stream()
                .map(rt -> rt.getText() != null ? rt.getText().getContent() : "")
                .collect(Collectors.joining());
    }

    /**
     * 从Heading对象中提取文本内容
     * 
     * @param heading Heading对象
     * @return 提取的文本内容
     */
    private String extractRichTextContent(Heading heading) {
        if (heading == null || heading.getRich_text() == null) {
            return "";
        }

        return heading.getRich_text().stream()
                .map(rt -> rt.getText() != null ? rt.getText().getContent() : "")
                .collect(Collectors.joining());
    }

    /**
     * 创建优化的列表项Block
     * 参考quote元素的处理逻辑，支持复杂子元素的智能处理
     * 
     * @param liElement li DOM元素
     * @return 优化后的Block
     */
    private Block createOptimizedListItem(Element liElement) {
        // 先判断是否存在子元素，如果不存在，则直接按当前逻辑处理
        if (liElement.children().isEmpty()) {
            String text = liElement.text();
            if (StringUtils.isBlank(text)) {
                return null;
            }
            List<RichText> richTextList = List.of(RichText.simpleText(text));
            return Block.buildBulletedListItem(ListItem.builder().rich_text(richTextList).build());
        }

        String ownerText = liElement.ownText();
        if (StringUtils.isNotBlank(ownerText)) {
            List<RichText> richTextList = List.of(RichText.simpleText(ownerText));
            Block liBlock = Block.buildBulletedListItem(ListItem.builder().rich_text(richTextList).build());
            liElement.children().forEach(child -> {
                List<Block> childBlocks = globalElementParser.parseElement(child);
                if (!childBlocks.isEmpty()) {
                    List<Block> children = liBlock.getBulleted_list_item().getChildren();
                    if (children == null) {
                        children = Lists.newArrayList();
                        liBlock.getBulleted_list_item().setChildren(children);
                        log.debug("初始化NumberedListItem的children列表");
                    }
                    children.addAll(childBlocks);
                }
            });
            return liBlock;
        }

        // 分析子元素类型 - 使用 JSoup API 查找所有嵌套的图片元素
        List<Element> imageElements = Lists.newArrayList();

        // 查找所有嵌套的 img 元素，不仅仅是直接子元素
        Elements allImageElements = liElement.select("img");
        if (!allImageElements.isEmpty()) {
            imageElements.addAll(allImageElements);
            log.debug("在 li 元素中找到 {} 个图片元素（包括嵌套的）", allImageElements.size());
        }

        // 构建rich_text内容
        List<RichText> richTexts = Lists.newArrayList();

        // 如果所有子元素都是文本元素，则遍历子元素，将得到的blocks提取出来作为li元素的richTexts
        if (imageElements.isEmpty()) {
            richTexts = extractRichTextsFromTextElements(liElement.children());
        } else {
            // 混合情况：先添加直接文本内容
            String directText = liElement.text();
            if (StringUtils.isNotBlank(directText)) {
                richTexts.add(RichText.simpleText(directText));
            }

        }

        // 如果没有富文本内容，创建空的富文本
        if (richTexts.isEmpty()) {
            log.error("没有富文本内容");
            richTexts.add(RichText.simpleText(""));
        }

        // 构建ListItem
        ListItem.ListItemBuilder listItemBuilder = ListItem.builder().rich_text(richTexts);

        // 处理children（图片元素和其他复杂元素）
        List<Block> childrenBlocks = Lists.newArrayList();

        // 添加图片元素到children
        for (Element imgElement : imageElements) {
            List<Block> imgBlocks = globalElementParser.parseElement(imgElement);
            childrenBlocks.addAll(imgBlocks);
            log.debug("添加图片元素到li的children中");
        }

        if (!childrenBlocks.isEmpty()) {
            listItemBuilder.children(childrenBlocks);
        }

        Block liBlock = Block.buildBulletedListItem(listItemBuilder.build());
        log.debug("创建优化的li元素，rich_text数量: {}, children数量: {}",
                richTexts.size(), childrenBlocks.size());

        return liBlock;
    }

    /**
     * 从文本元素列表中提取富文本内容
     * 
     * @param textElements 文本元素列表
     * @return 富文本列表
     */
    private List<RichText> extractRichTextsFromTextElements(List<Element> textElements) {
        List<RichText> richTexts = Lists.newArrayList();

        for (Element textElement : textElements) {
            List<Block> childBlocks = globalElementParser.parseElement(textElement);

            for (Block block : childBlocks) {
                if (block == null) {
                    continue;
                }

                // 从block中提取富文本内容
                List<RichText> blockRichTexts = extractRichTextsFromBlock(block);
                if (!blockRichTexts.isEmpty()) {
                    richTexts.addAll(blockRichTexts);
                }
            }
        }

        // 如果没有提取到任何富文本，使用元素的文本内容
        if (richTexts.isEmpty()) {
            for (Element textElement : textElements) {
                String text = textElement.text();
                if (StringUtils.isNotBlank(text)) {
                    richTexts.add(RichText.simpleText(text));
                }
            }
        }

        return richTexts;
    }

    /**
     * 从Block中提取富文本内容
     * 
     * @param block 待提取的Block
     * @return 富文本列表
     */
    private List<RichText> extractRichTextsFromBlock(Block block) {
        if (block == null || StringUtils.isBlank(block.getType())) {
            return Lists.newArrayList();
        }

        switch (block.getType()) {
            case "paragraph":
                return block.getParagraph() != null && block.getParagraph().getRich_text() != null
                        ? block.getParagraph().getRich_text()
                        : Lists.newArrayList();
            case "bulleted_list_item":
                return block.getBulleted_list_item() != null && block.getBulleted_list_item().getRich_text() != null
                        ? block.getBulleted_list_item().getRich_text()
                        : Lists.newArrayList();
            case "numbered_list_item":
                return block.getNumbered_list_item() != null && block.getNumbered_list_item().getRich_text() != null
                        ? block.getNumbered_list_item().getRich_text()
                        : Lists.newArrayList();
            default:
                // 对于其他类型，提取文本内容并转换为简单富文本
                String textContent = extractTextFromBlock(block);
                return StringUtils.isNotBlank(textContent)
                        ? List.of(RichText.simpleText(textContent))
                        : Lists.newArrayList();
        }
    }

}
