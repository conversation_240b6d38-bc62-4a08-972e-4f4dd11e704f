package com.maixi.road.clipper.service.tagresolver;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.util.ColorMapper;
import com.maixi.road.common.integration.notion.enums.BackgroundColorEnum;
import com.maixi.road.common.integration.notion.enums.ColorEnum;
import com.maixi.road.common.integration.notion.tags.TextTag;
import com.maixi.road.common.integration.notion.tags.TextTagProperty;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TextTagResolver {

    public static List<TextTag> resolverTextTag(Element element) {
        // 记录当前标签属性
        TextTagProperty currentTextTagProperty = currentTextTag(element);

        List<TextTag> textTags = Lists.newArrayList();
        for (Node node : element.childNodes()) {

            if (node instanceof TextNode) {
                textTags.add(TextTag.buildTextTag(((TextNode) node).text(), currentTextTagProperty));

            } else if (node instanceof Element) {
                Element childElement = (Element) node;
                List<TextTag> childTextTags = resolverTextTag(childElement);
                for (TextTag textTag : childTextTags) {
                    combineSourceToChild(textTag, currentTextTagProperty);
                    textTags.add(textTag);
                }
            }
        }
        return textTags;
    }

    public static void combineSourceToChild(TextTag target, TextTagProperty source) {
        // 合并href：只有在子元素没有显式设置时才继承父元素的href
        if (StringUtils.isNoneBlank(source.getHref()) && StringUtils.isBlank(target.getHref())
                && !target.isHrefExplicit()) {
            target.setHref(source.getHref());
            // 不标记为显式设置，因为这是继承来的
        }

        // 合并color：只有在子元素没有显式设置时才继承父元素的color
        if (StringUtils.isNoneBlank(source.getColor()) && StringUtils.isBlank(target.getColor())
                && !target.isColorExplicit()) {
            target.setColor(source.getColor());
            // 不标记为显式设置，因为这是继承来的
        }

        // 合并annotations：需要特别处理bold等样式
        int inheritedAnnotations = combineAnnotationsWithInheritance(target, source);
        target.setAnnotations(inheritedAnnotations);
    }

    /**
     * 智能合并annotations，考虑样式继承规则
     */
    private static int combineAnnotationsWithInheritance(TextTag target, TextTagProperty source) {
        int targetAnnotations = target.getAnnotations();
        int sourceAnnotations = source.getAnnotations();

        // 对于每个样式位，检查是否应该继承
        int resultAnnotations = targetAnnotations;

        // Bold样式处理 (位64)
        boolean sourceBold = (sourceAnnotations & 64) != 0;
        boolean targetBold = (targetAnnotations & 64) != 0;

        if (target.isBoldExplicit()) {
            // 子元素有显式设置（无论是bold还是normal），保持子元素的设置
            if (targetBold) {
                resultAnnotations |= 64;
            } else {
                resultAnnotations &= ~64; // 确保清除bold位
            }
        } else if (sourceBold) {
            // 子元素没有显式设置，且父元素是bold，则继承bold
            resultAnnotations |= 64;
        }
        // 如果子元素没有显式设置，且父元素也不是bold，则保持原样（normal）

        // Italic样式处理 (位32)
        boolean sourceItalic = (sourceAnnotations & 32) != 0;
        boolean targetItalic = (targetAnnotations & 32) != 0;

        if (target.isItalicExplicit()) {
            // 子元素有显式设置（无论是italic还是normal），保持子元素的设置
            if (targetItalic) {
                resultAnnotations |= 32;
            } else {
                resultAnnotations &= ~32; // 确保清除italic位
            }
        } else if (sourceItalic) {
            // 子元素没有显式设置，且父元素是italic，则继承italic
            resultAnnotations |= 32;
        }

        // 其他样式位直接合并（underline、strikethrough等）
        // 这些样式通常不需要特殊的继承处理
        resultAnnotations |= (sourceAnnotations & ~(64 | 32)); // 排除bold和italic位

        return resultAnnotations;
    }

    public static int calculateAnnotations(Element element, TextTagProperty currentTextTag) {
        int annotations = 0;
        // 1. HTML标签显式粗体
        if (element.nodeName().equals("b")
                || element.nodeName().equals("strong")
                || element.nodeName().equals("em")
                || element.nodeName().equals("big")) {
            annotations |= 64;
            currentTextTag.markBoldExplicit(); // 显式标记
        }
        if (element.nodeName().equals("a")) {
            String href = element.attr("href");
            if (StringUtils.isNoneBlank(href) && href.startsWith("http")) {
                annotations = annotations | 1;
                currentTextTag.setHref(href);
            }
        } else if (element.nodeName().equals("mark")) {
            annotations = annotations | 2;
            currentTextTag.setColor(BackgroundColorEnum._yellow.getColor());
        } else if (element.nodeName().equals("code") || element.nodeName().equals("samp")) {
            annotations = annotations | 4;
        } else if (element.nodeName().equals("u")) {
            annotations = annotations | 8;
        } else if (element.nodeName().equals("del") || element.nodeName().equals("strike")) {
            annotations = annotations | 16;
        } else if (element.nodeName().equals("i")) {
            annotations = annotations | 32;
        }

        // 2. CSS显式粗体/normal
        String style = element.attr("style");
        boolean explicitBoldFromCSS = false;
        if (StringUtils.isNotBlank(style) && style.toLowerCase().contains("font-weight")) {
            String fontWeight = extractFontWeight(style.toLowerCase()).trim();
            if ("normal".equals(fontWeight)) {
                currentTextTag.markBoldExplicit();
                annotations &= ~64;
                explicitBoldFromCSS = true;
            } else if (isBoldFontWeight(fontWeight)) {
                currentTextTag.markBoldExplicit();
                annotations |= 64;
                explicitBoldFromCSS = true;
            }
        }
        if (!explicitBoldFromCSS && isBoldElement(element)
                && !(element.nodeName().equals("b") || element.nodeName().equals("strong")
                        || element.nodeName().equals("em") || element.nodeName().equals("big"))) {
            // 只有在没有被CSS显式设置且不是HTML显式标签时，才根据继承判断
            annotations |= 64;
        }

        // 检查并设置CSS颜色
        ColorEnum cssColor = parseCSSColor(element);
        if (cssColor != null) {
            currentTextTag.setColor(cssColor.getColor());
            currentTextTag.markColorExplicit();
        }
        return annotations;
    }

    /**
     * 检查元素是否为粗体样式
     * 包括HTML标签和CSS样式的检查
     * 
     * @param element 要检查的元素
     * @return true表示是粗体样式
     */
    private static boolean isBoldElement(Element element) {
        // 检查HTML标签
        if (element.nodeName().equals("b")
                || element.nodeName().equals("strong")
                || element.nodeName().equals("em")
                || element.nodeName().equals("big")) {
            return true;
        }

        // 检查CSS内联样式
        if (checkFontWeightFromStyle(element)) {
            return true;
        }

        return false;
    }

    /**
     * 从元素的内联样式中检查font-weight属性
     * 
     * @param element 要检查的元素
     * @return true表示font-weight为粗体
     */
    private static boolean checkFontWeightFromStyle(Element element) {
        String style = element.attr("style");
        if (StringUtils.isBlank(style)) {
            return false;
        }

        // 将样式字符串转换为小写便于匹配
        String lowerStyle = style.toLowerCase();

        // 查找font-weight属性
        if (lowerStyle.contains("font-weight")) {
            // 提取font-weight的值
            String fontWeight = extractFontWeight(lowerStyle);
            return isBoldFontWeight(fontWeight);
        }

        return false;
    }

    /**
     * 从样式字符串中提取font-weight的值
     * 
     * @param style 样式字符串（已转换为小写）
     * @return font-weight的值，如果未找到返回空字符串
     */
    private static String extractFontWeight(String style) {
        try {
            // 匹配 font-weight: 值 的模式
            int startIndex = style.indexOf("font-weight:");
            if (startIndex == -1) {
                return "";
            }

            // 跳过"font-weight:"
            startIndex += 12;

            // 找到值的开始位置（跳过空格）
            while (startIndex < style.length() && Character.isWhitespace(style.charAt(startIndex))) {
                startIndex++;
            }

            // 找到值的结束位置（遇到分号或字符串末尾）
            int endIndex = startIndex;
            while (endIndex < style.length() && style.charAt(endIndex) != ';') {
                endIndex++;
            }

            return style.substring(startIndex, endIndex).trim();
        } catch (Exception e) {
            log.warn("解析font-weight样式时出错: {}", style, e);
            return "";
        }
    }

    /**
     * 判断font-weight值是否表示粗体
     * 
     * @param fontWeight font-weight的值
     * @return true表示是粗体
     */
    private static boolean isBoldFontWeight(String fontWeight) {
        if (StringUtils.isBlank(fontWeight)) {
            return false;
        }

        fontWeight = fontWeight.trim();

        // 检查关键字
        if ("bold".equals(fontWeight) || "bolder".equals(fontWeight)) {
            return true;
        }

        // 检查数值（700-900被认为是粗体）
        try {
            int weight = Integer.parseInt(fontWeight);
            return weight >= 700;
        } catch (NumberFormatException e) {
            // 不是数字，继续其他检查
        }

        // 可以根据需要添加更多的粗体判断逻辑
        return false;
    }

    /**
     * 解析元素的CSS颜色
     * 
     * @param element 要解析的元素
     * @return 解析到的Notion颜色，未找到时返回null
     */
    private static ColorEnum parseCSSColor(Element element) {
        // 1. 检查内联样式中的color属性
        String style = element.attr("style");
        if (StringUtils.isNotBlank(style)) {
            ColorEnum styleColor = ColorMapper.parseColorFromStyle(style);
            if (styleColor != null) {
                return styleColor;
            }
        }

        // 2. 检查特定标签的默认颜色映射
        // 某些HTML标签可能有默认的语义颜色
        return getSemanticColor(element);
    }

    /**
     * 获取HTML标签的语义颜色
     * 
     * @param element HTML元素
     * @return 对应的语义颜色，无特殊语义时返回null
     */
    private static ColorEnum getSemanticColor(Element element) {
        String tagName = element.nodeName().toLowerCase();

        // 根据HTML标签的语义定义颜色
        switch (tagName) {
            case "a":
                // 链接通常是蓝色的
                if (StringUtils.isNotBlank(element.attr("href"))) {
                    return ColorEnum._blue;
                }
                break;
            case "mark":
                // mark标签已经在calculateAnnotations中处理了背景色
                // 这里不设置前景色，避免冲突
                break;
            case "code":
            case "samp":
                // 代码通常用不同颜色显示，但这里保持默认
                break;
            default:
                // 其他标签无特殊颜色语义
                break;
        }

        return null;
    }

    private static TextTagProperty currentTextTag(Element element) {
        // 记录当前标签属性
        TextTagProperty currentTextTag = new TextTagProperty();
        currentTextTag.setColor(ColorEnum._default.getColor());
        int annotations = TextTagResolver.calculateAnnotations(element, currentTextTag);
        currentTextTag.setAnnotations(annotations);
        return currentTextTag;
    }
}
