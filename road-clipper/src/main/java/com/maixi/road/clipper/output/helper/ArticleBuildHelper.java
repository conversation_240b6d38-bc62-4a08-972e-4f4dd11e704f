package com.maixi.road.clipper.output.helper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.ArticleFieldDTO;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.ArticleFieldEnum;
import com.maixi.road.common.integration.notion.enums.NotionTypeEnum;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.integration.notion.model.page.Icon;
import com.maixi.road.common.integration.notion.model.property.Date;
import com.maixi.road.common.integration.notion.model.property.MultiSelect;
import com.maixi.road.common.integration.notion.model.property.Name;
import com.maixi.road.common.integration.notion.model.property.Select;
import com.maixi.road.common.integration.notion.model.property.Url;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ArticleBuildHelper {

    private static final Pattern icon_type_pattern = Pattern
            .compile("(JPEG|jpeg|JPG|jpg|gif|GIF|svg|SVG|PNG|png|tif|TIF|tiff|TIFF|bmp|BMP|heic|HEIC)");

    /**
     * 构建图标
     *
     * @param iconUrl
     * @param origin
     * @return
     */
    public static Icon buildIcon(String iconUrl, String origin) {
        if (StringUtils.isNotBlank(iconUrl)) {
            if (iconUrl.startsWith("http://") || iconUrl.startsWith("https://")) {
                Matcher matcher = icon_type_pattern.matcher(iconUrl);
                if (matcher.find()) {
                    return Icon.buildByFile(iconUrl);
                }
            }
        }
        return NotionConstants.icon(origin);
    }

    /**
     * 构建文章属性
     *
     * @param articleConfig
     * @param articleParam
     * @return
     */
    public static JSONObject propertiesBuild(ArticleFieldDTO articleConfig, ResolveFormRQ articleParam) {
        if (articleConfig == null) {
            return defaultProperty(articleParam);
        }
        JSONObject paramJson = JSON.parseObject(JSON.toJSONString(articleParam));
        return propertyByConfig(articleConfig, paramJson);
    }

    /**
     * 构建默认属性
     *
     * @param articleParam
     * @return
     */
    private static JSONObject defaultProperty(ResolveFormRQ articleParam) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Name", Name.simpleName(articleParam.getTitle()));
        jsonObject.put("创建时间", Date.buildOnlyStart(LocalDateTime.now().toString()));
        jsonObject.put("文章链接", Url.build(articleParam.getLink()));
        if (StringUtils.isNotBlank(articleParam.getAuthor())) {
            jsonObject.put("作者", Select.build(articleParam.getAuthor()));
        }
        if (!CollectionUtils.isEmpty(articleParam.getTags())) {
            jsonObject.put("标签", MultiSelect.build(articleParam.getTags()));
        }
        return jsonObject;
    }

    /**
     * 根据配置构建属性
     *
     * @param articleFieldDTO
     * @param paramObject
     * @return
     */
    private static JSONObject propertyByConfig(ArticleFieldDTO articleFieldDTO, JSONObject paramObject) {
    
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Name", Name.simpleName(paramObject.getString(ArticleFieldEnum.TITLE.getField())));

        // 创建时间
        if (articleFieldDTO.getCreateTime() != null && articleFieldDTO.getCreateTime().getName() != null) {
            if (NotionTypeEnum.created_time.getType().equals(articleFieldDTO.getCreateTime().getType())) {
                jsonObject.put(articleFieldDTO.getCreateTime().getName(),
                        Date.buildOnlyStart(LocalDateTime.now().toString()));
            } else {
                log.error("create time type not support");
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "创建时间类型不支持");
            }
        }

        // 作者字段存在，参数中存在，按照作者字段类型组装参数.
        if (articleFieldDTO.getAuthor() != null && articleFieldDTO.getAuthor().getName() != null
                && paramObject.getString(ArticleFieldEnum.AUTHOR.getField()) != null) {
            if (NotionTypeEnum.select.getType().equals(articleFieldDTO.getAuthor().getType())) {
                jsonObject.put(articleFieldDTO.getAuthor().getName(), Select.build(paramObject.getString(ArticleFieldEnum.AUTHOR.getField())));
            } else if (NotionTypeEnum.rich_text.getType().equals(articleFieldDTO.getAuthor().getType())) {
                jsonObject.put(articleFieldDTO.getAuthor().getName(),
                        RichText.textForProperty(paramObject.getString(ArticleFieldEnum.AUTHOR.getField())));
            } else {
                log.error("author type not support");
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "作者类型不支持");
            }
        }

        // 标签字段存在，参数中存在，按照标签字段类型组装参数.
        if (articleFieldDTO.getTags() != null && articleFieldDTO.getTags().getName() != null
                && paramObject.getJSONArray(ArticleFieldEnum.TAGS.getField()) != null && paramObject.getJSONArray(ArticleFieldEnum.TAGS.getField()).size() > 0) {
            if (NotionTypeEnum.multi_select.getType().equals(articleFieldDTO.getTags().getType())) {
                List<String> tags = paramObject.getJSONArray(ArticleFieldEnum.TAGS.getField()).toJavaList(String.class);
                jsonObject.put(articleFieldDTO.getTags().getName(), MultiSelect.build(tags));
            } else {
                log.error("tags type not support");
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "标签类型不支持");
            }
        }
        // 来源
        if (articleFieldDTO.getOrigin() != null && articleFieldDTO.getOrigin().getName() != null
                && paramObject.getString(ArticleFieldEnum.ORIGIN.getField()) != null) {
            if (NotionTypeEnum.select.getType().equals(articleFieldDTO.getOrigin().getType())) {
                jsonObject.put(articleFieldDTO.getOrigin().getName(), Select.build(paramObject.getString(ArticleFieldEnum.ORIGIN.getField())));
            } else if (NotionTypeEnum.rich_text.getType().equals(articleFieldDTO.getOrigin().getType())) {
                jsonObject.put(articleFieldDTO.getOrigin().getName(),
                        RichText.textForProperty(paramObject.getString(ArticleFieldEnum.ORIGIN.getField())));
            } else {
                log.error("origin type not support");
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "来源类型不支持");
            }
        }
        
        // 发布时间
        if (articleFieldDTO.getPublishTime() != null && articleFieldDTO.getPublishTime().getName() != null
                && paramObject.getString(ArticleFieldEnum.PUBLISH_TIME.getField()) != null) {
            if (NotionTypeEnum.date.getType().equals(articleFieldDTO.getPublishTime().getType())) {
                jsonObject.put(articleFieldDTO.getPublishTime().getName(),
                        Date.buildOnlyStart(paramObject.getString(ArticleFieldEnum.PUBLISH_TIME.getField())));
            } else {
                log.error("publish time type not support");
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "发布时间类型不支持");
            }
        }
        // 文章链接
        if (articleFieldDTO.getUrl() != null && articleFieldDTO.getUrl().getName() != null
                && paramObject.getString(ArticleFieldEnum.URL.getField()) != null) {
            if (NotionTypeEnum.url.getType().equals(articleFieldDTO.getUrl().getType())) {
                jsonObject.put(articleFieldDTO.getUrl().getName(), Url.build(paramObject.getString(ArticleFieldEnum.URL.getField())));
            } else if (NotionTypeEnum.rich_text.getType().equals(articleFieldDTO.getUrl().getType())) {
                jsonObject.put(articleFieldDTO.getUrl().getName(),
                        RichText.textForProperty(paramObject.getString(ArticleFieldEnum.URL.getField())));
            } else {
                log.error("url type not support");
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "文章链接类型不支持");
            }
        }
        // 备注
        if (articleFieldDTO.getRemark() != null && articleFieldDTO.getRemark().getName() != null
                && paramObject.getString(ArticleFieldEnum.REMARK.getField()) != null) {
            if (NotionTypeEnum.rich_text.getType().equals(articleFieldDTO.getRemark().getType())) {
                jsonObject.put(articleFieldDTO.getRemark().getName(),
                        RichText.textForProperty(paramObject.getString(ArticleFieldEnum.REMARK.getField())));
            } else {
                log.error("remark type not support");
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "备注类型不支持");
            }
        }
        return jsonObject;
    }
}
