package com.maixi.road.clipper.service.tagresolver;

import java.util.List;

import org.jsoup.nodes.Node;

import com.maixi.road.common.integration.notion.model.block.Block;

/**
 * 段落标签解析器，处理 <p> 标签
 */
public class ParagraphTagResolver implements ContentTagResolver {
    /**
     * 判断是否为段落标签
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "p".equalsIgnoreCase(node.nodeName());
    }

    /**
     * 解析段落标签为 Block
     * @param element DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        return null;
    }
}
