package com.maixi.road.clipper.service.parser.impl.general.resolver;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson.JSON;
import com.maixi.road.clipper.service.parser.impl.general.dto.TitleRule;
import com.maixi.road.common.core.model.dto.PropertyRuleDTO;

public class UniversalTitleResolver {

    public static String parseTitle(Document document, List<PropertyRuleDTO> list){
        String title = null;
        
        // 遍历规则，尝试提取标题
        for (PropertyRuleDTO titleRule : list) {
            // 将规则表达式解析为标题规则对象
            TitleRule rule = JSON.parseObject(titleRule.getExpression(), TitleRule.class);
            
            // 根据规则类型选择不同的提取方式
            switch (rule.getType()) {
                case "tag":
                    // 通过标签名查找元素
                    Elements elementsByTag = document.getElementsByTag(rule.getExpr());
                    title = extractVal(rule.getGetVal(), elementsByTag);
                    break;
                case "cssQuery":
                    // 通过CSS选择器查找元素
                    Elements selectElements = document.select(rule.getExpr());
                    title = extractVal(rule.getGetVal(), selectElements);
                    break;
                default:
                    break;
            }
            
            // 如果找到标题，立即返回
            if (StringUtils.isNoneBlank(title)) {
                break;
            }
        }
        return title;
    }

    private static String extractVal(String getVal, Elements elements) {
        if ("text".equals(getVal)) {
            // 提取元素的文本内容
            return Optional.ofNullable(elements.first()).map(Element::text).orElse(null);
        } else if ("attr.content".equals(getVal)) {
            // 提取元素的content属性值
            return Optional.ofNullable(elements.first()).map(e -> e.attr("content")).orElse(null);
        } else if ("attr.href".equals(getVal)) {
            // 提取元素的href属性值
            return Optional.ofNullable(elements.first()).map(e -> e.attr("href")).orElse(null);
        }
        return null;
    }
}
