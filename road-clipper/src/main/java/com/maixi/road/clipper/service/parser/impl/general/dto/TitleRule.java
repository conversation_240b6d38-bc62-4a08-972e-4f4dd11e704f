package com.maixi.road.clipper.service.parser.impl.general.dto;

import lombok.Data;

@Data
public class TitleRule {
    /**
     * tag,cssQuery
     */
    private String type;
    /**
     * title,h1
     * meta[property="og:title"]
     * h1[class="article-title"]
     * meta[name="og:title"]
     * h1[class="article_title"]
     * h1[class="article__bd__title"]
     */
    private String expr;
    /**
     * text,
     * attr.content
     */
    private String getVal;
}
