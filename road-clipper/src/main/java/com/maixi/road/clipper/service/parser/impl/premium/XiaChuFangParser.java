package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.clipper.service.parser.impl.premium.dto.MenuMaterial;
import com.maixi.road.clipper.service.parser.impl.premium.dto.MenuStep;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Heading;
import com.maixi.road.common.integration.notion.model.block.Image;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.block.Table;
import com.maixi.road.common.integration.notion.model.block.TableRow;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("xiaChuFangParser")
public class XiaChuFangParser extends AbstractParser {

    private static final Pattern timePattern = Pattern.compile(".*?(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}).*?");

    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private ConfigQueryApi configQueryApi;

    @Override
    public boolean supports(String url) {
        return url.contains("xiachufang.com/recipe");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);

        Map<String, List<MenuMaterial>> menuMaterials = parseMaterials(document);
        log.info("menuMaterials={}", JSONObject.toJSONString(menuMaterials));
        Map<String, List<MenuStep>> menuSteps = parseSteps(document);
        log.info("menuSteps={}", JSONObject.toJSONString(menuSteps));
        List<Block> blocks = Lists.newArrayList();

        if (!CollectionUtils.isEmpty(menuMaterials)) {
            for (Map.Entry<String, List<MenuMaterial>> entry : menuMaterials.entrySet()) {
                String key = entry.getKey();
                List<MenuMaterial> value = entry.getValue();
                blocks.add(Block.heading_1(Heading.buildHeading(key)));
                List<JSONObject> rows = Lists.newArrayList();
                for (MenuMaterial material : value) {
                    List<RichText> nameRichTexts = Lists.newArrayList(RichText.simpleText(material.getName()));
                    List<RichText> useageRichTexts = Lists.newArrayList(RichText.simpleText(material.getUseage()));
                    List<List<RichText>> richTextList = Lists.newArrayList(nameRichTexts, useageRichTexts);
                    JSONObject row = TableRow.buildRow(richTextList);
                    rows.add(row);
                }
                Table table = Table.builder()
                        .table_width(2)
                        .has_column_header(false)
                        .has_row_header(false)
                        .children(rows)
                        .build();
                blocks.add(Block.table(table));
            }
        }

        if (!CollectionUtils.isEmpty(menuSteps)) {
            for (Map.Entry<String, List<MenuStep>> entry : menuSteps.entrySet()) {
                String key = entry.getKey();
                List<MenuStep> value = entry.getValue();
                blocks.add(Block.heading_1(Heading.buildHeading(key)));
                for (MenuStep step : value) {
                    blocks.add(Block.heading_2(Heading.buildHeading(step.getTitle())));
                    blocks.add(
                            Block.paragraph(RichTexts.build(Lists.newArrayList(RichText.simpleText(step.getDesc())))));
                    if (StringUtils.isNotBlank(step.getUrl())) {
                        blocks.add(Block.image(new Image(step.getUrl())));
                    }
                }
            }
        }
        return blocks;
    }

    private Map<String, List<MenuMaterial>> parseMaterials(Document document) {
        Element section = document.getElementById("ings");
        if (section == null) {
            log.warn("没有找到食材列表 section");
            return new HashMap<>();
        }
        // 从 section 中获取 h3 标签内容
        Element titleH3 = section.selectFirst("h3.sub-title");
        String sectionTitle = titleH3 != null ? titleH3.text() : "用料";

        Element div = section.getElementsByClass("recipe-ingredient").first();
        if (div == null) {
            log.warn("食材表格格式异常");
            return new HashMap<>();
        }

        Elements materialsItems = div.getElementsByTag("a");
        List<MenuMaterial> menuMaterials = Lists.newArrayList();
        for (Element item : materialsItems) {
            Element nameElement = item.getElementsByClass("ing-name").first();
            Element unitElement = item.getElementsByClass("ing-amount").first();
            String name = nameElement != null ? nameElement.text() : "";
            String unit = unitElement != null ? unitElement.text() : "";
            menuMaterials.add(new MenuMaterial(name, unit));
        }
        Map<String, List<MenuMaterial>> map = new HashMap<>();
        map.put(sectionTitle, menuMaterials);
        return map;
    }

    private Map<String, List<MenuStep>> parseSteps(Document document) {
        Element section = document.getElementById("steps");
        if (section == null) {
            log.warn("没有找到步骤 section");
            return new HashMap<>();
        }
        // 从 section 中获取 h3 标签内容
        Element titleH3 = section.selectFirst("h3.sub-title");
        String stepsTitle = titleH3 != null ? titleH3.text() : "制作步骤";

        Element steps = section.selectFirst("div.recipe-steps");
        Elements stepsItems = null;
        if (steps == null) {
            log.warn("菜谱导入失败，没有找到菜谱步骤");
            stepsItems = new Elements();
        } else {
            stepsItems = steps.getElementsByClass("step step");
        }
        List<MenuStep> stepDescriptions = Lists.newArrayList();
        int stepIndex = 1;
        for (Element item : stepsItems) {
            String title = Optional.ofNullable(item.selectFirst("div.sub-title"))
                    .map(Element::text).orElse("步骤" + stepIndex);
            String text = item.getElementsByTag("p").text();
            String img = item.getElementsByTag("img").attr("src");
            stepDescriptions.add(new MenuStep(title, text, img));
            stepIndex++;
        }
        Map<String, List<MenuStep>> map = new HashMap<>();
        map.put(stepsTitle, stepDescriptions);
        return map;
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.XCF.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_XCF;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_XCF;
    }

    @Override
    protected String parsePublishTime(Document document) {
        // 查找 recipe-time class 的 div 元素
        Element timeElement = document.selectFirst("div[class=recipe-time]");
        if (timeElement != null) {
            String text = timeElement.text();
            Matcher matcher = timePattern.matcher(text);
            if (matcher.find()) {
                String time = matcher.group(1);
                try {
                    return time;
                } catch (Exception e) {
                    log.error("时间格式解析失败,url={},time={}", document.location(), timeElement.text());
                }
            }
        }
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    protected String parseAuthor(Document document) {
        Element authorElement = document.selectFirst("a[class=\"author-name\"]");
        String authorText = Optional.ofNullable(authorElement).map(Element::text).orElse(null);
        if (StringUtils.isNoneBlank(authorText)) {
            return authorText.trim().replace("&nbsp;", "");
        }
        log.error("作者内容解析失败,url={}", document.location());
        return "未知";
    }

    @Override
    protected String parseCover(Document document) {
        Elements elements = document.select("div[class=\"db\"]");
        String imgUrl = elements.stream()
                .findFirst()
                .map(e -> e.getElementsByTag("img").stream().findFirst().map(img -> img.attr("src")).orElse(null))
                .orElse(null);
        if (StringUtils.isBlank(imgUrl)) {
            return defaultHeadImgUrl();
        }
        return imgUrl;
    }

    @Override
    protected String parseTitle(Document document) {
        // 从h1标签中获取class包含recipe-name的元素
        Element nameElement = document.selectFirst("h1.recipe-name");
        return Optional.ofNullable(nameElement)
                .map(Element::text)
                .orElse("美味菜谱");
    }

    private static final String userAgent = "Mozilla/5.0 (Linux; Android 13; SM-S908B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36";

    @Override
    protected Document fetchDocument(String url) throws ClipperException {
        try {
            Document document = Jsoup.connect(url)
                    .referrer("https://m.xiachufang.com")
                    .userAgent(userAgent)
                    .header("Accept",
                            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                    .header("Accept-Language", "zh-CN,zh;q=0.9")
                    .header("Accept-Encoding", "gzip, deflate, br")
                    .timeout(2000)
                    .get();
            if (document.selectFirst("section#steps.steps") == null) {
                log.error("下厨房解析异常,重试一次, url={}", url);
                document = Jsoup.connect(url)
                        .referrer("https://m.xiachufang.com")
                        .userAgent(userAgent)
                        .header("Accept",
                                "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                        .header("Accept-Language", "zh-CN,zh;q=0.9")
                        .header("Accept-Encoding", "gzip, deflate, br")
                        .timeout(2000)
                        .get();
            }
            return document;
        } catch (Exception e) {
            log.error("访问下厨房链接失败,url={}", url);
        }
        return null;
    }

}
