package com.maixi.road.clipper.output.helper;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.integration.notion.enums.MessageFieldEnum;
import com.maixi.road.common.integration.notion.enums.NotionTypeEnum;
import com.maixi.road.common.integration.notion.model.property.Date;
import com.maixi.road.common.integration.notion.model.property.MultiSelect;
import com.maixi.road.common.integration.notion.model.property.Name;
import com.maixi.road.common.integration.notion.model.property.Select;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
@Slf4j
public class MessageBuildHelper {

    public static JSONObject propertiesBuild(MessageFieldDTO customProperty, String content, MsgTypeEnum msgType,
                                             String[] tags) {
        String shortTitle;
        if (StringUtils.isNoneBlank(content) && content.length() > 100) {
            shortTitle = content.substring(0, 96) + "...";
        } else {
            shortTitle = content;
        }
        if (customProperty == null) {
            return defaultProperty(shortTitle, msgType.getName());
        }
        JSONObject paramJson = new JSONObject()
                .fluentPut(MessageFieldEnum.TITLE.getField(), shortTitle)
                .fluentPut(MessageFieldEnum.TYPE.getField(), msgType.getName())
                .fluentPut(MessageFieldEnum.TAGS.getField(), Lists.newArrayList(tags));

        return propertyByConfig(customProperty, paramJson);
    }

    private static JSONObject propertyByConfig(MessageFieldDTO messageFieldDTO, JSONObject paramJson) {
//        MessageFieldDTO messageFieldDTO = getMessageFieldDTO(customProperty);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Name", Name.simpleName(paramJson.getString(MessageFieldEnum.TITLE.getField())));

        // 创建时间
        if (messageFieldDTO.getCreateTime() != null && StringUtils.isNoneBlank(messageFieldDTO.getCreateTime().getName())) {
            jsonObject.put(messageFieldDTO.getCreateTime().getName(), Date.buildOnlyStart(LocalDateTime.now().toString()));
        }


        // 标签
        if (messageFieldDTO.getTags() != null && StringUtils.isNoneBlank(messageFieldDTO.getTags().getName())
                && paramJson.getJSONArray(MessageFieldEnum.TAGS.getField()) != null) {
            if (NotionTypeEnum.multi_select.getType().equals(messageFieldDTO.getTags().getType())) {
                List<String> tags = paramJson.getJSONArray(MessageFieldEnum.TAGS.getField()).toJavaList(String.class);
                jsonObject.put(messageFieldDTO.getTags().getName(), MultiSelect.build(tags));
            } else {
                log.error("tags type not support");
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "标签类型不支持");
            }
        }

        // 消息类型
        if (messageFieldDTO.getType() != null && StringUtils.isNoneBlank(messageFieldDTO.getType().getName())) {
            if (NotionTypeEnum.select.getType().equals(messageFieldDTO.getType().getType())) {
                jsonObject.put(messageFieldDTO.getType().getName(), Select.build(paramJson.getString(MessageFieldEnum.TYPE.getField())));
            } else {
                log.error("type type not support");
                throw RoadException.create(ErrorCodeEnum.PARAMS_ERROR, "消息类型类型不支持");
            }
        }
        
        return jsonObject;
    }

//    private static MessageFieldDTO getMessageFieldDTO(UserMessage message) {
//        if (StringUtils.isBlank(message.getConfigJson())) {
//            MessageFieldDTO fieldDTO = new MessageFieldDTO();
//            fieldDTO.setDatabaseId(message.getDatabaseId());
//            if (StringUtils.isNotBlank(message.getType())) {
//                fieldDTO.setType(Field.builder().name(message.getType()).type("select").supportTypes(NotionTypeEnum.categoryType()).build());
//            }
//            if (StringUtils.isNotBlank(message.getTag())) {
//                fieldDTO.setTags(Field.builder().name(message.getTag()).type("multi_select").supportTypes(NotionTypeEnum.tagsType()).build());
//            }
//            if (StringUtils.isNotBlank(message.getCreateTime())) {
//                fieldDTO.setCreateTime(Field.builder().name(message.getCreateTime()).type("created_time").supportTypes(NotionTypeEnum.createdTimeType()).build());
//            }
//            return fieldDTO;
//        }
//        return JSON.parseObject(message.getConfigJson(), MessageFieldDTO.class);
//    }

    private static JSONObject defaultProperty(String name, String type) {
        return new JSONObject()
                .fluentPut("Name", Name.simpleName(name))
                .fluentPut("消息类型", Select.build(type))
                .fluentPut("创建时间", Date.buildOnlyStart(LocalDateTime.now().toString()));
    }
}
