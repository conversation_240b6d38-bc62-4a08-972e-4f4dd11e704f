package com.maixi.road.clipper.service.tagresolver;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.tagresolver.weixin.MpImgTagResolver;
import com.maixi.road.common.core.utils.ImageUtil;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Image;

import lombok.extern.slf4j.Slf4j;

/**
 * 图片标签解析器，处理 <img> 标签
 */
@Component("imageTagResolver")
@Slf4j
public class ImageTagResolver implements ContentTagResolver {
    /**
     * 判断是否为图片标签
     * @param node DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "img".equalsIgnoreCase(node.nodeName());
    }

    /**
     * 解析图片标签为 Block
     * @param node DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        String src = element.attr("src");
        if (src.contains("we-emoji")) {
            return Lists.newArrayList();
        }
        String dataSrc = element.attr("data-src");
        if (!dataSrc.isEmpty() && dataSrc.startsWith("http")) {
            src = dataSrc;
        }
        if (StringUtils.isNotBlank(src)) {
            if (src.startsWith("https://mmbiz.qpic.cn/")) {
                String imgType = MpImgTagResolver.analyzerImageType(src);
                String imageSrc = MpImgTagResolver.buildCorrectImageSrc(src, imgType);
                return Lists.newArrayList(Block.image(new Image(ImageUtil.transform2SecureUrl(imageSrc))));
            }
            String[] split = src.split("\\?");
            String imageSrc = split[0];
            if (imageSrc.startsWith("awebp")) {
                imageSrc = imageSrc.replace("awebp", "webp");
            }
            return Lists.newArrayList(Block.image(new Image(imageSrc)));
        }
        return Lists.newArrayList();
    }
}
