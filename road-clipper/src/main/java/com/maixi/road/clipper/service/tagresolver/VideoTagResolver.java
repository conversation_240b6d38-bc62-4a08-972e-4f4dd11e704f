package com.maixi.road.clipper.service.tagresolver;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Video;

import lombok.extern.slf4j.Slf4j;

/**
 * 视频标签解析器，处理 <video> 标签
 */
@Slf4j
@Component("videoTagResolver")
public class VideoTagResolver implements ContentTagResolver {
    /**
     * 判断是否为视频标签
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "video".equalsIgnoreCase(node.nodeName());
    }

    /**
     * 解析视频标签为 Block
     * @param element DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        Element element = (Element) node;
        // 获取视频地址
        String src = element.attr("src");
        if (StringUtils.isBlank(src)) {
            // 判断是否有 source 子标签
            Element sourceElement = element.selectFirst("source");
            if (sourceElement != null) {
                src = sourceElement.attr("src");
            }
            if (StringUtils.isBlank(src)) {
                return null;
            }
        }
        // 组装成video-block
        return Lists.newArrayList(Block.video(new Video(src)));
    }
}
