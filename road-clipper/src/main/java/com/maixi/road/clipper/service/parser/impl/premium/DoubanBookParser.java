package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("doubanBookParser")
public class DoubanBookParser extends AbstractParser {

    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private GlobalElementParser globalElementParser;

    @Override
    public boolean supports(String url) {
        return url.contains("douban.com/book/review")
                || url.contains("book.douban.com/review")
                || url.contains("douban.com/doubanapp/review");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Elements elements = getDocument(url).select("div[class=\"main-bd\"]");
        Element element = elements.first();
        if (element == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "豆瓣书评解析异常");
        }
        return globalElementParser.parseElement(element);
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.DOUBAN_BOOK.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_DOUBAN_BOOK;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_DOUBAN;
    }

    @Override
    protected String parseLink(Document document) {
        Elements elements = document.select("meta[property=\"og:url\"]");
        String url = Optional.ofNullable(elements.first()).map(e -> e.attr("content")).orElse(null);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        return url;
    }

    @Override
    protected String parseCover(Document document) {
        Element metaElement = document.selectFirst("meta[property=\"og:image\"]");
        if (metaElement == null) {
            return defaultHeadImgUrl();
        }
        String imgUrl = metaElement.attr("content");
        if (StringUtils.isNotBlank(imgUrl)) {
            return imgUrl;
        }
        return defaultHeadImgUrl();
    }

    @Override
    protected String parsePublishTime(Document document) {
        Element element = document.selectFirst("div[class=\"main-meta\"]");
        if (element == null) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        Elements span = element.getElementsByTag("span");
        if (span.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        Element firstSpan = span.first();
        String publicTime = firstSpan != null ? firstSpan.text() : "";
        if (StringUtils.isBlank(publicTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return publicTime;
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements header = document.getElementsByTag("header");
        if (header.hasClass("main-hd")) {
            Element firstLink = header.select("a").first();
            return firstLink != null ? firstLink.text() : "未知";
        }
        return "未知";
    }

}
