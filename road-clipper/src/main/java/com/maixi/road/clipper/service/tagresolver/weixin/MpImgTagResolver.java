package com.maixi.road.clipper.service.tagresolver.weixin;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.tagresolver.ImageTagResolver;
import com.maixi.road.common.core.utils.ImageUtil;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Image;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
// TODO @Component("mpImgTagResolver")
public class MpImgTagResolver extends ImageTagResolver {

    private static final Pattern imageTypePattern = Pattern
            .compile("wx_fmt=(JPEG|jpeg|JPG|jpg|gif|GIF|svg|SVG|PNG|png|tif|TIF|tiff|TIFF|bmp|BMP|heic|HEIC)");
    private static final Pattern imageTypePattern2 = Pattern
            .compile("_(JPEG|jpeg|JPG|jpg|gif|GIF|svg|SVG|PNG|png|tif|TIF|tiff|TIFF|bmp|BMP|heic|HEIC)");

    /**
     * 从图片地址中解析出图片类型，如果未解析到，默认设置为jpeg
     *
     * @param imageSrc 图片地址
     * @return 图片类型
     */
    public static String analyzerImageType(String imageSrc) {
        String imgType = null;
        Matcher matcher = imageTypePattern.matcher(imageSrc);
        if (matcher.find()) {
            imgType = matcher.group(1);
        } else {
            Matcher matcher2 = imageTypePattern2.matcher(imageSrc);
            if (matcher2.find()) {
                imgType = matcher2.group(1);
            }
        }
        if (imgType == null) {
            log.error("解析图片地址失败，图片地址：{}", imageSrc);
        }
        return Optional.ofNullable(imgType).orElse("jpeg");
    }

    public static String buildCorrectImageSrc(String imageSrc, String imageType) {
        String[] split = imageSrc.split("\\?");
        String correctImgSrc = split[0];
        if (split.length == 1) {
            correctImgSrc += "." + Optional.ofNullable(imageType).orElse("jpeg");
        } else if (split.length == 2) {
            correctImgSrc += "." + Optional.ofNullable(imageType).orElse("jpeg") + "?" + split[1];
        }
        return correctImgSrc;
    }

    @Override
    public List<Block> resolve(Node element) {

        String attr = element.attr("data-type");
        // 小图标直接丢弃
        if ("svg".equals(attr)) {
            return null;
        }

        // 过滤 135编辑器 的修饰图片
        String w = element.attr("data-w");
        if (StringUtils.isNotBlank(w) && Integer.parseInt(w) < 50) {
            return null;
        }

        // 获取图片地址，微信图片地址从data-src中获取
        String imageSrc = element.attr("data-src");
        if (StringUtils.isBlank(imageSrc)) {
            imageSrc = element.attr("src");
        }

        // 表情丢弃
        if (imageSrc.contains("we-emoji")) {
            return null;
        }

        // 解析图片类型
        String imageType = analyzerImageType(imageSrc);

        // 拼接好真实的图片地址
        String correctImageUrl = buildCorrectImageSrc(imageSrc, imageType);

        // 将图片地址转换成https
        String secureUrl = ImageUtil.transform2SecureUrl(correctImageUrl);

        // 组装成image-block
        return Lists.newArrayList(Block.image(new Image(secureUrl)));
    }
}
