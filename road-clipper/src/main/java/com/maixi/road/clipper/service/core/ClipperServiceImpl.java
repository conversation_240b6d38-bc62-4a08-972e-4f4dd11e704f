package com.maixi.road.clipper.service.core;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.image.ImageProcessService;
import com.maixi.road.clipper.image.ImageUploadHelper;
import com.maixi.road.clipper.output.NotionOutputService;
import com.maixi.road.clipper.service.parser.ContentParser;
import com.maixi.road.clipper.service.parser.factory.ParserFactory;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import com.maixi.road.common.core.enums.ThreadLocalKeys;
import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;
import com.maixi.road.common.core.exception.ClipperException;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.core.model.dto.Result;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.business.wechat.enums.MsgTypeEnum;
import com.maixi.road.common.core.utils.ThreadLocalUtils;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.page.Page;
import com.maixi.road.common.integration.notion.model.page.Parent;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.common.service.redis.api.RedisOpApi;
import com.maixi.road.markdown.dto.MarkdownSaveRQ;
import com.maixi.road.markdown.dto.MarkdownSaveRS;
import com.maixi.road.markdown.service.MarkdownOutputService;
import com.maixi.road.notion.remote.dto.request.PageCreateRQ;
import com.maixi.road.notion.remote.NotionClient;
import com.maixi.road.notion.remote.manager.ParagraphBlockBuilder;
import com.maixi.road.clipper.output.helper.MessageBuildHelper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 剪藏服务实现
 */
@Slf4j
@Service
public class ClipperServiceImpl implements ClipperCoreService {

    @Resource
    private ParserFactory parserFactory;
    @Resource
    private ImageProcessService imageProcessService;
    @Resource
    private NotionOutputService notionOutputService;
    @Resource
    private MarkdownOutputService markdownOutputService;
    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private RedisOpApi redisOpApi;
    @Resource
    private NotionClient notionClient;

    // 用于存储异步正文解析任务的Future，使用虚拟线程提高I/O密集型任务性能
    private final ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 解析URL，只同步返回basicResult，正文和正文图片异步处理
     *
     * @param url    文章URL
     * @param userId 用户ID
     * @return 文章基础属性
     */
    @Override
    public ResolveRS parseUrl(String url, String userId) {
        if (StringUtils.isBlank(url)) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "URL不能为空");
        }
        if (StringUtils.isBlank(userId)) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "用户ID不能为空");
        }
        try {
            log.info("开始解析URL, url={}, userId={}", url, userId);
            // 获取合适的解析器
            ContentParser parser = parserFactory.getParser(url);
            // 只解析基本信息
            ResolveRS basicResult = parser.parseBasicInfo(url, userId);

            // 异步解析正文和正文图片
            executor.execute(() -> {
                try {
                    // 使用TransmittableThreadLocal存储解析结果，支持线程间传递
                    ThreadLocalUtils.set(ThreadLocalKeys.CLIPPER_ORIGIN.getKey(), basicResult.getOrigin());
                    log.debug("设置ThreadLocal解析结果, url={}, userId={}", url, userId);

                    List<Block> list = parser.parseContent(url, userId);
                    basicResult.setBlocks(list);
                    processImages(basicResult, userId);
                    redisOpApi.saveClipResult(basicResult.getLink(), basicResult.getBlocks());
                } catch (Exception e) {
                } finally {
                    // 清理ThreadLocal，防止内存泄漏
                    ThreadLocalUtils.remove(ThreadLocalKeys.CLIPPER_ORIGIN.getKey());
                }
            });
            log.info("URL基本信息解析完成, url={}, title={}", url, basicResult.getTitle());
            return basicResult;
        } catch (ClipperException e) {
            log.error("URL解析异常, url={}, userId={}, error={}", url, userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("URL解析异常, url={}, userId={}", url, userId, e);
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "解析失败: " + e.getMessage());
        }
    }

    /**
     * 提交文章时，阻塞等待正文解析结果，超时10秒
     *
     * @param article 文章参数
     * @param userId  用户ID
     * @return 是否成功
     */
    @Override
    public boolean saveContent(ResolveFormRQ article, String userId) {
        if (article == null) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "文章参数不能为空");
        }
        if (StringUtils.isBlank(userId)) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "用户ID不能为空");
        }
        if (StringUtils.isBlank(article.getLink())) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "文章链接不能为空");
        }
        if (StringUtils.isBlank(article.getTitle())) {
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "文章标题不能为空");
        }
        try {
            log.info("开始提交文章, title={}, link={}, userId={}", article.getTitle(), article.getLink(), userId);
            // 阻塞等待正文内容，依赖内部超时机制（10秒）
            Future<List<Block>> future = redisOpApi.getClipResult(article.getLink());
            if (future != null) {
                try {
                    // 内部异步任务会在10秒后返回空列表（如果未获取到数据）
                    List<Block> blocks = future.get();
                    if (!CollectionUtils.isEmpty(blocks)) {
                        article.setBlocks(blocks);
                        log.info("成功获取正文解析结果, link={}, userId={}, blocks数量={}",
                                article.getLink(), userId, blocks.size());
                    } else {
                        log.warn("正文解析结果为空（可能超时），link={}, userId={}", article.getLink(), userId);
                        article.setBlocks(Lists.newArrayList());
                    }
                } catch (Exception e) {
                    log.error("正文解析异常, link={}, userId={}", article.getLink(), userId, e);
                    throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "正文解析失败: " + e.getMessage());
                }
            }

            UserConfig userConfig = configQueryApi.queryConfig(userId);

            // 根据输出类型决定输出到哪里
            String outputType = userConfig.getOutputType();
            if ("markdown".equalsIgnoreCase(outputType)) {
                // 输出到 Markdown
                MarkdownSaveRQ markdownRequest = MarkdownSaveRQ.builder()
                        .unionId(userId)
                        .userForm(article)
                        .savePath(userConfig.getMarkdownSavePath())
                        .createAssetsDir(true)
                        .downloadImages(true)
                        .includeMetadata(true)
                        .obConfig(userConfig.getObConfig())
                        .isArticle(true)
                        .build();
                Result<MarkdownSaveRS> markdownResult = markdownOutputService.saveArticle(markdownRequest);
                if (markdownResult.isSuccess()) {
                    log.info("文章已保存为 Markdown 文件, title={}, link={}, userId={}, filePath={}",
                            article.getTitle(), article.getLink(), userId, markdownResult.getData().getFilePath());
                    return true;
                }
                throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, markdownResult.getMsg());
            } else {
                // 默认输出到 Notion
                PageCreateRQ createRequest = PageCreateRQ.builder()
                        .unionId(userId)
                        .userForm(article)
                        .blockSizeLimit(configQueryApi.gloablConfig().getBlockSizeLimit())
                        .noCover(userConfig.getNoCover() == 1)
                        .noIcon(userConfig.getNoIcon() == 1)
                        .build();
                Result<PageCreateRS> createResult = notionOutputService.saveArticle(createRequest);
                if (createResult.isSuccess()) {
                    log.info("文章已保存到 Notion, title={}, link={}, userId={}", article.getTitle(), article.getLink(),
                            userId);
                    return true;
                }
                throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, createResult.getMsg());
            }
        } catch (ClipperException e) {
            log.error("文章提交失败, title={}, link={}, userId={}, error={}", article.getTitle(), article.getLink(), userId,
                    e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("文章提交异常, title={}, link={}, userId={}", article.getTitle(), article.getLink(), userId, e);
            throw ClipperException.create(ClipperErrCodeEnum.CLIPPER_ERROR, "提交失败: " + e.getMessage());
        }
    }

    /**
     * 处理图片
     *
     * @param result 解析结果
     * @param userId 用户ID
     */
    private void processImages(ResolveRS result, String userId) {
        if (result == null) {
            return;
        }

        // 处理封面图片
        if (StringUtils.isNotBlank(result.getCover())) {
            try {
                String processedCover = imageProcessService.processImage(result.getCover(), userId);
                if (StringUtils.isNotBlank(processedCover)) {
                    result.setCover(processedCover);
                }
            } catch (Exception e) {
                log.warn("处理封面图片失败, cover={}, userId={}", result.getCover(), userId, e);
            }
        }

        // 处理内容中的图片
        Set<String> imgList = new HashSet<>();
        if (!CollectionUtils.isEmpty(result.getBlocks())) {
            imgList = ImageUploadHelper.extractImgList(result, result.getBlocks());
        }
        Map<String, String> urlMap = imageProcessService.processImages(result.getLink(), Lists.newArrayList(imgList),
                userId);
        ImageUploadHelper.replaceImageBlock(result, result.getBlocks(), urlMap);
    }

    /**
     * 保存文本消息到Notion和Markdown（根据用户配置）
     * 
     * @param content 消息内容
     * @param tags    标签数组
     * @param userId  用户ID
     * @return 保存结果
     */
    @Override
    public String saveTextMessage(String content, String[] tags, String userId) {
        try {
            // 获取用户配置
            UserConfig userConfig = configQueryApi.queryConfig(userId);

            // 获取消息字段配置
            MessageFieldDTO messageFieldDTO = configQueryApi.getMessageFieldDTO(userId);

            // 根据输出类型决定输出到哪里
            String outputType = userConfig.getOutputType();

            // 根据配置决定保存目标
            boolean saveToNotion = "notion".equalsIgnoreCase(outputType) || "both".equalsIgnoreCase(outputType)
                    || StringUtils.isBlank(outputType);
            boolean saveToMarkdown = "markdown".equalsIgnoreCase(outputType) || "both".equalsIgnoreCase(outputType);

            boolean notionSuccess = false;
            boolean markdownSuccess = false;

            // 保存到Notion
            if (saveToNotion) {
                log.info("保存消息到Notion, content={}, userId={}", content, userId);
                // 构建Notion页面
                Page page = buildMessageNotionPage(messageFieldDTO, content, tags);
                notionSuccess = saveMessageToNotion(messageFieldDTO, page, userId);
            }

            // 保存到Markdown
            if (saveToMarkdown) {
                log.info("保存消息到Markdown, content={}, userId={}", content, userId);
                SaveMarkdownResult markdownSaveResult = saveMessageToMarkdown(content, tags, userId, userConfig);
                markdownSuccess = markdownSaveResult.isSuccess();
            }

            // 生成并返回保存结果
            return generateSaveResult(saveToNotion, saveToMarkdown, notionSuccess, markdownSuccess);
        } catch (Exception e) {
            log.error("保存消息异常, content={}, userId={}", content.substring(0, Math.min(content.length(), 20)), userId, e);
            return "保存失败: " + e.getMessage();
        }
    }

    /**
     * 根据保存结果生成响应消息
     * 
     * @param saveToNotion    是否保存到Notion
     * @param saveToMarkdown  是否保存到Markdown
     * @param notionSuccess   Notion保存是否成功
     * @param markdownSuccess Markdown保存是否成功
     * @return 保存结果消息
     */
    private String generateSaveResult(boolean saveToNotion, boolean saveToMarkdown,
            boolean notionSuccess, boolean markdownSuccess) {
        if (saveToNotion && saveToMarkdown) {
            if (notionSuccess && markdownSuccess) {
                return "保存成功，已同步到Notion和Markdown";
            } else if (notionSuccess) {
                return "部分保存成功，已同步到Notion，但Markdown保存失败";
            } else if (markdownSuccess) {
                return "部分保存成功，已同步到Markdown，但Notion保存失败";
            } else {
                return "保存失败，Notion和Markdown均保存失败";
            }
        } else if (saveToNotion) {
            return notionSuccess ? "保存成功，已同步到Notion" : "保存失败";
        } else if (saveToMarkdown) {
            return markdownSuccess ? "保存成功，已同步到Markdown" : "保存失败";
        } else {
            return "未配置任何输出目标，保存失败";
        }
    }

    /**
     * 保存消息到Markdown
     * 
     * @param content    消息内容
     * @param tags       标签数组
     * @param userId     用户ID
     * @param userConfig 用户配置
     * @return Markdown保存结果
     */
    private SaveMarkdownResult saveMessageToMarkdown(String content, String[] tags, String userId,
            UserConfig userConfig) {
        String title = content.substring(0, Math.min(content.length(), 100));
        ArrayList<Block> blocks = Lists
                .newArrayList(ParagraphBlockBuilder.singleParagraphWithSingleDefaultRichText(content));
        try {
            // 创建一个简单的ResolveFormRQ对象来包含消息内容
            ResolveFormRQ messageForm = new ResolveFormRQ();
            messageForm.setTitle(title);
            messageForm.setTags(Arrays.asList(tags));
            messageForm.setCategory("text");
            messageForm.setBlocks(blocks);

            // 构建Markdown保存请求
            MarkdownSaveRQ markdownRequest = MarkdownSaveRQ.builder()
                    .unionId(userId)
                    .userForm(messageForm)
                    .savePath(userConfig.getMarkdownSavePath())
                    .createAssetsDir(false) // 消息不需要资源目录
                    .downloadImages(false) // 消息不需要下载图片
                    .includeMetadata(true) // 包含元数据
                    .obConfig(userConfig.getObConfig())
                    .isArticle(false)
                    .build();

            // 保存到Markdown，使用现有的saveArticle方法
            Result<MarkdownSaveRS> markdownResult = markdownOutputService.saveArticle(markdownRequest);
            if (markdownResult.isSuccess()) {
                String filePath = markdownResult.getData().getFilePath();
                log.info("消息已保存为Markdown文件, title={}, userId={}, filePath={}",
                        title, userId, filePath);
                return new SaveMarkdownResult(true, filePath);
            } else {
                log.error("保存消息到Markdown失败, title={}, userId={}, error={}",
                        title, userId, markdownResult.getMsg());
                return new SaveMarkdownResult(false, "");
            }
        } catch (Exception e) {
            log.error("保存消息到Markdown异常, title={}, userId={}", title, userId, e);
            return new SaveMarkdownResult(false, "");
        }
    }

    /**
     * 构建消息的Notion页面
     * 
     * @param messageFieldDTO 消息字段配置
     * @param content         消息内容
     * @param tags            标签数组
     * @return Notion页面对象
     */
    private Page buildMessageNotionPage(MessageFieldDTO messageFieldDTO, String content, String[] tags) {
        List<Block> blocks = Lists.newArrayList();
        // TODO 这里一个 block 不一定够用
        Block paragraph = ParagraphBlockBuilder.singleParagraphWithSingleDefaultRichText(content);
        blocks.add(paragraph);

        return Page.builder()
                .parent(Parent.build(messageFieldDTO.getDatabaseId()))
                .properties(MessageBuildHelper.propertiesBuild(messageFieldDTO, content, MsgTypeEnum.TEXT, tags))
                .children(blocks)
                .build();
    }

    /**
     * 保存消息到Notion
     * 
     * @param messageFieldDTO 消息字段配置
     * @param page            Notion页面
     * @param userId          用户ID
     * @return 是否保存成功
     */
    private boolean saveMessageToNotion(MessageFieldDTO messageFieldDTO, Page page, String userId) {
        try {
            notionClient.createPageWithRetry(messageFieldDTO.getAccessToken(), page, userId);
            return true;
        } catch (InterruptedException e) {
            log.error("保存消息到Notion失败, content={}, userId={}",
                    page.getChildren().getFirst().getParagraph().getRich_text().getFirst().getText().getContent(),
                    userId, e);
            return false;
        }
    }

    /**
     * Markdown保存结果类
     */
    private static class SaveMarkdownResult {
        private boolean success;

        public SaveMarkdownResult(boolean success, String filePath) {
            this.success = success;
            // filePath参数保留，但不再使用
        }

        public boolean isSuccess() {
            return success;
        }
    }

}
