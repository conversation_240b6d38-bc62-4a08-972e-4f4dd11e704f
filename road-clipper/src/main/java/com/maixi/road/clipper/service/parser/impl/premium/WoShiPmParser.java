package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("woShiPmParser")
public class WoShiPmParser extends AbstractParser{

    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private ConfigQueryApi configQueryApi;

    
    @Override
    public boolean supports(String url) {
        return url.contains("woshipm.com");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);
        Elements elements = document.select("div[class=\"article--content grap\"]");
        Element element = elements.first();
        if (element == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "人人都是产品经理解析异常");
        }
        return globalElementParser.parseElement(element);
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.PM.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_PM;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_PM;
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements elements = document.select("div[class=\"author u-flex\"]");
        if (elements.isEmpty()) {
            return "未知";
        }
        Element first = elements.first();
        if (first == null) {
            return "未知";
        }
        Elements aTag = first.getElementsByTag("a");
        if (aTag.isEmpty()) {
            return "未知";
        }
        return Optional.ofNullable(aTag.first()).map(Element::text).orElse("未知");
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements elements = document.select("div[class=\"meta--sup\"]");
        if (elements.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        for (Element element : elements) {
            Elements time = element.getElementsByTag("time");
            if (!time.isEmpty()) {
                Element timeElement = time.first();
                if (timeElement != null) {
                    String publicDate = timeElement.text();
                    if (StringUtils.isNotBlank(publicDate)) {
                        try {
                            return LocalDate.parse(publicDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        } catch (DateTimeParseException e) {
                            log.warn("Failed to parse date: {}", publicDate, e);
                        }
                    }
                }
            }
        }
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

}
