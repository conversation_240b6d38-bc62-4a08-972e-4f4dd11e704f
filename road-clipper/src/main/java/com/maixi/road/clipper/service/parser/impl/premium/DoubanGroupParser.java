package com.maixi.road.clipper.service.parser.impl.premium;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.clipper.service.parser.GlobalElementParser;
import com.maixi.road.clipper.service.parser.impl.AbstractParser;
import com.maixi.road.clipper.service.tagresolver.ImageTagResolver;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.ColorEnum;
import com.maixi.road.common.integration.notion.enums.OriginTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.integration.notion.model.block.Icon;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.service.config.ConfigQueryApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component("doubanGroupParser")
public class DoubanGroupParser extends AbstractParser {

    @Resource
    private GlobalElementParser globalElementParser;
    @Resource
    private ConfigQueryApi configQueryApi;
    @Resource
    private ImageTagResolver imgTagResolver;

    @Override
    public boolean supports(String url) {
        return url.contains("douban.com/group/topic");
    }

    @Override
    public List<Block> parseContent(String url, String userId) {
        Document document = fetchDocument(url);
        Elements elements = document.select("div[class=\"article\"]");
        Element element = elements.first();
        if (element == null) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, "豆瓣小组解析异常");
        }
        List<Block> blocks = globalElementParser.parseElement(element);
        Element popularComments = document.getElementById("popular-comments");
        if (popularComments != null) {
            List<Block> blockList = new ArrayList<>();
            try {
                List<Block> comments = handleComments(popularComments);
                blockList.addAll(comments);
            } catch (Exception e) {
                log.error("解析豆瓣小组评论异常", e);
            }
            blocks.addAll(blockList);
        }
        Element comments = document.getElementById("comments");
        if (comments != null) {
            List<Block> blockList = new ArrayList<>();
            try {
                List<Block> handleComments = handleComments(comments);
                blockList.addAll(handleComments);
            } catch (Exception e) {
                log.error("解析豆瓣小组评论异常", e);
            }
            blocks.addAll(blockList);
        }
        return blocks;
    }

    private List<Block> handleComments(Element element) {
        Elements children = element.children();
        List<Block> blocks = Lists.newArrayList();
        for (Element item : children) {
            Elements elements = item.getElementsByTag("h4");
            Element first = elements.first();
            if (first != null) {
                Elements name = first.getElementsByTag("a");
                Elements time = first.getElementsByTag("span");
                Elements comment = item.select("div[class=\"reply-content\"]");
                if (comment.isEmpty()) {
                    comment = item.select("p[class=\"reply-content\"]");
                }
                Element commentElement = comment.first();
                if (commentElement != null && StringUtils.isNotBlank(commentElement.text())) {
                    RichText commentText = RichText.simpleText(commentElement.text());
                    RichText nameText = RichText.boldText(name.text() + "\n");
                    RichText timeText = RichText.colorText(time.text() + "\n", ColorEnum._blue);
                    Callout callout = Callout.build(Lists.newArrayList(nameText, timeText, commentText),
                            Icon.emoji("\uD83D\uDCAC"));
                    blocks.add(Block.callout(callout));
                }
            }
            Elements photos = item.select("div[class=\"cmt-img-wrapper\"]");
            if (!photos.isEmpty()) {
                for (Element imgEle : photos) {
                    Elements img = imgEle.getElementsByTag("img");
                    if (!img.isEmpty()) {
                        for (Element element1 : img) {
                            List<Block> imgBlock = imgTagResolver.resolve(element1);
                            blocks.addAll(imgBlock);
                        }
                    }
                }
            }
        }
        return blocks;
    }

    @Override
    protected boolean usePicCloud(String unionId) {
        return configQueryApi.queryConfig(unionId).getAlwaysUsePicCloud() == 1;
    }

    @Override
    protected String parseOrigin(Document document) {
        return OriginTypeEnum.DOUBAN_GROUP.getName();
    }

    @Override
    protected String defaultHeadImgUrl() {
        return NotionConstants.COVER_DOUBAN;
    }

    @Override
    protected String defaultLogoUrl() {
        return NotionConstants.ICON_DOUBAN;
    }

    @Override
    protected String parsePublishTime(Document document) {
        Elements elements = document.select("div[class=\"create-time color-green\"]");
        if (elements.isEmpty()) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        String publicTime = Optional.ofNullable(elements.first()).map(Element::text).orElse(null);
        if (StringUtils.isBlank(publicTime)) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return publicTime;
    }

    @Override
    protected String parseTitle(Document document) {
        Elements elements = document.getElementsByTag("h1");
        if (elements.isEmpty()) {
            return "豆瓣小组-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        return Optional.ofNullable(elements.first()).map(Element::text).orElse(null);
    }

    @Override
    protected String parseAuthor(Document document) {
        Elements elements = document.select("div[class=\"title\"]");
        if (elements.isEmpty()) {
            return "未知";
        }
        Elements aTag = elements.first().getElementsByTag("a");
        if (aTag.isEmpty()) {
            return "未知";
        }
        return Optional.ofNullable(aTag.first()).map(Element::text).orElse("未知");
    }

}
