package com.maixi.road.clipper.service.tagresolver;
import java.util.List;

import org.jsoup.nodes.Node;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.model.block.Block;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class DividerTagResolver implements ContentTagResolver {
    /**
     * 判断是否为分割线标签
     * @param element DOM 元素
     * @return 是否支持
     */
    @Override
    public boolean supports(Node node) {
        return "hr".equalsIgnoreCase(node.nodeName());
    }

    /**
     * 解析分割线标签
     * @param element DOM 元素
     * @return Block 结构
     */
    @Override
    public List<Block> resolve(Node node) {
        return Lists.newArrayList(Block.divider());
    }

}
