package com.maixi.road.clipper.service.parser;

import java.io.IOException;
import java.util.List;

import com.maixi.road.common.core.model.dto.ResolveRS;
import com.maixi.road.common.integration.notion.model.block.Block;

/**
 * 内容解析器接口
 */
public interface ContentParser {
    
    /**
     * 判断是否支持该URL
     * 
     * @param url 要判断的URL
     * @return 是否支持
     */
    boolean supports(String url);


    /**
     * 解析基本信息
     * 
     * @param url 要解析的URL
     * @param userId 用户ID
     * @return 解析结果
     * @throws IOException 如果发生IO错误
     * @throws InterruptedException 如果操作被中断
     */
    ResolveRS parseBasicInfo(String url, String userId);

    /**
     * 解析正文内容
     * 
     * @param url 要解析的URL
     * @param userId 用户ID
     * @return 解析结果
     */
    List<Block> parseContent(String url, String userId);
}
