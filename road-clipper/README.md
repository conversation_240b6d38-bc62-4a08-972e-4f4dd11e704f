# Road Clipper

Road Clipper 是一个用于网页内容剪藏的独立模块，支持多种网站的内容解析和保存。

## 功能特点

- 支持多种网站的内容解析，如微信公众号、小红书、知乎、豆瓣等
- 支持纯文本消息的处理和保存
- 支持图片的转存和处理
- 支持将解析的内容同步到Notion
- 支持为剪藏内容添加标签

## 快速开始

### 添加依赖

```xml
<dependency>
    <groupId>com.maixi.road</groupId>
    <artifactId>road-clipper</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 使用示例

```java
import com.maixi.road.clipper.api.ClipperApi;
import com.maixi.road.clipper.api.dto.request.ArticleRequest;
import com.maixi.road.clipper.api.dto.request.TextMessageRequest;
import com.maixi.road.clipper.api.dto.response.ClipperResponse;
import com.maixi.road.clipper.api.dto.response.ResolveResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/clipper")
public class ClipperController {

    @Autowired
    private ClipperApi clipperApi;

    @PostMapping("/resolve")
    public ClipperResponse<ResolveResult> resolveUrl(@RequestParam String url, @RequestParam String userId) {
        return clipperApi.resolveUrl(url, userId);
    }

    @PostMapping("/submit")
    public ClipperResponse<Boolean> submitArticle(@RequestBody ArticleRequest article, @RequestParam String userId) {
        return clipperApi.submitArticle(article, userId);
    }

    @PostMapping("/sync-message")
    public ClipperResponse<String> syncTextMessage(@RequestBody TextMessageRequest message, @RequestParam String userId) {
        return clipperApi.syncTextMessage(message, userId);
    }
}
```

## 模块结构

```
road-clipper/
├── api/                  # 对外API接口
│   ├── ClipperApi.java   # 剪藏服务API接口
│   └── dto/              # 数据传输对象
│       ├── request/      # 请求对象
│       └── response/     # 响应对象
├── config/               # 配置类
├── constant/             # 常量定义
├── exception/            # 异常处理
├── model/                # 内部数据模型
├── service/              # 服务实现
│   ├── core/             # 核心服务
│   │   ├── ClipperService.java
│   │   ├── TextClipperService.java
│   │   └── ImageProcessService.java
│   ├── parser/           # 解析器
│   │   ├── base/         # 基础解析器
│   │   │   ├── AbstractParser.java
│   │   │   └── ParserTemplate.java
│   │   ├── factory/      # 解析器工厂
│   │   │   └── ParserFactory.java
│   │   └── impl/         # 具体实现
│   │       ├── general/  # 通用解析器
│   │       └── premium/  # 特定网站解析器
│   └── integration/      # 第三方集成
│       └── notion/       # Notion集成
└── util/                 # 工具类
```

## 扩展解析器

要添加新的网站解析器，只需继承 `AbstractParser` 类并实现相应的方法：

```java
@Component
public class CustomSiteParser extends AbstractParser {
    
    public CustomSiteParser(RedissonClient redissonClient) {
        super(redissonClient, Pattern.compile("https?://www\\.customsite\\.com/.*"));
    }
    
    @Override
    protected ResolveResult parseProperties(Document document, String url) {
        // 实现属性解析逻辑
    }
    
    @Override
    protected String parseContent(Document document, String url, String userId, ResolveResult result) {
        // 实现内容解析逻辑
    }
}
```

## 配置

模块使用Spring Boot的自动配置机制，无需额外配置即可使用。如需自定义配置，可以在应用的配置文件中添加相应的属性。

## 依赖

- Spring Boot
- Jsoup
- OkHttp
- Redisson
- S3-Proxy
