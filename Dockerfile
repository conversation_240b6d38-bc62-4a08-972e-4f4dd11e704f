# 第一阶段：构建应用
FROM amazoncorretto:21 AS builder

WORKDIR /app
ARG JAR_FILE=/roadserver-app/target/roadserver.jar
COPY ${JAR_FILE} app.jar
RUN java -Djarmode=layertools -jar app.jar extract && rm app.jar

# 第二阶段：提取分层文件
FROM amazoncorretto:21

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
ENV JAVA_OPTS=" -server -Xmx1024m -Xms256m -Xmn512m -Xss256k"

WORKDIR /app

# 复制分层文件
COPY --from=builder /app/dependencies/ ./
COPY --from=builder /app/spring-boot-loader/ ./
COPY --from=builder /app/snapshot-dependencies/ ./
COPY --from=builder /app/application/ ./

EXPOSE 8080

# 设置启动命令
ENV JAVA_OPTS="-Djava.library.path=/home/<USER>"
ENTRYPOINT ["sh","-c","java $JAVA_OPTS org.springframework.boot.loader.launch.JarLauncher"]