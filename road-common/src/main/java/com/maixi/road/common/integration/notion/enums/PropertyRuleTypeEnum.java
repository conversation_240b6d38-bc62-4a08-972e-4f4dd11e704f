package com.maixi.road.common.integration.notion.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PropertyRuleTypeEnum {

    TITLE(1, "title"),

    DESCRIPTION(2, "description"),

    ICON(3, "icon"),

    COVER(4, "cover"),

    AUTHOR(5, "author"),

    PUBLIC_DATE(6, "public_date"),

    SITE_NAME(7, "site_name"),

    ARTICLE(8, "article"),

    REMOVE_ELEMENT(99, "remove_element"),

    ;

    private final Integer type;
    private final String typeDesc;

    public static PropertyRuleTypeEnum getByType(Integer type) {
        for (PropertyRuleTypeEnum item : PropertyRuleTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return null;
    }
}
