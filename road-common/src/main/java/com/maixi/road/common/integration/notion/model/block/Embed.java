package com.maixi.road.common.integration.notion.model.block;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Embed {
    private List<String> caption;
    private String url;

    public static Embed build(String url) {
        return Embed.builder()
                .url(url)
                .build();
    }
}
