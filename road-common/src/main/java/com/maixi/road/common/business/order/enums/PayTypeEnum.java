package com.maixi.road.common.business.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PayTypeEnum {

    WXPAY(0, "微信支付"),
    ALIPAY(1, "支付宝支付"),
    ;

    private final Integer code;
    private final String desc;

    public static PayTypeEnum explain(Integer code) {
        for (PayTypeEnum payTypeEnum : PayTypeEnum.values()) {
            if (payTypeEnum.getCode().equals(code)) {
                return payTypeEnum;
            }
        }
        return null;
    }
}
