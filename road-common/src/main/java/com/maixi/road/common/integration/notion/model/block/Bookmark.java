package com.maixi.road.common.integration.notion.model.block;

import lombok.Data;

import java.util.Collections;
import java.util.List;

import com.maixi.road.common.integration.notion.model.common.RichText;


@Data
public class Bookmark {
    private List<RichText> caption;
    private String url;

    public Bookmark(String url, String caption) {
        this.url = url;
        this.caption = Collections.singletonList(RichText.simpleText(caption));
    }

    public Bookmark(String url) {
        this.url = url;
    }

    public Bookmark() {
    }
}
