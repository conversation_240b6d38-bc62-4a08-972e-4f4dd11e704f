package com.maixi.road.common.integration.notion.model.page;
import com.alibaba.fastjson.JSONObject;
import com.maixi.road.common.integration.notion.model.block.Block;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Page {

    private Parent parent;
    private Cover cover;
    private Icon icon;
    private JSONObject properties;
    private List<Block> children;

}
