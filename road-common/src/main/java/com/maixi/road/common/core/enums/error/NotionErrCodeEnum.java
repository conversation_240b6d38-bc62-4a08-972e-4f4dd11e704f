package com.maixi.road.common.core.enums.error;

import com.maixi.road.common.core.enums.EnumInterface;

import lombok.Getter;

/**
 * <p>业务级别错误码</p>
 * F13000-F13999
 */
@Getter
public enum NotionErrCodeEnum implements EnumInterface<NotionErrCodeEnum> {

    DEFAULT_ERROR(99999, "Error", "Error"),


    NOTION_CREATE_PAGE_FAILED(60001, "Notion 保存失败"),


    ;

    private final Integer code;
    private final String msg;
    private String desc;

    NotionErrCodeEnum(int code, String message) {
        this.code = code;
        this.msg = message;
    }

    NotionErrCodeEnum(int code, String desc, String message) {
        this(code, message);
        this.desc = desc;
    }


    @Override
    public NotionErrCodeEnum getDefault() {
        return DEFAULT_ERROR;
    }
}
