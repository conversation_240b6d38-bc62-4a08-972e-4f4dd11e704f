package com.maixi.road.common.core.model.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文本消息请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextMessageRequest {
    
    /**
     * 消息内容
     */
    @NotBlank(message = "内容不能为空")
    private String content;
    
    /**
     * 标签列表
     */
    private List<String> tags;
}
