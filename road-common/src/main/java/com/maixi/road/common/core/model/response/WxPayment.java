package com.maixi.road.common.core.model.response;

import lombok.Data;

import java.util.Objects;

import com.maixi.road.common.core.utils.Md5Utils;

@Data
public class WxPayment {

    private String wxTradeId;
    /**
     * 小程序ID
     */
    private String appId;
    /**
     * 时间戳
     */
    private String timeStamp;
    /**
     * 随机字符串
     */
    private String nonceStr;
    /**
     * 订单详情扩展字符串
     */
    private String _package;
    /**
     * 签名方式
     */
    private String signType;

    private String paySign;


    public void sign(String key) {
        String sign = ("appId=" + appId + "&");
        sign += ("nonceStr=" + nonceStr + "&");
        sign += ("package=" + _package + "&");
        sign += ("signType=" + signType + "&");
        sign += ("timeStamp=" + timeStamp + "&");
        sign += ("key=" + key);
        System.out.println("sign=" + sign);
        setPaySign(Objects.requireNonNull(Md5Utils.MD5(sign)).toUpperCase());
    }
}
