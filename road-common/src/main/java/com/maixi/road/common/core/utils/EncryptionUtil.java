package com.maixi.road.common.core.utils;



import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

public class EncryptionUtil {

    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final String KEY = "AbcSecretKey0908";

    public static String getEncryptSecret(String apiSecret) {
        String encryptSecret = "";
        try {
            encryptSecret = encrypt(apiSecret);
        } catch (Exception e) {
            throw RoadException.create(ErrorCodeEnum.ENCRYPT_ERROR);
        }
        return encryptSecret;
    }

    public static String getDecryptSecret(String apiSecret) {
        String decryptSecret = "";
        try {
            decryptSecret = decrypt(apiSecret);
        } catch (Exception e) {
            throw RoadException.create(ErrorCodeEnum.DECRYPT_ERROR);
        }
        return decryptSecret;
    }

    private static String encrypt(String data) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(KEY.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedData = cipher.doFinal(data.getBytes());
        return Base64.getEncoder().encodeToString(encryptedData);
    }

    private static String decrypt(String encryptedData) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(KEY.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decryptedData = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decryptedData);
    }

    public static void main(String[] args) throws Exception {
        String data = "1w-DU0iQKdkl5zYkXEyreEMaTPk";
        String encryptedData = encrypt(data);
        String decryptedData = decrypt(encryptedData);
        System.out.println(data.equals(decryptedData));
    }
}
