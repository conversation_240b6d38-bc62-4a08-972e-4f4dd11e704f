package com.maixi.road.common.core.utils;

import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;

import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Objects;

/**
 * AssertUtils
 */
public class AssertUtils {

    /**
     * 断言，若对象为null，则抛出业务异常code
     */
    public static void notNullWithBizExp(Object obj, ErrorCodeEnum codeEnum) {
        if (Objects.isNull(obj)) {
            throw RoadException.create(codeEnum);
        }
    }

    public static void notNullWithBizExp(Object obj, String errorMsg) {
        if (Objects.isNull(obj)) {
            throw RoadException.create(ErrorCodeEnum.BIZ_ERROR, errorMsg);
        }
    }

    /**
     * 断言，若对象为null或对象值为空，则抛出业务异常code
     */
    public static void notEmptyWithBizExp(Object obj, ErrorCodeEnum codeEnum) {
        if (ObjectUtils.isEmpty(obj)) {
            throw RoadException.create(codeEnum);
        }
    }

    /**
     * 断言，若字符串为null或字符串为空白，则抛出业务异常code
     */
    public static void notBlankWithBizExp(CharSequence str, ErrorCodeEnum codeEnum) {
        if (StringUtils.isBlank(str)) {
            throw RoadException.create(codeEnum);
        }
    }

    /**
     * 断言，若字符串为null或字符串为空白，则抛出业务异常code
     */
    public static void notBlankWithBizExp(CharSequence str, ErrorCodeEnum codeEnum, @NonNull String msg) {
        if (StringUtils.isBlank(str)) {
            throw RoadException.create(codeEnum, msg);
        }
    }

    /**
     * 断言，若为假，则抛出业务异常code
     */
    public static void notTrueWithBizExp(boolean bool, ErrorCodeEnum codeEnum) {
        if (!bool) {
            throw RoadException.create(codeEnum);
        }
    }

    /**
     * 断言，若为真，则抛出业务异常code
     */
    public static void trueWithBizExp(boolean bool, ErrorCodeEnum codeEnum) {
        if (bool) {
            throw RoadException.create(codeEnum);
        }
    }

    /**
     * 断言，若为真，则抛出业务异常code
     */
    public static void trueWithBizExp(boolean bool, ErrorCodeEnum codeEnum, @NonNull String msg) {
        if (bool) {
            throw RoadException.create(codeEnum, msg);
        }
    }

    /**
     * 断言，若为假，则抛出业务异常code
     */
    public static void notTrueWithBizExp(boolean bool, ErrorCodeEnum codeEnum, @NonNull String msg) {
        if (!bool) {
            throw RoadException.create(codeEnum, msg);
        }
    }
}
