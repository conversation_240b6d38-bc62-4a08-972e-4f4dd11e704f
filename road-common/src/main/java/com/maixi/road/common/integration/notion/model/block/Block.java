package com.maixi.road.common.integration.notion.model.block;

import lombok.Data;

@Data
public class Block {
    private String object;
    private String type;

    private Heading heading_1;
    private Heading heading_2;
    private Heading heading_3;

    private RichTexts paragraph;
    private RichTexts quote;
    private Callout callout;

    private Image image;
    private Video video;
    private Audio audio;
    private File file;
    private Pdf pdf;
    private Bookmark bookmark;

    private Code code;
    private Table table;
    private Divider divider;

    private ListItem bulleted_list_item;
    private ListItem numbered_list_item;

    private Embed embed;
    private LinkToPage link_to_page;

    public static Block linkToPage(LinkToPage linkToPage) {
        Block block = new Block();
        block.setObject("block");
        block.setType("link_to_page");
        block.setLink_to_page(linkToPage);
        return block;
    }

    public static Block buildBookmark(Bookmark bookmark) {
        Block block = new Block();
        block.setObject("block");
        block.setType("bookmark");
        block.setBookmark(bookmark);
        return block;
    }

    public static Block buildCode(Code code) {
        Block block = new Block();
        block.setObject("block");
        block.setType("code");
        block.setCode(code);
        return block;
    }

    public static Block buildEmbed(Embed embed) {
        Block block = new Block();
        block.setObject("block");
        block.setType("embed");
        block.setEmbed(embed);
        return block;
    }

    public static Block buildBulletedListItem(ListItem bulletedListItem) {
        Block block = new Block();
        block.setObject("block");
        block.setType("bulleted_list_item");
        block.setBulleted_list_item(bulletedListItem);
        return block;
    }

    public static Block buildNumberedListItem(ListItem numbered_list_item) {
        Block block = new Block();
        block.setObject("block");
        block.setType("numbered_list_item");
        block.setNumbered_list_item(numbered_list_item);
        return block;
    }

    public static Block paragraph(RichTexts paragraph) {
        Block block = new Block();
        block.setObject("block");
        block.setType("paragraph");
        block.setParagraph(paragraph);
        return block;
    }

    public static Block image(Image image) {
        Block block = new Block();
        block.setObject("block");
        block.setType("image");
        block.setImage(image);
        return block;
    }

    public static Block video(Video video) {
        Block block = new Block();
        block.setObject("block");
        block.setType("video");
        block.setVideo(video);
        return block;
    }

    public static Block audio(Audio audio) {
        Block block = new Block();
        block.setObject("block");
        block.setType("audio");
        block.setAudio(audio);
        return block;
    }

    public static Block file(File file) {
        Block block = new Block();
        block.setObject("block");
        block.setType("file");
        block.setFile(file);
        return block;
    }

    public static Block pdf(Pdf pdf) {
        Block block = new Block();
        block.setObject("block");
        block.setType("pdf");
        block.setPdf(pdf);
        return block;
    }

    public static Block heading_1(Heading heading) {
        Block block = new Block();
        block.setObject("block");
        block.setType("heading_1");
        block.setHeading_1(heading);
        return block;
    }

    public static Block heading_2(Heading heading) {
        Block block = new Block();
        block.setObject("block");
        block.setType("heading_2");
        block.setHeading_2(heading);
        return block;
    }

    public static Block heading_3(Heading heading) {
        Block block = new Block();
        block.setObject("block");
        block.setType("heading_3");
        block.setHeading_3(heading);
        return block;
    }

    public static Block quote(RichTexts body) {
        Block block = new Block();
        block.setObject("block");
        block.setType("quote");
        block.setQuote(body);
        return block;
    }

    public static Block divider() {
        Block block = new Block();
        block.setObject("block");
        block.setType("divider");
        block.setDivider(Divider.divider());
        return block;
    }

    public static Block table(Table table) {
        Block block = new Block();
        block.setObject("block");
        block.setType("table");
        block.setTable(table);
        return block;
    }

    public static Block callout(Callout callout) {
        Block block = new Block();
        block.setObject("block");
        block.setType("callout");
        block.setCallout(callout);
        return block;
    }
}
