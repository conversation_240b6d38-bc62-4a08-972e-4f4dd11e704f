package com.maixi.road.common.integration.notion.model.block;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

import com.maixi.road.common.integration.notion.model.common.RichText;

@Data
@NoArgsConstructor
public class Image {
    private String type;
    private External external;
    private List<RichText> caption;

    public Image(String url) {
        this.type = "external";
        this.external = External.newFile(url);
    }

    public Image(String url, String caption) {
        this.type = "external";
        this.external = External.newFile(url);
        this.caption = Collections.singletonList(RichText.simpleText(caption));
    }
}
