package com.maixi.road.common.core.model.request;


import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ArticleParam {

    private String origin;

    private String title;

    private String author;

    private String link;

    //========================================//


    private String icon;


    private String cover;


    private List<String> tags;


    private String remark;


    private String publishTime;


    private String siteName;


    private String description;

}
