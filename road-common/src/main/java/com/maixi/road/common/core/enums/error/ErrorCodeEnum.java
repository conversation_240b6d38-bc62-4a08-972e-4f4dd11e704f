package com.maixi.road.common.core.enums.error;

import com.maixi.road.common.core.enums.EnumInterface;

import lombok.Getter;

/**
 * <p>业务级别错误码</p>
 * F13000-F13999
 */
@Getter
public enum ErrorCodeEnum implements EnumInterface<ErrorCodeEnum> {
    REQUIRE_NOT_NULL(100, "must be not null"),

    DEFAULT_ERROR(99999, "Error"),

    PARAMS_ERROR(400, "参数错误"),
    MISS_PARAM(401, "缺少参数"),
    FALLBACK(999, "远程调用失败"),

    INTERNAL_ERROR(500, "服务内部异常错误"),
    BIZ_ERROR(666, "业务校验异常"),

    RESP_IS_NOT_JSON(70000, "响应数据不是json"),

    //用户相关
    USER_NOT_REGISTER(80000, "用户未注册"),
    USER_NOT_LOGIN(80001, "用户未登录"),
    USER_UNAUTHORIZED(80002, "用户未授权或授权失效"),
    USER_DATA_ERROR(80003, "用户数据异常"),
    USER_UNAUTHORIZED_OPERATION(80004, "用户无权限操作"),
    USER_LOGIN_TOKEN_EXPIRED(80005, "用户登录token已过期"),
    USER_OPENID_NOT_EXIST(80006, "用户信息缺失 openid"),
    USER_OPENID_NOT_MATCH_APPID(80007, "用户 openid 与 appid 不匹配"),

    USER_OPERATION_FREQUENCY(90001, "用户操作过于频繁"),
    ENCRYPT_ERROR(90001, "加密失败"),
    DECRYPT_ERROR(90002, "解密失败"),

    RESPONSE_SUCCESS(200, "ok"),
    THIRD_PARTY_FAIL(3000, "第三方服务调用失败"),
    ;

    private final Integer code;
    private final String msg;
    private String desc;

    ErrorCodeEnum(int code, String message) {
        this.code = code;
        this.msg = message;
    }

    ErrorCodeEnum(int code, String desc, String message) {
        this(code, message);
        this.desc = desc;
    }


    @Override
    public ErrorCodeEnum getDefault() {
        return DEFAULT_ERROR;
    }
}
