package com.maixi.road.common.integration.notion.constants;

public class NotionIntegrations {

    public static final String GRANT_TYPE = "authorization_code";
    public static final String NOTION_VERSION = "2022-06-28";
    public static final String CLIENT_ID = "179d872b-594c-8002-831f-00375a64e353";
    public static final String CLIENT_SECRET = "**************************************************";
    public static final String CLIENT_ID_AND_SECRET = CLIENT_ID + ":" + CLIENT_SECRET;
    public static final String INTEGRATION_HOST = "https://www.notion.so/install-integration";
    public static final String REDIRECT_URI = "https://www.notionmpclipper.site/notionclipper/notion/auth/callback";
    public static final String AUTHORIZE_URI = "https://api.notion.com/v1/oauth/authorize?client_id=179d872b-594c-8002-831f-00375a64e353&response_type=code&owner=user&redirect_uri=https%3A%2F%2Fwww.notionmpclipper.site%2Fnotionclipper%2Fnotion%2Fauth%2Fcallback";
}
