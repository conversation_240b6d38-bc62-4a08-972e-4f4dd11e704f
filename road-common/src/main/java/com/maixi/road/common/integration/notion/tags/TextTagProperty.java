package com.maixi.road.common.integration.notion.tags;


import com.maixi.road.common.integration.notion.enums.ColorEnum;

import lombok.Data;

@Data
public class TextTagProperty {
    private String href;
    private String color;
    private Integer annotations;

    // 样式来源标记位，用于区分样式是显式设置还是继承获得
    // 使用位运算，每一位表示一个样式属性的来源
    // 0: 默认/继承, 1: 显式设置
    private int styleSourceFlags = 0;

    // 样式来源标记位常量
    public static final int HREF_EXPLICIT = 1; // 0001: href显式设置
    public static final int COLOR_EXPLICIT = 2; // 0010: color显式设置
    public static final int BOLD_EXPLICIT = 4; // 0100: bold显式设置
    public static final int ITALIC_EXPLICIT = 8; // 1000: italic显式设置
    // 可以根据需要添加更多样式标记

    /**
     * 标记href为显式设置
     */
    public void markHrefExplicit() {
        this.styleSourceFlags |= HREF_EXPLICIT;
    }

    /**
     * 标记color为显式设置
     */
    public void markColorExplicit() {
        this.styleSourceFlags |= COLOR_EXPLICIT;
    }

    /**
     * 标记bold为显式设置
     */
    public void markBoldExplicit() {
        this.styleSourceFlags |= BOLD_EXPLICIT;
    }

    /**
     * 标记italic为显式设置
     */
    public void markItalicExplicit() {
        this.styleSourceFlags |= ITALIC_EXPLICIT;
    }

    /**
     * 检查href是否为显式设置
     */
    public boolean isHrefExplicit() {
        return (this.styleSourceFlags & HREF_EXPLICIT) != 0;
    }

    /**
     * 检查color是否为显式设置
     */
    public boolean isColorExplicit() {
        return (this.styleSourceFlags & COLOR_EXPLICIT) != 0;
    }

    /**
     * 检查bold是否为显式设置
     */
    public boolean isBoldExplicit() {
        return (this.styleSourceFlags & BOLD_EXPLICIT) != 0;
    }

    /**
     * 检查italic是否为显式设置
     */
    public boolean isItalicExplicit() {
        return (this.styleSourceFlags & ITALIC_EXPLICIT) != 0;
    }

    /**
     * 获取样式来源标记位
     */
    public int getStyleSourceFlags() {
        return this.styleSourceFlags;
    }

    public static TextTagProperty defaultProperty() {
        TextTagProperty currentTextTag = new TextTagProperty();
        currentTextTag.setColor(ColorEnum._default.getColor());
        currentTextTag.setAnnotations(0);
        // 默认属性不标记任何显式设置
        currentTextTag.setStyleSourceFlags(0);
        return currentTextTag;
    }
}
