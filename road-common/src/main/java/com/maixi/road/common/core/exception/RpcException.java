package com.maixi.road.common.core.exception;

import com.maixi.road.common.core.enums.error.RpcErrCodeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RpcException extends RuntimeException {

    private int code;

    private String msg;

    public RpcException(int code, String msg) {
        super(toMsg(code, msg));
        this.code = code;
        this.msg = msg;
    }


    public RpcException(RpcErrCodeEnum error) {
        this(error.getCode(), error.getMsg());
    }

    private static String toMsg(int code, String msg) {
        return "[" + code + "] " + msg;
    }

    public static RpcException create(RpcErrCodeEnum error) {
        return new RpcException(error);
    }

    public static RpcException create(RpcErrCodeEnum error, String errorMsg) {
        return new RpcException(error.getCode(), errorMsg);
    }
}
