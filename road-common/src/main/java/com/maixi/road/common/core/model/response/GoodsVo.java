package com.maixi.road.common.core.model.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder

public class GoodsVo {

    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品编号
     */
    private String goodsNo;
    /**
     * 商品描述
     */
    private String description;
    /**
     * 原价
     */
    private Integer price;
    /**
     * 折扣价
     */
    private Integer discount;
}
