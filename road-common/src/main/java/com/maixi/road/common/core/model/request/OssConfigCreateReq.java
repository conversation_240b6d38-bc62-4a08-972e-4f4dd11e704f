package com.maixi.road.common.core.model.request;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class OssConfigCreateReq {

    @NotNull(message = "图床类型不能为空")
    private String type;

    @NotBlank(message = "endpoint不能为空")
    private String endpoint;

    @NotBlank(message = "accessKey不能为空")
    private String accessKey;

    @NotBlank(message = "accessSecret不能为空")
    private String accessSecret;

    @NotBlank(message = "bucketName不能为空")
    private String bucketName;

    /**
     * 自定义域名
     */
    private String customDomain;

    /**
     * 区域
     */
    private String region;
}
