package com.maixi.road.common.core.enums.error;

import com.maixi.road.common.core.enums.EnumInterface;

import lombok.Getter;

/**
 * <p>业务级别错误码</p>
 * F13000-F13999
 */
@Getter
public enum ClipperErrCodeEnum implements EnumInterface<ClipperErrCodeEnum> {

    DEFAULT_ERROR(99999, "Error", "Error"),

    CLIPPER_ERROR(40001, "文章解析失败"), 
    CLIPPER_INSUFFICIENT_BALANCE(40002, "免费次数已用完"), 
    CLIPPER_DAY_INSUFFICIENT_BALANCE(40003, "今日免费次数已用完"), 
    CLIPPER_MONTH_INSUFFICIENT_BALANCE(40004, "本月免费次数已用完"),

    ;

    private final Integer code;
    private final String msg;
    private String desc;

    ClipperErrCodeEnum(int code, String message) {
        this.code = code;
        this.msg = message;
    }

    ClipperErrCodeEnum(int code, String desc, String message) {
        this(code, message);
        this.desc = desc;
    }


    @Override
    public ClipperErrCodeEnum getDefault() {
        return DEFAULT_ERROR;
    }
}
