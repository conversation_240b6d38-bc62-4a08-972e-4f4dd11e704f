package com.maixi.road.common.core.enums.error;

import com.maixi.road.common.core.enums.EnumInterface;

import lombok.Getter;

/**
 * <p>业务级别错误码</p>
 * F13000-F13999
 */
@Getter
public enum ParamErrCodeEnum implements EnumInterface<ParamErrCodeEnum> {

    DEFAULT_ERROR(99999, "Error", "Error"),




    ;

    private final Integer code;
    private final String msg;
    private String desc;

    ParamErrCodeEnum(int code, String message) {
        this.code = code;
        this.msg = message;
    }

    ParamErrCodeEnum(int code, String desc, String message) {
        this(code, message);
        this.desc = desc;
    }


    @Override
    public ParamErrCodeEnum getDefault() {
        return DEFAULT_ERROR;
    }
}
