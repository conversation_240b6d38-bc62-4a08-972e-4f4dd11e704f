package com.maixi.road.common.integration.notion.model.block;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

import com.maixi.road.common.integration.notion.model.common.RichText;


@Data
@NoArgsConstructor
public class File {

    private String type;
    private External external;
    private String name;
    private List<RichText> caption;

    public File(String url, String fileName) {
        this.type = "external";
        this.name = fileName;
        this.external = External.newFile(url);
        this.caption = Collections.emptyList();
    }
}
