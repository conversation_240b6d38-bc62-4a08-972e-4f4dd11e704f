package com.maixi.road.common.integration.notion.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OriginTypeEnum {

    WX("微信", "https://mp.weixin.qq.com"),
    RB("小红书", "https://www.xiaohongshu.com"),
    JK("即刻", "https://m.okjike.com"),
    SSPAI("少数派", "https://sspai.com"),
    XyuZhou("小宇宙", "https://www.xiaoyuzhoufm.com"),
    XCF("下厨房", "https://www.xiachufang.com"),
    DOUBAN_GROUP("豆瓣小组", "https://douban.com"),
    DOUBAN_BOOK("豆瓣书评", "https://douban.com"),
    DOUBAN_NOTE("豆瓣日记", "https://douban.com"),
    DOUBAN_MOVIE("豆瓣电影", "https://douban.com"),
    BILIBILI("哔哩哔哩", "https://www.bilibili.com"),
    SMZDM("什么值得买", "https://post.smzdm.com"),
    XUEQIU("雪球", "https://xueqiu.com"),
    UNTAG("UNTAG", "https://utgd.net"),

    ZH("知乎", "https://zhuanlan.zhihu.com"),
    WB("微博", "https://www.weibo.com"),
    CSDN("CSDN", "https://blog.csdn.net"),
    JUEJIN("掘金", "https://juejin.cn"),
    PM("人人都是产品经理", "https://www.woshipm.com"),
    UNKNOWN("未知", "");

    private final String name;
    private final String domain;
}
