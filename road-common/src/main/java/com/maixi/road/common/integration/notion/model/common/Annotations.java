package com.maixi.road.common.integration.notion.model.common;

import lombok.Data;

import java.util.Objects;

import com.maixi.road.common.integration.notion.enums.ColorEnum;


/**
 * <AUTHOR>
 * @since 2023/7/26
 **/
@Data
public class Annotations {
    private boolean bold;
    private boolean italic;
    private boolean strikethrough;
    private boolean underline;
    private boolean code;
    private String color;

    public static Annotations defaultAnnotations() {
        Annotations annotations = new Annotations();
        annotations.setBold(false);
        annotations.setItalic(false);
        annotations.setStrikethrough(false);
        annotations.setUnderline(false);
        annotations.setCode(false);
        annotations.setColor(ColorEnum._default.getColor());
        return annotations;
    }

    public static Annotations grayItalic() {
        Annotations annotations = new Annotations();
        annotations.setBold(false);
        annotations.setItalic(true);
        annotations.setStrikethrough(false);
        annotations.setUnderline(false);
        annotations.setCode(false);
        annotations.setColor(ColorEnum._gray.getColor());
        return annotations;
    }

    public static Annotations bold() {
        Annotations annotations = new Annotations();
        annotations.setBold(true);
        annotations.setItalic(false);
        annotations.setStrikethrough(false);
        annotations.setUnderline(false);
        annotations.setCode(false);
        annotations.setColor(ColorEnum._default.getColor());
        return annotations;
    }

    public static Annotations red() {
        Annotations annotations = new Annotations();
        annotations.setBold(true);
        annotations.setItalic(false);
        annotations.setStrikethrough(false);
        annotations.setUnderline(false);
        annotations.setCode(false);
        annotations.setColor(ColorEnum._red.getColor());
        return annotations;
    }

    public static Annotations colorBy(ColorEnum color) {
        Annotations annotations = new Annotations();
        annotations.setBold(false);
        annotations.setItalic(false);
        annotations.setStrikethrough(false);
        annotations.setUnderline(false);
        annotations.setCode(false);
        annotations.setColor(color.getColor());
        return annotations;
    }

    public static Annotations underline() {
        Annotations annotations = new Annotations();
        annotations.setBold(false);
        annotations.setItalic(false);
        annotations.setStrikethrough(false);
        annotations.setUnderline(true);
        annotations.setCode(false);
        annotations.setColor(ColorEnum._default.getColor());
        return annotations;
    }

    public static boolean compare(Annotations a, Annotations b) {
        if (a == null || b == null) {
            return false;
        }
        return a.isBold() == b.isBold()
                && a.isItalic() == b.isItalic()
                && a.isCode() == b.isCode()
                && a.isUnderline() == b.isUnderline()
                && a.isStrikethrough() == b.isStrikethrough()
                && Objects.equals(a.getColor(), b.getColor());
    }

    public static Annotations copy(Annotations annotations) {
        Annotations newAnnotations = new Annotations();
        newAnnotations.setBold(annotations.isBold());
        newAnnotations.setItalic(annotations.isItalic());
        newAnnotations.setStrikethrough(annotations.isStrikethrough());
        newAnnotations.setUnderline(annotations.isUnderline());
        newAnnotations.setCode(annotations.isCode());
        newAnnotations.setColor(annotations.getColor());
        return newAnnotations;
    }
}
