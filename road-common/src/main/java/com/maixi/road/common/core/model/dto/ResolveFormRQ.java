package com.maixi.road.common.core.model.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ResolveFormRQ extends ResolveRS {

    private String remark;
    private List<String> tags;

    public ResolveFormRQ(ResolveRS resolveRS) {
        this.setTitle(resolveRS.getTitle());
        this.setAuthor(resolveRS.getAuthor());
        this.setLink(resolveRS.getLink());
        this.setIcon(resolveRS.getIcon());
        this.setCover(resolveRS.getCover());
        this.setOrigin(resolveRS.getOrigin());
        this.setSiteName(resolveRS.getSiteName());
        this.setPublishTime(resolveRS.getPublishTime());
        this.setDescription(resolveRS.getDescription());
        this.setCategory(resolveRS.getCategory());
    }
}
