package com.maixi.road.common.core.model.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PromotionVo {
    /**
     * 推广码
     */
    private String promotionCode;
    /**
     * 受邀人 ID。需要脱敏处理
     */
    private String objectId;
    /**
     * 获得积分
     */
    private Integer credit;
    /**
     * 状态。0：未兑换，1：已兑换，2：已过期
     */
    private Integer status;
    /**
     * 获得日期
     */
    private String gmtCreate;
}
