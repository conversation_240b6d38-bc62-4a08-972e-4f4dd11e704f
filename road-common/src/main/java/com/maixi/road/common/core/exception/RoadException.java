package com.maixi.road.common.core.exception;

import com.maixi.road.common.core.enums.error.ErrorCodeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RoadException extends RuntimeException {

    private int code;

    private String msg;

    public RoadException(int code, String msg) {
        super(toMsg(code, msg));
        this.code = code;
        this.msg = msg;
    }

    public RoadException(ErrorCodeEnum error) {
        this(error.getCode(), error.getMsg());
    }

    private static String toMsg(int code, String msg) {
        return "[" + code + "] " + msg;
    }

    public static RoadException create(ErrorCodeEnum error) {
        return new RoadException(error);
    }

    public static RoadException create(ErrorCodeEnum error, String errorMsg) {
        return new RoadException(error.getCode(), errorMsg);
    }
}
