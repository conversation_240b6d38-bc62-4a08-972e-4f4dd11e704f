package com.maixi.road.common.core.model.request;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MemberCreateParam {


    @NotBlank(message = "unionId不能为空")
    private String unionId;

    @NotNull(message = "startTime不能为空")
    private LocalDateTime startTime;

    @NotNull(message = "会员类型不能为空")
    private Integer vipType;

    @NotNull(message = "令牌缺失")
    private String token;
}
