package com.maixi.road.common.core.exception;

import com.maixi.road.common.core.enums.error.ImageCloudErrCodeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ImageCloudException extends RuntimeException {

    private int code;

    private String msg;

    public ImageCloudException(int code, String msg) {
        super(toMsg(code, msg));
        this.code = code;
        this.msg = msg;
    }


    public ImageCloudException(ImageCloudErrCodeEnum error) {
        this(error.getCode(), error.getMsg());
    }

    private static String toMsg(int code, String msg) {
        return "[" + code + "] " + msg;
    }

    public static ImageCloudException create(ImageCloudErrCodeEnum error) {
        return new ImageCloudException(error);
    }

    public static ImageCloudException create(ImageCloudErrCodeEnum error, String errorMsg) {
        return new ImageCloudException(error.getCode(), errorMsg);
    }
}
