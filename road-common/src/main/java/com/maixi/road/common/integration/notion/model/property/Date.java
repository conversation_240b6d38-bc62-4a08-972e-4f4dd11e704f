package com.maixi.road.common.integration.notion.model.property;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Date {

    private Attr date;

    public static Date buildOnlyStart(String start) {
        return new Date(Attr.onlyStart(start));
    }

    public static Date buildWithEnd(String start, String end) {
        return new Date(Attr.withEnd(start, end));
    }

    public static JSONObject newPropertyDefine() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("date", new Object());
        return jsonObject;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Attr {
        private String start;
        private String end;
        private String time_zone = "Asia/Shanghai";

        public static Attr onlyStart(String start) {
            Attr attr = new Attr();
            attr.setStart(start);
            return attr;
        }

        public static Attr withEnd(String start, String end) {
            Attr attr = new Attr();
            attr.setStart(start);
            attr.setEnd(end);
            return attr;
        }
    }
}
