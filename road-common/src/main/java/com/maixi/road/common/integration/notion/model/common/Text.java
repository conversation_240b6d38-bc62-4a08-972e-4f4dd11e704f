package com.maixi.road.common.integration.notion.model.common;

import lombok.Data;

@Data
public class Text {
    private String content;
    private Link link;

    public static Text simpleContent(String content) {
        Text text = new Text();
        text.setContent(content);
        text.setLink(null);
        return text;
    }

    public static Text simpleContentWithLink(String content, String link) {
        Text text = new Text();
        text.setContent(content);
        text.setLink(new Link(link));
        return text;
    }

    public static Text copy(Text text) {
        Text newText = new Text();
        newText.setContent(text.getContent());
        if (text.getLink() != null) {
            newText.setLink(new Link(text.getLink().getUrl()));
        }
        return newText;
    }

}
