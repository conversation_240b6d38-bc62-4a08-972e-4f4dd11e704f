package com.maixi.road.common.integration.notion.model.page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import com.maixi.road.common.integration.notion.model.block.External;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class Icon {

    private String type;
    private String emoji;
    private External external;

    public Icon(String type, String emoji) {
        this.type = type;
        this.emoji = emoji;
    }

    public Icon(String type, External external) {
        this.type = type;
        this.external = external;
    }

    public static Icon buildByEmoji(String emoji) {
        if (StringUtils.isBlank(emoji)) {
            return null;
        }
        return new Icon("emoji", emoji);
    }

    public static Icon buildByFile(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        return new Icon("external", External.newFile(url));
    }
}
