package com.maixi.road.common.core.model.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder

public class OssConfigVo {


    private String type;


    private String endpoint;

    /**
     * 区域
     */
    private String region;


    private String accessKey;


    private String accessSecret;


    private String bucketName;

    /**
     * 自定义域名
     */

    private String customDomain;
}
