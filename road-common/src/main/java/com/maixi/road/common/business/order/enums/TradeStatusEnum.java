package com.maixi.road.common.business.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TradeStatusEnum {

    SUCCESS("SUCCESS","交易成功"),
    REFUND("REFUND","转入退款"),
    NOTPAY("NOTPAY","未支付"),
    CLOSED("CLOSED","已关闭"),
    USERPAYING("USERPAYING","用户支付中"),
    PAYERROR("PAYERROR","支付失败"),
    ACCEPT("ACCEPT","已接收，等待扣款"),
    ;

    private final String code;
    private final String desc;

}
