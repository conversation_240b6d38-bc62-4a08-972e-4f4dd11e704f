package com.maixi.road.common.core.model.dto;


import lombok.Data;

@Data
public class UserConfig {

    /**
     * 用户ID
     */
    private String unionId;

    /**
     * 始终使用自有图床
     */
    private Integer alwaysUsePicCloud;

    /**
     * 始终优先使用 cloudinary 图床
     */
    private Integer alwaysUseCloudinary;

    /**
     * 支持通用网页
     */
    private Integer supportAllSite;

    /**
     * 不要 icon
     */
    private Integer noIcon;

    /**
     * 不要 cover
     */
    private Integer noCover;

    /**
     * 快捷剪藏作为首页
     */
    private Integer quickClipAsEnterPage;

    /**
     * 是否保存到 obsidian
     */
    private Integer toObsidian;
    
    /**
     * 输出类型，默认为 notion，可选值：notion、markdown
     */
    private String outputType = "notion";
    
    /**
     * Markdown 文件保存路径
     */
    private String markdownSavePath;

    private ObsidianDTO obConfig;

}
