package com.maixi.road.common.core.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 剪藏模块通用响应对象
 * @param <T> 响应数据类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClipperResponse<T> {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 创建成功响应
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应对象
     */
    public static <T> ClipperResponse<T> success(T data) {
        return ClipperResponse.<T>builder()
                .success(true)
                .code(0)
                .data(data)
                .build();
    }
    
    /**
     * 创建失败响应
     * @param code 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应对象
     */
    public static <T> ClipperResponse<T> fail(Integer code, String message) {
        return ClipperResponse.<T>builder()
                .success(false)
                .code(code)
                .message(message)
                .build();
    }
    
    /**
     * 创建失败响应
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应对象
     */
    public static <T> ClipperResponse<T> fail(String message) {
        return fail(99999, message);
    }
}
