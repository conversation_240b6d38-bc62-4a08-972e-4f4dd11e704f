package com.maixi.road.common.core.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户常用标签DTO
 * 用于客户端与服务端同步常用标签数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserCommonTagsDTO {

    /**
     * 用户唯一标识
     */
    private String unionId;

    /**
     * 常用标签列表
     */
    private List<String> tags;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}