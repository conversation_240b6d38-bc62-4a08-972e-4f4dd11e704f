package com.maixi.road.common.core.model.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder

public class UserVo {

    /**
     * 微信标识
     */
    private String unionId;
    /**
     * 子账号 id
     */
    private String subUserId;


    private UserVo mainUserVo;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 推广码
     */
    private String introCode;

    /**
     * 是否绑定 notion
     */
    private Boolean notionAuth;

    /**
     * 消息转存数量
     */
    private Integer msgCount;
    /**
     * 文章剪藏数量
     */
    private Integer articleCount;
    /**
     * 分享次数
     */
    private Integer shareCount;


    private Integer praise;


    private Integer remainCount;


    private String userType;
    

    private Integer memberType;


    private Integer memberNo;


    private String endTime;


    private String openId;


    private String email;
}
