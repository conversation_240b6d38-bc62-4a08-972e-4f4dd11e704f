package com.maixi.road.common.service.config;

import com.maixi.road.common.core.model.dto.ArticleFieldDTO;
import com.maixi.road.common.core.model.dto.GloablConfig;
import com.maixi.road.common.core.model.dto.MessageFieldDTO;
import com.maixi.road.common.core.model.dto.UserConfig;
import com.maixi.road.common.integration.s3.config.S3Config;

public interface ConfigQueryApi {

    /**
     * 查询配置
     *
     * @param userId 用户ID
     * @return 用户配置
     */
    UserConfig queryConfig(String unionId);

    /**
     * 查询S3配置
     *
     * @param unionId 用户ID
     * @return S3配置
     */
    S3Config queryS3Config(String unionId);


    /**
     * 查询全局配置
     *
     * @return 全局配置
     */
    GloablConfig gloablConfig();

    /**
     * 获取图片存储配置
     *
     * @param unionId 用户ID
     * @return S3配置
     */
    S3Config getImgS3Config(String unionId);

    /**
     * 获取文章字段DTO
     *
     * @param unionId 用户ID
     * @return 文章字段DTO
     */
    ArticleFieldDTO getArticleFieldDTO(String unionId);

    /**
     * 获取消息字段 DTO
     * @param unionId
     * @return
     */
    MessageFieldDTO getMessageFieldDTO(String unionId);
}
