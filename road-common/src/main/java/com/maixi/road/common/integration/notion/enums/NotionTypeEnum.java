package com.maixi.road.common.integration.notion.enums;

import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NotionTypeEnum {
    created_time("created_time"),
    date("date"),
    multi_select("multi_select"),
    rich_text("rich_text"),
    select("select"),
    url("url"),

    ;

    private final String type;

    /**
     * 文章链接
     * @return
     */
    public static Set<String> urlType() {
        return Set.of(url.getType(), rich_text.getType());
    }

    /**
     * 来源
     * @return
     */
    public static Set<String> originType() {
        return Set.of(select.getType(), rich_text.getType());
    }

    /**
     * 标签
     * @return
     */
    public static Set<String> tagsType() {
        return Set.of(multi_select.getType());
    }

    /**
     * 创建时间
     * @return
     */
    public static Set<String> createdTimeType() {
        return Set.of(created_time.getType());
    }

    /**
     * 发布时间
     * @return
     */
    public static Set<String> publicTimeType() {
        return Set.of(date.getType());
    }

    /**
     * 备注
     * @return
     */
    public static Set<String> remarkType() {
        return Set.of(rich_text.getType());
    }

    /**
     * 作者
     * @return
     */
    public static Set<String> authorType() {
        return Set.of(select.getType(), rich_text.getType());
    }

    /**
     * 消息分类
     * @return
     */
    public static Set<String> categoryType() {
        return Set.of(select.getType());
    }
}
