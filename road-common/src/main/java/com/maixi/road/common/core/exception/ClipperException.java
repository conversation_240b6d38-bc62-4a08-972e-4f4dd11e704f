package com.maixi.road.common.core.exception;

import com.maixi.road.common.core.enums.error.ClipperErrCodeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = true)
public class ClipperException extends RuntimeException {

    private int code;

    private String msg;

    public ClipperException(int code, String msg) {
        super(toMsg(code, msg));
        this.code = code;
        this.msg = msg;
    }


    public ClipperException(ClipperErrCodeEnum error) {
        this(error.getCode(),  error.getMsg());
    }

    private static String toMsg(int code, String msg) {
        return "[" + code + "] " + msg;
    }

    public static ClipperException create(ClipperErrCodeEnum error) {
        return new ClipperException(error);
    }

    public static ClipperException create(ClipperErrCodeEnum error, String errorMsg) {
        return new ClipperException(error.getCode(), errorMsg);
    }
}
