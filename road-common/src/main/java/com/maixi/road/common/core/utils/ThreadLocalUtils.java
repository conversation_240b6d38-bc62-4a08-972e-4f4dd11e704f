package com.maixi.road.common.core.utils;

import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 线程本地变量工具类
 * 基于阿里巴巴的transmittable-thread-local库，支持在线程池等异步场景下的值传递
 * 
 * <AUTHOR>
 */
@Slf4j
public class ThreadLocalUtils {

    /**
     * 存储所有的TransmittableThreadLocal实例，用于统一管理和清理
     */
    private static final Map<String, TransmittableThreadLocal<?>> THREAD_LOCAL_MAP = new ConcurrentHashMap<>();

    /**
     * 创建一个TransmittableThreadLocal实例
     * 
     * @param key 唯一标识键
     * @param <T> 值类型
     * @return TransmittableThreadLocal实例
     */
    @SuppressWarnings("unchecked")
    public static <T> TransmittableThreadLocal<T> createThreadLocal(String key) {
        return (TransmittableThreadLocal<T>) THREAD_LOCAL_MAP.computeIfAbsent(key,
                k -> {
                    log.debug("创建TransmittableThreadLocal实例, key={}", k);
                    return new TransmittableThreadLocal<>();
                });
    }

    /**
     * 创建一个带有初始值供应器的TransmittableThreadLocal实例
     * 
     * @param key                  唯一标识键
     * @param initialValueSupplier 初始值供应器
     * @param <T>                  值类型
     * @return TransmittableThreadLocal实例
     */
    @SuppressWarnings("unchecked")
    public static <T> TransmittableThreadLocal<T> createThreadLocal(String key, Supplier<T> initialValueSupplier) {
        return (TransmittableThreadLocal<T>) THREAD_LOCAL_MAP.computeIfAbsent(key,
                k -> {
                    log.debug("创建带初始值的TransmittableThreadLocal实例, key={}", k);
                    return new TransmittableThreadLocal<T>() {
                        @Override
                        protected T initialValue() {
                            return initialValueSupplier.get();
                        }
                    };
                });
    }

    /**
     * 设置线程本地变量的值
     * 
     * @param key   唯一标识键
     * @param value 要设置的值
     * @param <T>   值类型
     */
    public static <T> void set(String key, T value) {
        TransmittableThreadLocal<T> threadLocal = createThreadLocal(key);
        threadLocal.set(value);
        log.debug("设置ThreadLocal值, key={}, value={}", key, value);
    }

    /**
     * 获取线程本地变量的值
     * 
     * @param key 唯一标识键
     * @param <T> 值类型
     * @return 线程本地变量的值，如果不存在则返回null
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(String key) {
        TransmittableThreadLocal<T> threadLocal = (TransmittableThreadLocal<T>) THREAD_LOCAL_MAP.get(key);
        if (threadLocal != null) {
            T value = threadLocal.get();
            log.debug("获取ThreadLocal值, key={}, value={}", key, value);
            return value;
        }
        log.debug("ThreadLocal不存在, key={}", key);
        return null;
    }

    /**
     * 获取线程本地变量的值，如果不存在则返回默认值
     * 
     * @param key          唯一标识键
     * @param defaultValue 默认值
     * @param <T>          值类型
     * @return 线程本地变量的值或默认值
     */
    public static <T> T getOrDefault(String key, T defaultValue) {
        T value = get(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 移除指定键的线程本地变量值
     * 
     * @param key 唯一标识键
     */
    public static void remove(String key) {
        TransmittableThreadLocal<?> threadLocal = THREAD_LOCAL_MAP.get(key);
        if (threadLocal != null) {
            threadLocal.remove();
            log.debug("移除ThreadLocal值, key={}", key);
        }
    }

    /**
     * 清理所有线程本地变量
     * 建议在请求结束或线程结束时调用，防止内存泄漏
     */
    public static void clearAll() {
        log.debug("清理所有ThreadLocal值, 共{}个", THREAD_LOCAL_MAP.size());
        THREAD_LOCAL_MAP.values().forEach(TransmittableThreadLocal::remove);
    }

    /**
     * 清理指定键的线程本地变量并从管理器中移除
     * 
     * @param key 唯一标识键
     */
    public static void removeAndClean(String key) {
        TransmittableThreadLocal<?> threadLocal = THREAD_LOCAL_MAP.remove(key);
        if (threadLocal != null) {
            threadLocal.remove();
            log.debug("移除并清理ThreadLocal, key={}", key);
        }
    }

    /**
     * 获取当前管理的ThreadLocal数量
     * 
     * @return ThreadLocal数量
     */
    public static int size() {
        return THREAD_LOCAL_MAP.size();
    }

    /**
     * 检查指定键的ThreadLocal是否存在
     * 
     * @param key 唯一标识键
     * @return 是否存在
     */
    public static boolean contains(String key) {
        return THREAD_LOCAL_MAP.containsKey(key);
    }
}