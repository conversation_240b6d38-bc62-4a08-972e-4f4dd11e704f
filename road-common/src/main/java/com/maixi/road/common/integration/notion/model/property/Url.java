package com.maixi.road.common.integration.notion.model.property;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class Url {
    private String url;

    public static Url build(String url) {
        return new Url(url);
    }

    public static JSONObject newPropertyDefine() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url", new Object());
        return jsonObject;
    }
}
