package com.maixi.road.common.business.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OrderStatusEnum {

    NOTPAY(0,"待支付"),
    SUCCESS(2,"支付成功"),
    PAYERROR(3,"支付错误"),
    REFUND(4,"已退款"),
    CLOSED(6,"已关闭"),

    ;

    private final Integer status;
    private final String desc;

    public static OrderStatusEnum getByStatus(Integer status) {
        for (OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
            if (orderStatusEnum.getStatus().equals(status)) {
                return orderStatusEnum;
            }
        }
        return null;
    }
}
