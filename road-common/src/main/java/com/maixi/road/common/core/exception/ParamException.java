package com.maixi.road.common.core.exception;

import com.maixi.road.common.core.enums.error.ParamErrCodeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ParamException extends RuntimeException {

    private int code;

    private String msg;

    public ParamException(int code, String msg) {
        super(toMsg(code, msg));
        this.code = code;
        this.msg = msg;
    }


    public ParamException(ParamErrCodeEnum error) {
        this(error.getCode(),  error.getMsg());
    }

    private static String toMsg(int code, String msg) {
        return "[" + code + "] " + msg;
    }

    public static ParamException create(ParamErrCodeEnum error) {
        return new ParamException(error);
    }

    public static ParamException create(ParamErrCodeEnum error, String errorMsg) {
        return new ParamException(error.getCode(), errorMsg);
    }
}
