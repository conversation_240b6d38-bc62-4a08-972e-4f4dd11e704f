package com.maixi.road.common.integration.notion.model.page;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import com.maixi.road.common.integration.notion.model.property.Url;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class Cover {
    private Url external;

    public static Cover build(String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            return null;
        }
        return new Cover(Url.build(imageUrl));
    }
}
