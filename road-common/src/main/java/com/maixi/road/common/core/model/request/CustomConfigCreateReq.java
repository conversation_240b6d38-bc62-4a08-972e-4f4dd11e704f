package com.maixi.road.common.core.model.request;


import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CustomConfigCreateReq {

    /**
     * 总是使用图床，若为是，则所有平台都使用图床
     * 若为否，则除了小红书和少数派及公共网页，都不使用图床
     */
    @NotNull(message = "alwaysUsePicCloud 不能为null")
    private Boolean alwaysUsePicCloud;
    /**
     * 图床优先使用 Cloudinary
     */
    @NotNull(message = "alwaysUseCloudinary 不能为null")
    private Boolean alwaysUseCloudinary;

    /**
     * 支持通用网页
     */
    @NotNull(message = "supportAllSite 不能为null")
    private Boolean supportAllSite;


    private Boolean noIcon;

    private Boolean noCover;

    private Boolean quickClipAsEnterPage;

    private Boolean toObsidian;
}
