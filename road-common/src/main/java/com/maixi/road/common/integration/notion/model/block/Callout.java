package com.maixi.road.common.integration.notion.model.block;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

import com.maixi.road.common.integration.notion.enums.BackgroundColorEnum;
import com.maixi.road.common.integration.notion.model.common.RichText;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Callout {
    private Icon icon;
    private List<RichText> rich_text;
    private String color;

    public static Callout build(List<RichText> richTexts) {
        return Callout.builder()
//                .color(BackgroundColorEnum._default.getColor())
                .rich_text(richTexts)
                .build();
    }

    public static Callout build(List<RichText> richTexts,Icon icon) {
        return Callout.builder()
                .icon(icon)
                .rich_text(richTexts)
                .build();
    }

    public static Callout buildTip(String tipContent) {
        return Callout.builder()
                .color(BackgroundColorEnum._yellow.getColor())
                .icon(Icon.emoji("⚠️"))
                .rich_text(Collections.singletonList(RichText.simpleText(tipContent)))
                .build();
    }

    public static Callout buildInfo(String infoContent) {
        return Callout.builder()
                .color(BackgroundColorEnum._yellow.getColor())
                .icon(Icon.emoji("✅"))
                .rich_text(Collections.singletonList(RichText.simpleText(infoContent)))
                .build();
    }
}
