package com.maixi.road.common.business.order.enums;

import org.springframework.util.Assert;

/**
 */
public enum PaymentBizTypeEnum {

    PAY_ORDER(11, "支付订单"),
//    PAYMENT_MEMBER(22, "充值会员"),

    ;

    private Integer code;
    private String desc;

    PaymentBizTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PaymentBizTypeEnum explain(Integer code) {
        Assert.notNull(code, "code should not be null");
        for (PaymentBizTypeEnum enume : PaymentBizTypeEnum.values()) {
            if (enume.getCode() == code) {
                return enume;
            }
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
