package com.maixi.road.common.core.exception;

import com.maixi.road.common.core.enums.error.NotionErrCodeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class NotionException extends RuntimeException {

    private int code;

    private String msg;

    public NotionException(int code, String msg) {
        super(toMsg(code, msg));
        this.code = code;
        this.msg = msg;
    }


    public NotionException(NotionErrCodeEnum error) {
        this(error.getCode(), error.getMsg());
    }

    private static String toMsg(int code, String msg) {
        return "[" + code + "] " + msg;
    }

    public static NotionException create(NotionErrCodeEnum error) {
        return new NotionException(error);
    }

    public static NotionException create(NotionErrCodeEnum error, String errorMsg) {
        return new NotionException(error.getCode(), errorMsg);
    }
}
