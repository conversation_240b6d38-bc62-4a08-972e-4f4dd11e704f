package com.maixi.road.common.core.utils;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


/**
 *
 */
@Slf4j
public class RequestUtil {

    private static final Pattern domain_pattern = Pattern.compile("^(?:https?://)?(?:www\\.)?([^:/\\s]+)");

    private RequestUtil() {
    }

    // public static String getUnionIdFromRequest(HttpServletRequest request) {
    //     if (request == null) {
    //         return null;
    //     }
    //     String jwtToken = request.getHeader("Authorization");
    //     Claims claims = JwtUtil.parse(jwtToken);
    //     return Optional.ofNullable(claims).map(Claims::getSubject).orElse(null);
    // }

    public static String getDomainFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "未知";
        }
        try {
            Matcher matcher = domain_pattern.matcher(url);
            if (matcher.find()) {
                String value = matcher.group(1);
                return value;
            }
        } catch (Exception e) {
            log.error("getDomainFromUrl error, url:{}", url, e);
        }
        return "未知";
    }

    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        return ((ServletRequestAttributes) requestAttributes).getRequest();
    }

    public static HttpSession getSession() {
        return getRequest().getSession();
    }

    public static String getRequestUa() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        return request.getHeader("user-agent");
    }


    public static String getIpByHttpServletRequest() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        return getIpByHttpServletRequest(request);
    }

    public static String getIpByHttpServletRequest(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (ip != null && ip.length() > 15) { // "***.***.***.***".length() = 15
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","));
            }
        }
        return ip;
    }

}
