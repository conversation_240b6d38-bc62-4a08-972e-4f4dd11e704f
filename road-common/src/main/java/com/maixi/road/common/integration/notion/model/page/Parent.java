package com.maixi.road.common.integration.notion.model.page;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/7/27
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Parent {
    private String type;
    private String database_id;

    public static Parent build(String dbId) {
        return new Parent("database_id", dbId);
    }
}
