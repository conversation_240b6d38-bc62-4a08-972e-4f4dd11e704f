package com.maixi.road.common.integration.notion.tags;


import com.maixi.road.common.integration.notion.model.block.Block;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WrapBlock {
    private Block block;
    private TextTag TextTag;
    private Boolean isBlock;
    private Boolean isTextTag;

    public static WrapBlock wrapBlock(Block block) {
        return new WrapBlock(block, null, true, false);
    }

    public static WrapBlock wrapTextTag(TextTag textTag) {
        return new WrapBlock(null, textTag, false, true);
    }
}
