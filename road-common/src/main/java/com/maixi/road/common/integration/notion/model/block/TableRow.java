package com.maixi.road.common.integration.notion.model.block;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.common.integration.notion.model.common.RichText;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class TableRow {

    private List<RichText> cells;

    public static JSONObject buildRow(List<List<RichText>> richTextList) {
        JSONObject tableRow = new JSONObject();
        tableRow.put("type", "table_row");
        tableRow.put("table_row", new JSONObject()
                .fluentPut("cells", richTextList));
        return tableRow;
    }
}
