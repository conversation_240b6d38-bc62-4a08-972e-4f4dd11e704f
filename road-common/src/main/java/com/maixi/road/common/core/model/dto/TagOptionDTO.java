package com.maixi.road.common.core.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 标签选项DTO
 * 用于返回Notion数据库中标签字段的所有可选值
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagOptionDTO {

    /**
     * 标签字段名称
     */
    private String fieldName;

    /**
     * 标签字段类型 (select 或 multi_select)
     */
    private String fieldType;

    /**
     * 所有可选的标签值
     */
    private List<String> options;
}