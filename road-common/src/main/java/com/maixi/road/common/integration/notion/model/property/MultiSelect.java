package com.maixi.road.common.integration.notion.model.property;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class MultiSelect {
    private Set<Attr> multi_select;

    public static MultiSelect empty() {
        return new MultiSelect(Collections.emptySet());
    }

    public static MultiSelect build(List<String> items) {
        if (!CollectionUtils.isEmpty(items)) {
            return new MultiSelect(items.stream().map(Attr::new).collect(Collectors.toSet()));
        }
        return empty();
    }

    public static JSONObject newPropertyDefine() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("multi_select", new Object());
        return jsonObject;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class Attr {
        private String name;
    }
}
