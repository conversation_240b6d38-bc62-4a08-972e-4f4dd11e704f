package com.maixi.road.common.business.user.enums;

import com.maixi.road.common.core.enums.EnumInterface;

import lombok.Getter;

@Getter
public enum PromotionStatusEnum implements EnumInterface<PromotionStatusEnum> {

    UN_EXCHANGE(0, "未兑换"),
    EXCHANGED(1, "已兑换"),
    EXPIRED(2, "已过期"),
    DEFAULT(-1, "异常"),
    ;


    private final Integer code;
    private String msg;
    private final String desc;

    PromotionStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public PromotionStatusEnum getDefault() {
        return DEFAULT;
    }
}
