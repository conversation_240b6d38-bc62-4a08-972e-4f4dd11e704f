package com.maixi.road.common.business.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum OrderTypeEnum {

    REWARD(0, "打赏订单"),
    FOREVER_VIP(1, "永久会员订单"),
    YEAR_VIP(2, "年卡会员订单"),
    COUNT_BUY(3, "购买次数订单"),
    TWO_YEAR_VIP(4, "两年卡会员订单"),
    OB_FOREVER_VIP(5, "永久会员订单"),
    OB_YEAR_VIP(6, "年卡会员订单"),
    OB_TWO_YEAR_VIP(7, "两年卡会员订单"),
    ;

    private final Integer type;
    private final String desc;

    public static OrderTypeEnum getByType(Integer type) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.values()) {
            if (Objects.equals(orderTypeEnum.getType(), type)) {
                return orderTypeEnum;
            }
        }
        return null;
    }
}
