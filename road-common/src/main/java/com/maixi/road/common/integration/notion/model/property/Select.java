package com.maixi.road.common.integration.notion.model.property;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class Select {

    private Attr select;

    public static Select build(String item) {
        if (StringUtils.isNoneBlank(item)) {
            return new Select(new Attr(item));
        }
        return new Select(new Attr());
    }

    public static JSONObject newPropertyDefine() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("select", new Object());
        return jsonObject;
    }


    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class Attr {
        private String name;
    }
}
