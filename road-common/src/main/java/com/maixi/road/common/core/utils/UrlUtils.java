package com.maixi.road.common.core.utils;

import lombok.extern.slf4j.Slf4j;

import javax.xml.bind.DatatypeConverter;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

@Slf4j
public class UrlUtils {

    public static String getReferer(String url) {
        URI uri;
        try {
            uri = new URI(url);
            String domain = uri.getHost();
            // 去掉前缀 www. （如果需要）
            if (domain != null && domain.startsWith("www.")) {
                domain = domain.substring(4);
            }
            return domain;
        } catch (URISyntaxException e) {
            log.error("Error parsing URL: {}", e.getMessage());
        }
        return "";
    }

    /**
     * 从URL中提取文件名
     *
     * @param url 网络资源URL
     * @return 文件名
     */
    public static String getFileNameFromUrl(String url) {
        try {
            URI uri = URI.create(url);
            String path = uri.getPath();
            String fileName = path.substring(path.lastIndexOf('/') + 1);

            // 处理URL参数
            int queryIndex = fileName.indexOf('?');
            if (queryIndex > 0) {
                fileName = fileName.substring(0, queryIndex);
            }

            return generateMD5HexString(url) + "_" + fileName;
        } catch (Exception e) {
            return null;
        }
    }

    public static String generateMD5HexString(String input) {
        try {
            // 创建MD5哈希实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 计算输入的MD5哈希，生成128位（16字节）的哈希值
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为十六进制字符串（32个字符）
            String hexString = DatatypeConverter.printHexBinary(hashBytes).toLowerCase();

            log.info("generateMD5HexString: input: {}, hexString: {}", input, hexString);
            return hexString;
        } catch (Exception e) {
            log.error("generateMD5HexString: input: {}, error: {}", input, e.getMessage());
            return input;
        }
    }

}
