package com.maixi.road.common.core.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder

public class UserArticleDto {
    /**
     * 文章收藏数据库 ID
     */
    private String databaseId;
    /**
     * 文章链接字段名
     */
    private String url;
    /**
     * 文章来源字段名
     */
    private String origin;
    /**
     * 文章作者字段名
     */
    private String author;
    /**
     * 文章标签字段名
     */
    private String tag;
    /**
     * 创建时间字段名
     */
    private String createTime;
    /**
     * 发布时间字段名
     */
    private String publishTime;
    /**
     * 备注字段名
     */
    private String remark;
}
