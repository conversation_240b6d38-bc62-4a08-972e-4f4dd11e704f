package com.maixi.road.common.core.utils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件名处理工具类
 * 提供安全的文件名生成和清理功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
public class NameUtils {

    /**
     * 文件名友好的时间格式
     */
    private static final DateTimeFormatter SAFE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    /**
     * 默认的文件名长度限制
     */
    private static final int DEFAULT_MAX_LENGTH = 100;

    /**
     * Windows系统保留的文件名
     */
    private static final String[] WINDOWS_RESERVED_NAMES = {
            "CON", "PRN", "AUX", "NUL",
            "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
            "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
    };

    /**
     * 生成带时间戳的安全文件名
     * 
     * @param title           标题
     * @param timestampMillis 时间戳（毫秒）
     * @return 清理后的安全文件名
     */
    public static String generateSafeFileName(String title, Long timestampMillis) {
        return generateSafeFileName(title, timestampMillis, DEFAULT_MAX_LENGTH);
    }

    /**
     * 生成带时间戳的安全文件名
     * 
     * @param title           标题
     * @param timestampMillis 时间戳（毫秒）
     * @param maxLength       最大长度限制
     * @return 清理后的安全文件名
     */
    public static String generateSafeFileName(String title, Long timestampMillis, int maxLength) {
        if (StringUtils.isBlank(title)) {
            title = "未命名";
        }

        // 清理标题中的特殊字符，确保适合作为文件名
        title = sanitizeFileName(title);

        // 使用文件名友好的时间格式：yyyyMMdd_HHmmss
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestampMillis / 1000, 0, ZoneOffset.of("+8"));
        String safeTimeFormat = dateTime.format(SAFE_TIME_FORMAT);

        String fullTitle = title + "_" + safeTimeFormat;

        // 限制文件名长度，避免过长
        if (fullTitle.length() > maxLength) {
            // 保留时间戳部分，截断标题部分
            int timePartLength = safeTimeFormat.length() + 1; // +1 for underscore
            int maxTitleLength = maxLength - timePartLength;
            if (maxTitleLength > 0) {
                title = title.substring(0, Math.min(title.length(), maxTitleLength));
                fullTitle = title + "_" + safeTimeFormat;
            } else {
                // 如果时间戳本身都太长，只使用时间戳
                fullTitle = safeTimeFormat;
            }
        }

        log.debug("生成安全文件名：原标题={}, 时间戳={}, 最终文件名={}", title, timestampMillis, fullTitle);

        return fullTitle;
    }

    /**
     * 生成当前时间的安全文件名
     * 
     * @param prefix 文件名前缀
     * @return 安全文件名
     */
    public static String generateFileNameWithCurrentTime(String prefix) {
        if (StringUtils.isBlank(prefix)) {
            prefix = "file";
        }

        String safePrefix = sanitizeFileName(prefix);
        String timeFormat = LocalDateTime.now().format(SAFE_TIME_FORMAT);

        return safePrefix + "_" + timeFormat;
    }

    /**
     * 清理文件名中的非法字符
     * 移除或替换不适合文件名的字符
     *
     * @param fileName 原始文件名
     * @return 清理后的安全文件名
     */
    public static String sanitizeFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "未命名";
        }

        // 定义文件名中的非法字符（Windows + Linux + macOS）
        // Windows: < > : " | ? * \0 以及控制字符
        // Linux/macOS: \0 和 /（路径分隔符）
        String sanitized = fileName
                // 移除或替换特殊字符
                .replaceAll("[<>:\"|?*\\x00-\\x1f/\\\\]", "_") // 替换非法字符为下划线
                .replaceAll("\\s+", "_") // 将多个空格替换为单个下划线
                .replaceAll("_{2,}", "_") // 将多个连续下划线替换为单个下划线
                .replaceAll("^_+|_+$", "") // 移除开头和结尾的下划线
                .trim();

        // 处理Windows保留文件名
        String upperCased = sanitized.toUpperCase();
        for (String reserved : WINDOWS_RESERVED_NAMES) {
            if (reserved.equals(upperCased)) {
                sanitized = sanitized + "_file";
                break;
            }
        }

        // 确保文件名不为空
        if (StringUtils.isBlank(sanitized)) {
            sanitized = "未命名";
        }

        return sanitized;
    }

    /**
     * 获取文件名友好的时间格式字符串
     * 
     * @param timestampMillis 时间戳（毫秒）
     * @return 格式化的时间字符串
     */
    public static String formatTimestampForFileName(Long timestampMillis) {
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestampMillis / 1000, 0, ZoneOffset.of("+8"));
        return dateTime.format(SAFE_TIME_FORMAT);
    }

    /**
     * 获取当前时间的文件名友好格式
     * 
     * @return 格式化的当前时间字符串
     */
    public static String getCurrentTimeForFileName() {
        return LocalDateTime.now().format(SAFE_TIME_FORMAT);
    }
}