package com.maixi.road.common.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.maixi.road.common.business.order.enums.PaymentBizTypeEnum;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

@Slf4j
@Component
public class TradeNoGenerator {

    /**
     * 订单前缀标识
     */
//    @Value("${pay.order.mark}")
//    private static final String orderMark = "NMC";

    private static String orderPrefixMark = "NMC";

//    @PostConstruct
//    public void init() {
//        TradeNoGenerator.orderPrefixMark = orderMark;
//    }

    private static String generateNo(String tns, Long userId) {
        Assert.notNull(userId, "userId should not null");
        DateFormat dateFormat = new SimpleDateFormat("YYMMddHHmmss");
        String date = dateFormat.format(new Date());
        Long buyer = userId % (999);
        DecimalFormat df = new DecimalFormat("000");
        DecimalFormat df2 = new DecimalFormat("0000");
        Random r1 = new Random();
        String random1 = df.format(r1.nextInt(1000));
        Random r2 = new Random();
        String random2 = df2.format(r2.nextInt(10000));
        return tns + date + df.format(buyer) + random1 + random2;
    }

    public static String generateTradeNo(PaymentBizTypeEnum tns, Long userId) {
        return orderPrefixMark + "T" + generateNo(String.valueOf(tns.getCode()), userId);
    }

    public static String generateTradeNo(Integer code, Long userId) {
        return orderPrefixMark + "T" + generateNo(String.valueOf(code), userId);
    }

    public static String generateRefundNo(Integer code, Long userId) {
        return orderPrefixMark + "R" + generateNo(String.valueOf(code), userId);
    }
}
