package com.maixi.road.common.business.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum VipTypeEnum {

    NORMAL(0, "普通用户"),
    FOREVER_VIP(1, "永久会员"),
    YEAR_VIP(2, "年卡会员"),
    ;

    private final Integer type;
    private final String desc;

    public static VipTypeEnum getByType(Integer type) {
        for (VipTypeEnum vipTypeEnum : VipTypeEnum.values()) {
            if (vipTypeEnum.type.equals(type)) {
                return vipTypeEnum;
            }
        }
        return null;
    }
}
