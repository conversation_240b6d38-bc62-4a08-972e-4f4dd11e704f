package com.maixi.road.common.core.exception;

import com.maixi.road.common.core.enums.error.OrderErrCodeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderException extends RuntimeException {

    private int code;

    private String msg;

    public OrderException(int code, String msg) {
        super(toMsg(code, msg));
        this.code = code;
        this.msg = msg;
    }

    public OrderException(OrderErrCodeEnum error) {
        this(error.getCode(), error.getMsg());
    }

    private static String toMsg(int code, String msg) {
        return "[" + code + "] " + msg;
    }

    public static OrderException create(OrderErrCodeEnum error) {
        return new OrderException(error);
    }

    public static OrderException create(OrderErrCodeEnum error, String errorMsg) {
        return new OrderException(error.getCode(), errorMsg);
    }
}
