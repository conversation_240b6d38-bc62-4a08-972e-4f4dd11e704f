package com.maixi.road.common.core.utils;

import java.util.HashSet;
import java.util.Random;
import java.util.Set;

public class PromotionCodeGenerator {

    private static final String CHAR_POOL = "abcdefghijklmnopqrstuvwxyz";
    private static final int CODE_LENGTH = 6;
    private static final Random RANDOM = new Random();

    /**
     * 生成指定数量的不重复的分享码
     *
     * @param count 分享码数量
     * @return 唯一分享码集合
     */
    public static Set<String> generateShareCodes(int count) {
        if (count > Math.pow(CHAR_POOL.length(), CODE_LENGTH)) {
            throw new IllegalArgumentException("分享码数量超过可能的最大组合数！");
        }

        Set<String> codes = new HashSet<>(count);
        while (codes.size() < count) {
            String code = generateRandomCode();
            codes.add(code); // 使用 Set 自动去重
        }
        return codes;
    }

    /**
     * 随机生成一个 6 位分享码
     *
     * @return 分享码
     */
    public static String generateRandomCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CHAR_POOL.charAt(RANDOM.nextInt(CHAR_POOL.length())));
        }
        return code.toString();
    }
}
