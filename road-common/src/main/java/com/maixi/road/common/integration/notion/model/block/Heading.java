package com.maixi.road.common.integration.notion.model.block;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

import com.maixi.road.common.integration.notion.model.common.RichText;


@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Heading {
    private List<RichText> rich_text;
    /**
     * 为 true 就是 toggle list
     */
    private boolean is_toggleable;
    private String color;

    public static Heading buildHeading(String text) {
        return new Heading(Collections.singletonList(RichText.simpleText(text)), false, "default");
    }

    public List<RichText> getRich_text() {
        return rich_text;
    }

    public void setRich_text(List<RichText> rich_text) {
        this.rich_text = rich_text;
    }

    public boolean isIs_toggleable() {
        return is_toggleable;
    }

    public void setIs_toggleable(boolean is_toggleable) {
        this.is_toggleable = is_toggleable;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }
}
