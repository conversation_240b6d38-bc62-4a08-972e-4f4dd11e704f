package com.maixi.road.common.core.utils;

import java.util.Map;

import org.slf4j.MDC;

public class MDCRunnable implements Runnable {
    private final Runnable delegate;
    private final Map<String, String> contextMap;

    public MDCRunnable(Runnable delegate) {
        this.delegate = delegate;
        // 捕获父线程的 MDC 上下文
        this.contextMap = MDC.getCopyOfContextMap();
    }

    @Override
    public void run() {
        if (contextMap != null) {
            MDC.setContextMap(contextMap);
        }
        try {
            delegate.run();
        } finally {
            MDC.clear();
        }
    }
}
