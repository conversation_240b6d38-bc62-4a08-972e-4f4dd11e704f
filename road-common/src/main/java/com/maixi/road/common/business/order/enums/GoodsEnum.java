package com.maixi.road.common.business.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum GoodsEnum {

    ONE_YEAR_MEMBER("annual_member", "会员-一年"),
    TWO_YEAR_MEMBER("two_year_member", "会员-两年"),
    PERMANENT_MEMBER("permanent_member", "永续会员"),
    OB_ONE_YEAR_MEMBER("ob_annual_member", "会员-一年"),
    OB_TWO_YEAR_MEMBER("ob_two_year_member", "会员-两年"),
    OB_PERMANENT_MEMBER("ob_permanent_member", "永续会员"),
    ;

    private final String goodsNo;
    private final String desc;

    public static GoodsEnum getByType(String goodsNo) {
        for (GoodsEnum orderTypeEnum : GoodsEnum.values()) {
            if (Objects.equals(orderTypeEnum.getGoodsNo(), goodsNo)) {
                return orderTypeEnum;
            }
        }
        return null;
    }
}
