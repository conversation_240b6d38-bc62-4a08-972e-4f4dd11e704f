package com.maixi.road.common.core.utils;

import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

public class JsonUtil {
    public static boolean isJson(String json) {
        try {
            JsonParser.parseString(json);
            return true; // 如果没有抛出异常，则认为是JSON格式
        } catch (JsonSyntaxException jse) {
            return false; // 如果抛出异常，则认为不是JSON格式
        }
    }
}
