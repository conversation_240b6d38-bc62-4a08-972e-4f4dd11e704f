package com.maixi.road.common.core.enums.error;

import com.maixi.road.common.core.enums.EnumInterface;

import lombok.Getter;

/**
 * <p>业务级别错误码</p>
 * F13000-F13999
 */
@Getter
public enum ImageCloudErrCodeEnum implements EnumInterface<ImageCloudErrCodeEnum> {

    DEFAULT_ERROR(99999, "Error", "Error"),


    VERIFY_FAIL(50000, "校验失败，请检查配置");

    private final Integer code;
    private final String msg;
    private String desc;

    ImageCloudErrCodeEnum(int code, String message) {
        this.code = code;
        this.msg = message;
    }

    ImageCloudErrCodeEnum(int code, String desc, String message) {
        this(code, message);
        this.desc = desc;
    }


    @Override
    public ImageCloudErrCodeEnum getDefault() {
        return DEFAULT_ERROR;
    }
}
