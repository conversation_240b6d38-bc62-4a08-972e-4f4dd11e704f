package com.maixi.road.common.service.redis.api;

import java.util.List;
import java.util.concurrent.Future;

import com.maixi.road.common.integration.notion.model.block.Block;

public interface RedisOpApi {

    /**
     * 保存剪切结果
     * @param url
     * @param blocks
     */
    void saveClipResult(String url, List<Block> blocks);

    /**
     * 获取剪切结果
     * @param url
     * @return
     */
    Future<List<Block>> getClipResult(String url);
}
