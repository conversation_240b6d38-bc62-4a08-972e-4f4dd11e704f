package com.maixi.road.common.core.model.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PromotionData {

    /**
     * 推广码
     */

    private String promotionCode;
    /**
     * 累计积分
     */

    private Integer sum;
    /**
     * 已使用积分
     */

    private Integer useSum;
    /**
     * 可用积分
     */

    private Integer availableSum;

    /**
     * 推广人次
     */
    private Integer shareCount;
}
