package com.maixi.road.common.integration.notion.model.common;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.common.integration.notion.enums.ColorEnum;
import com.maixi.road.common.integration.notion.enums.RichTextEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/7/26
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RichText {
    /**
     *
     */
    private String type;
    private Text text;
    private Annotations annotations;
    private String plain_text;
    private String href;

    // fixme equation 暂时不支持
    // private Equation equation;
    // fixme 注意 mention 类型暂时不支持
    // private Mention mention

    public static RichText simpleText(String content) {
        RichText richText = new RichText();
        richText.setText(Text.simpleContent(content));
        richText.setPlain_text(content);
        richText.setHref(null);
        richText.setType(RichTextEnum.text.getType());
        richText.setAnnotations(Annotations.defaultAnnotations());
        return richText;
    }

    public static RichText boldText(String content) {
        RichText richText = new RichText();
        richText.setText(Text.simpleContent(content));
        richText.setPlain_text(content);
        richText.setHref(null);
        richText.setType(RichTextEnum.text.getType());
        richText.setAnnotations(Annotations.bold());
        return richText;
    }

    public static RichText grayItalicText(String content) {
        RichText richText = new RichText();
        richText.setText(Text.simpleContent(content));
        richText.setPlain_text(content);
        richText.setHref(null);
        richText.setType(RichTextEnum.text.getType());
        richText.setAnnotations(Annotations.grayItalic());
        return richText;
    }

    public static RichText colorText(String content, ColorEnum color) {
        RichText richText = new RichText();
        richText.setText(Text.simpleContent(content));
        richText.setPlain_text(content);
        richText.setHref(null);
        richText.setType(RichTextEnum.text.getType());
        richText.setAnnotations(Annotations.colorBy(color));
        return richText;
    }

    public static RichText simpleRedText(String content) {
        RichText richText = new RichText();
        richText.setText(Text.simpleContent(content));
        richText.setPlain_text(content);
        richText.setHref(null);
        richText.setType(RichTextEnum.text.getType());
        richText.setAnnotations(Annotations.red());
        return richText;
    }

    public static RichText textWithLink(String content, String link) {
        RichText richText = new RichText();
        richText.setText(Text.simpleContentWithLink(content, link));
        richText.setPlain_text(content);
        richText.setHref(link);
        richText.setType(RichTextEnum.text.getType());
        richText.setAnnotations(Annotations.defaultAnnotations());
        return richText;
    }

    public static RichText textWithBlueLink(String content, String link) {
        RichText richText = new RichText();
        richText.setText(Text.simpleContentWithLink(content, link));
        richText.setPlain_text(content);
        richText.setHref(link);
        richText.setType(RichTextEnum.text.getType());
        richText.setAnnotations(Annotations.colorBy(ColorEnum._blue));
        return richText;
    }

    public static JSONObject textForProperty(String content) {
        if (StringUtils.isBlank(content)) {
            return new JSONObject().fluentPut("rich_text", Collections.emptyList());
        }
        RichText richText = new RichText();
        richText.setText(Text.simpleContent(content));
        return new JSONObject().fluentPut("rich_text", Collections.singletonList(richText));
    }

    public static JSONObject newPropertyDefine() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.fluentPut("rich_text", new Object());
        return jsonObject;
    }

    public static boolean compare(RichText a, RichText b) {
        if (a == null || b == null) {
            return false;
        }
        return ((a.getHref() == null && b.getHref() == null) || Objects.equals(a.getHref(), b.getHref()))
                && Annotations.compare(a.getAnnotations(), b.getAnnotations())
                && a.getType().equals(b.getType());
    }

    public static RichText copy(RichText richText) {
        RichText newRichText = new RichText();
        newRichText.setType(richText.getType());
        newRichText.setText(Text.copy(richText.getText()));
        newRichText.setAnnotations(Annotations.copy(richText.getAnnotations()));
        newRichText.setPlain_text(richText.getPlain_text());
        newRichText.setHref(richText.getHref());
        return newRichText;
    }
}
