package com.maixi.road.common.integration.notion.model.block;

import com.google.common.collect.Lists;
import com.maixi.road.common.integration.notion.enums.ColorEnum;
import com.maixi.road.common.integration.notion.model.common.RichText;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * shift 换行用 \n
 * 回车换行，用下一个 rich_text
 * 段落换行，用空的 paragraph
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RichTexts {

    /**
     *
     */
    private String color;
    private List<RichText> rich_text;

    /**
     * 用于段落换行
     */
    public static final RichTexts EMPTY = empty();

    public static RichTexts build(List<RichText> richTexts) {
        RichTexts paragraph = new RichTexts();
        paragraph.setColor("default");
        paragraph.setRich_text(richTexts);
        return paragraph;
    }

    public static RichTexts redBuild(List<RichText> richTexts) {
        RichTexts paragraph = new RichTexts();
        paragraph.setColor(ColorEnum._red.getColor());
        paragraph.setRich_text(richTexts);
        return paragraph;
    }

    private static RichTexts empty() {
        RichTexts paragraph = new RichTexts();
        paragraph.setColor("default");
        paragraph.setRich_text(Collections.emptyList());
        return paragraph;
    }

    public static RichTexts fail(String url) {
        RichTexts paragraph = new RichTexts();
        paragraph.setColor("default");
        paragraph.setRich_text(
                Lists.newArrayList(RichText.simpleText("文章解析失败，可重试一下。\n或将以下 url 反馈给开发者（wx: NotionHelper）\n"),
                        RichText.simpleText(url)));
        return paragraph;
    }

    public static RichTexts blockSizeTooLarge(String url) {
        RichTexts paragraph = new RichTexts();
        paragraph.setColor("default");
        paragraph.setRich_text(
                Lists.newArrayList(RichText.simpleText("文章内容超长导致同步失败。\n或将以下 url 反馈给开发者（wx: NotionHelper）\n"),
                        RichText.simpleText(url)));
        return paragraph;
    }


}
