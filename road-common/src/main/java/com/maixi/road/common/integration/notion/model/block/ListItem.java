package com.maixi.road.common.integration.notion.model.block;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

import com.maixi.road.common.integration.notion.model.common.RichText;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ListItem {
    private List<RichText> rich_text;
    private String color;
    private List<Block> children;

    public static ListItem buildItem(String content) {
        return ListItem.builder()
                .color("default")
                .rich_text(Collections.singletonList(RichText.simpleText(content)))
                .build();
    }

    public static ListItem buildItem(RichText richText) {
        return ListItem.builder()
                .color("default")
                .rich_text(Collections.singletonList(richText))
                .build();
    }

    public static ListItem buildItem(List<RichText> richTexts) {
        return ListItem.builder()
                .color("default")
                .rich_text(richTexts)
                .build();
    }
}
