package com.maixi.road.common.integration.notion.model.property;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class Name {
    private List<Title> title;

    public static Name simpleName(String name) {
        return new Name(Collections.singletonList(Title.simpleTitle(name)));
    }
}
