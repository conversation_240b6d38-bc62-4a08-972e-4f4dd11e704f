package com.maixi.road.common.core.enums.error;

import com.maixi.road.common.core.enums.EnumInterface;

import lombok.Getter;

/**
 * <p>业务级别错误码</p>
 * F13000-F13999
 */
@Getter
public enum OrderErrCodeEnum implements EnumInterface<OrderErrCodeEnum> {

    DEFAULT_ERROR(99999, "Error", "Error"),

    // 用户相关
    USER_NOT_EXIST(30001, "用户不存在"),
    USER_UNAUTHORIZED_OPERATION(30001, "用户无权限操作"),
    USER_MISSING_OPENID(30002, "用户缺失OPENID"),


    GOOD_INVALID_STATUS(31001, "商品已下架"),
    GOOD_PRICE_INCORRECT(31002, "商品价格不正确"),


    MARKETING_PROMOTION_CODE_INVALID(32001, "优惠码无效"),

    ORDER_UNKNOWN_ORDER_TYPE(33001,"未知的订单类型"),
    ORDER_NO_NEED_TO_BUY(33002,"当前用户无需购买"),
    ORDER_SELL_OUT(33003,"当前商品已售罄"),
    ORDER_BUY_ONLY_ONCE(33004,"仅允许购买一次"),
    ORDER_NOT_EXIST(33005,"订单不存在"),
    ORDER_PRICE_INCORRECT(33005,"订单价格不正确"),
    ORDER_NOT_BELONG_YOU(33005,"订单不属于此用户"),

    PAYMENT_NOT_EXIST(34001,"支付单不存在"),


    ;

    private final Integer code;
    private final String msg;
    private String desc;

    OrderErrCodeEnum(int code, String message) {
        this.code = code;
        this.msg = message;
    }

    OrderErrCodeEnum(int code, String desc, String message) {
        this(code, message);
        this.desc = desc;
    }


    @Override
    public OrderErrCodeEnum getDefault() {
        return DEFAULT_ERROR;
    }
}
