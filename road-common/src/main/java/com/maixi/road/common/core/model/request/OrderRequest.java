package com.maixi.road.common.core.model.request;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class OrderRequest {

    /**
     * 商品编号
     */

    @NotBlank(message = "goodsNo不能为空")
    private String goodsNo;

    /**
     * 商品金额
     */
    @NotNull(message = "amount不能为空")
    private Integer amount;

    /**
     * 优惠码
     */

    private String discountCode;
}
