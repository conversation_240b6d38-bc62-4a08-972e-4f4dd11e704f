package com.maixi.road.common.core.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import com.maixi.road.common.core.constant.CommonConstants;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Date;

@Slf4j
public class JwtUtil {
    private final static String secretKey = "84MS9XFazSK82ui9tmvhJ6n/+C7XHDnSvig3/9nNer/plGu998xxhIIda1u+b3E7XTf5oYWt5VBbZMSp7VzdFw==";
    private final static Duration expiration = Duration.ofDays(30);
    private static final SecretKey key = Keys.hmacShaKeyFor(secretKey.getBytes(StandardCharsets.UTF_8));

    /**
     * 生成 jwt token
     *
     * @param unionId unionId
     * @return token
     */
    public static String createToken(String unionId) {
        // 过期时间
        Date expiryDate = new Date(System.currentTimeMillis() + expiration.toMillis());
        return Jwts.builder()
                .subject(unionId) // 将 unionId 放进JWT
                .issuedAt(new Date()) // 设置JWT签发时间
                .expiration(expiryDate) // 设置过期时间
                .signWith(key) // 设置加密算法和秘钥
                .compact();
    }

    /**
     * 解析JWT
     *
     * @param token JWT字符串
     * @return 解析成功返回Claims对象，解析失败返回null
     */
    public static Claims parse(String token) {
        if (StringUtils.isEmpty(token)) {
            return null;
        }

        Claims claims = null;
        try {
            claims = Jwts.parser().verifyWith(key).build().parseSignedClaims(token).getPayload();
        } catch (JwtException e) {
            log.error("登录token解析失败,{}", e.getMessage());
        }
        return claims;
    }

    public static void main(String[] args) {

        String token = createToken(CommonConstants.TEST_UNION_ID);
        log.info("token:{}", token);
        Claims parse = parse(token);
        log.info("parse:{}", parse.getSubject());
    }

}
