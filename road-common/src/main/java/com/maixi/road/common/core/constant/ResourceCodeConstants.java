package com.maixi.road.common.core.constant;

public class ResourceCodeConstants {

    // 永久会员数量限制
    public static final String PERMANENT_LIMIT = "five_year_limit";
    // 会员优惠金额
    public static final String DISCOUNT_AMOUNT = "discountAmount";

    public static final String PROMOTION_CREDIT = "promotion_credit";
    // 会员购买入口开关
    public static final String VIP_OPEN = "vip_open";

    public static final String BLOCK_SIZE_LIMIT = "block_size_limit";


    // 剪藏次数开关限制
    public static final String COUNT_LIMIT_OPEN = "count_limit_open";
    public static final String DAY_LIMIT = "day_limit";
    public static final String MONTH_LIMIT = "month_limit";

    public static final Integer DEFAULT_DISCOUNT_AMOUNT = 600;

    public static final Integer DEFAULT_PROMOTION_CREDIT = 3;

    public static final Integer DEFAULT_FOREVER_VIP_LIMIT_COUNT = 100;

    public static final String SEND_2_NOTION_USE_CLOUD_FUNCTION = "send2notion_by_CloudFunction";

    public static final String APPEND_2_NOTION_USE_CLOUD_FUNCTION = "append2notion_by_CloudFunction";

    public static final String UPDATE_2_NOTION_USE_CLOUD_FUNCTION = "update2notion_by_CloudFunction";

    public static final String SEARCH_NOTION_USE_CLOUD_FUNCTION = "searchNotion_by_CloudFunction";

    public static final String CREATE_TOKEN_USE_CLOUD_FUNCTION = "createToken_by_CloudFunction";

    public static final String OPEN_ORDER_CHECK_JOB = "order_check_job_switch";
    public static final String MAX_IMAGE_SIZE_LIMIT = "max_image_size_limit";
}
