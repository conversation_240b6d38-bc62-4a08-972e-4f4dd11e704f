package com.maixi.road.common.integration.s3.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * S3 配置对象
 * 包含连接到S3兼容存储所需的所有配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class S3Config {
    /**
     * 用户ID（可选，用于缓存标识）
     */
    private String userId;
    
    /**
     * 提供商类型：aws, minio, aliyun 等
     */
    private String provider;
    
    /**
     * 服务端点
     */
    private String endpoint;
    
    /**
     * 访问密钥
     */
    private String accessKey;
    
    /**
     * 访问密钥
     */
    private String secretKey;
    
    /**
     * 区域
     */
    private String region;
    
    /**
     * 存储桶
     */
    private String bucket;
    
    /**
     * 对象前缀（可选）
     */
    private String prefix;
    
    /**
     * 是否使用路径风格访问
     */
    @Builder.Default
    private boolean pathStyle = false;
    
    /**
     * 额外参数,自定义域名
     */
    @Builder.Default
    private Map<String, String> extraParams = new HashMap<>();
    
    /**
     * 获取额外参数
     * 
     * @param key 参数键
     * @return 参数值
     */
    public String getExtraParam(String key) {
        return extraParams != null ? extraParams.get(key) : null;
    }
    
    /**
     * 设置额外参数
     * 
     * @param key 参数键
     * @param value 参数值
     */
    public void setExtraParam(String key, String value) {
        if (extraParams == null) {
            extraParams = new HashMap<>();
        }
        extraParams.put(key, value);
    }
} 