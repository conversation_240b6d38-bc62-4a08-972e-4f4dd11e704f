package com.maixi.road.common.integration.notion.model.block;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.maixi.road.common.integration.notion.model.common.RichText;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Code {

    /**
     * 可以忽略，直接设置为空
     */
    private List<String> caption;
    private List<RichText> rich_text;
    private String language;
}
