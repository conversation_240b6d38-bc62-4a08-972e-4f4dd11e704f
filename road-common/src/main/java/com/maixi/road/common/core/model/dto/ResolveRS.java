package com.maixi.road.common.core.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.maixi.road.common.integration.notion.model.block.Block;

/**
 * URL解析结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResolveRS {
    
    /**
     * 文章标题
     */
    private String title;
    
    /**
     * 作者
     */
    private String author;
    
    /**
     * 文章链接
     */
    private String link;

    /**
     * 图标URL
     */
    private String icon;
    
    /**
     * 封面图片URL
     */
    private String cover;
    
    /**
     * 来源
     */
    private String origin;
    
    /**
     * 网站名称
     */
    private String siteName;
    
    /**
     * 发布时间
     */
    private String publishTime;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 内容块列表（JSON格式）
     */
    private List<Block> blocks;

    /**
     * 分类
     */
    private String category;
}
