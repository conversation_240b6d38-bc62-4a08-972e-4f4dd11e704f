package com.maixi.road.common.core.model.response;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder

public class UserMessageDto {

    /**
     * 文章收藏数据库 ID
     */
    private String databaseId;
    /**
     * 消息类型字段名
     */
    private String type;
    /**
     * 消息标签字段名
     *
     * @version 2023-12-05
     */
    private String tag;
    /**
     * 创建时间字段名
     */
    private String createTime;
}
