package com.maixi.road.common.integration.notion.tags;

import com.maixi.road.common.integration.notion.enums.RichTextEnum;
import com.maixi.road.common.integration.notion.model.common.Annotations;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.integration.notion.model.common.Text;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TextTag {

    private String content;
    private String href;
    private String color;
    /**
     * // 0000000
     * 1000000 粗体 64
     * 0100000 斜体 32
     * 0010000 删除线 16
     * 0001000 下划线 8
     * 0000100 代码 4
     * 0000010 颜色 2
     * 0000001 链接 1
     */
    private Integer annotations;

    // 样式来源标记位，用于区分样式是显式设置还是继承获得
    // 使用位运算，每一位表示一个样式属性的来源
    // 0: 默认/继承, 1: 显式设置
    private int styleSourceFlags = 0;

    // 样式来源标记位常量（复用TextTagProperty中的常量）
    public static final int HREF_EXPLICIT = 1; // 0001: href显式设置
    public static final int COLOR_EXPLICIT = 2; // 0010: color显式设置
    public static final int BOLD_EXPLICIT = 4; // 0100: bold显式设置
    public static final int ITALIC_EXPLICIT = 8; // 1000: italic显式设置

    /**
     * 检查bold是否为显式设置
     */
    public boolean isBoldExplicit() {
        return (this.styleSourceFlags & BOLD_EXPLICIT) != 0;
    }

    /**
     * 检查italic是否为显式设置
     */
    public boolean isItalicExplicit() {
        return (this.styleSourceFlags & ITALIC_EXPLICIT) != 0;
    }

    /**
     * 检查color是否为显式设置
     */
    public boolean isColorExplicit() {
        return (this.styleSourceFlags & COLOR_EXPLICIT) != 0;
    }

    /**
     * 检查href是否为显式设置
     */
    public boolean isHrefExplicit() {
        return (this.styleSourceFlags & HREF_EXPLICIT) != 0;
    }

    /**
     * 标记bold为显式设置
     */
    public void markBoldExplicit() {
        this.styleSourceFlags |= BOLD_EXPLICIT;
    }

    /**
     * 标记italic为显式设置
     */
    public void markItalicExplicit() {
        this.styleSourceFlags |= ITALIC_EXPLICIT;
    }

    /**
     * 标记color为显式设置
     */
    public void markColorExplicit() {
        this.styleSourceFlags |= COLOR_EXPLICIT;
    }

    /**
     * 标记href为显式设置
     */
    public void markHrefExplicit() {
        this.styleSourceFlags |= HREF_EXPLICIT;
    }

    public static TextTag buildTextTag(String content,TextTagProperty property){
        return TextTag.builder()
                .content(content)
                .href(property.getHref())
                .color(property.getColor())
                .annotations(property.getAnnotations())
                .styleSourceFlags(property.getStyleSourceFlags())
                .build();
    }

    public RichText toRichText() {
        RichText richText = new RichText();
        richText.setText(Text.simpleContent(content));
        richText.setPlain_text(content);
        richText.setType(RichTextEnum.text.getType());
        Annotations annotations = new Annotations();
        annotations.setBold((this.annotations & (1 << 6)) != 0);
        annotations.setItalic((this.annotations & (1 << 5)) != 0);
        annotations.setStrikethrough((this.annotations & (1 << 4)) != 0);
        annotations.setUnderline((this.annotations & (1 << 3)) != 0);
        annotations.setCode((this.annotations & (1 << 2)) != 0);
        if ((this.annotations & (1 << 1)) != 0) {
            annotations.setColor(this.color);
        }
        if ((this.annotations & 1) != 0) {
            richText.setHref(href);
            richText.setText(Text.simpleContentWithLink(content, href));
        }
        richText.setAnnotations(annotations);
        return richText;
    }
}