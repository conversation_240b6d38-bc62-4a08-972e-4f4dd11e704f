package com.maixi.road.common.core.constant;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;

/**
 * <AUTHOR> * @since 2023/7/26
 **/
public class CommonConstants {

    /**
     * Redis RMap 的 Key，用于存储所有 PropertyRule 分组数据
     */
    public static final String PROPERTY_RULES_RMAP_KEY = "property_rules_collection";

    public static final boolean isWindows = System.getProperty("os.name").toLowerCase().contains("win");
    public static final boolean isLinux = System.getProperty("os.name").toLowerCase().contains("linux");

    public static final String TEST_UNION_ID = "osb1655YhzjZS4MrrpHnMOfiAY8Q";
    public static final String TEST_OPEN_ID = "o-tho5dc_Qo5jHlZyp438le8CIpE";

    public static final String WX_USER_SECRET_PREFIX = "绑定口令:春暖花开@secret:";
    public static final int WX_USER_SECRET_PREFIX_LENGTH = WX_USER_SECRET_PREFIX.length();

    public static final Pattern domainPattern = Pattern.compile("^(https?://)?(www\\.)?(mp\\.weixin\\.qq\\.com|sspai\\.com|(m\\.)?douban\\.com|bilibili\\.com|woshipm\\.com|xiaohongshu\\.com|xhslink\\.com|zhuanlan\\.zhihu\\.com|zhihu\\.com)(/\\S*)?$");
    public static final Pattern shortLinkPattern = Pattern.compile("(https?://)?(www\\.)?(xhslink\\.com)(/[a-zA-Z0-9]+)?(/[a-zA-Z0-9]+)?，");

    public static final Pattern webUrlRegex = Pattern.compile("(https?://)?([\\w\\-]+\\.)+[\\w\\-]+(/[\\w\\-._~:/?#\\\\@!$&'()*+,;=%]*)?");


    public static final ExecutorService RESOLVER_POOL = Executors.newVirtualThreadPerTaskExecutor();

    public static final String SPACE = " ";
    private static final byte[] bytes = {(byte) 0xC2, (byte) 0xA0};
    public static final String UTFSpace = new String(bytes, StandardCharsets.UTF_8);


    public static final String priKey = "-----BEGIN RSA PRIVATE KEY-----\n" +
            "MIIEpAIBAAKCAQEArQUF71C8aB2HiqV4iGMObEUhlgHMgvqpd6PfdBTpx1rO2cuJ\n" +
            "MgUSnDXc6gfOX5xXPTgi12QvYCIfX7GwrjRAJBoMgXtemC84sZg+/YGcf+UGZc00\n" +
            "UroZOg3Y2jjKSLhcd9tt4m7UatSFqWT2TORMcqSav2d3PjrQ/foazE54PMCQV24V\n" +
            "wmqSmQdlI8/DMA8Cm2GE5B875sRLyzBpb+JsOFzG3vrLrmlet98QecpqSF4p15T1\n" +
            "eMXGouaTiQfCXeQKv+2WmzlZJb9VYi1hpQjkLb7hz3eqfGiO0kTk9twLnnh3vNl+\n" +
            "BhUi/2jfVW0/fYB28yKAHh0ZyVI8Ps60wn6POQIDAQABAoIBAQCRK4HLRmaOxRgQ\n" +
            "0L3NUa+sUGTZxDCgRUkjxS+b6mAskJi2TPyUJ4Vx5A+znVEfD8Jy70Lsjnbhpj/E\n" +
            "vwaLY/PdVq3fhm/cKOLcv+06LyHsfXFcwUyBjPvVpgCpOLUhRu7u8gHE11ELQb3U\n" +
            "X434/iTWnB3rMxjt8dOhBdSuT7i630YQO7kSilWuzC8FdfjgSsw1by2P1ipIcIA0\n" +
            "hvQW4swb0L88KxdzbTEp/uGkhSbGFS9+JapJ67joSGzTnClveuKJoLYqw7ypS1Ws\n" +
            "yu73xQDokLyF1XKKIToBQDj8OKEklv93Jz8QKGLXdrSHTMLlXTNfd+roicRe7S3W\n" +
            "UAOL2CxxAoGBANNJfw8YYCEkYK+RfK7947wi69O/f7fCbkHaED/TRwKYbgjS7fuO\n" +
            "O/lc3yDFKys3C2ZIs10CadDgYy4tFJ+5266zBCScmBBorNdJpsrLNxnT/t7siJIm\n" +
            "Bvr8dGp8LyFaaLiZTCRI7ViLTY78N3jvCmpMdqRtMYOY2bn/LSGOwaF/AoGBANGi\n" +
            "YcUtiQR93EuthumPIf/Cw+2+kxEQHjTS+upJnOg041APURV7WU3uthBHVW5aZa1A\n" +
            "dSM39yBBeTkYDGadFcbdgM7Pc3UcTnE+8MsEjsQMiNjc2pveSZKhwTwqx0vliMp9\n" +
            "CbygM03VjLkusIEtwV96zPUbu/vmm5IngbjprbtHAoGABo0Q03b+BAkMtmTcaNCW\n" +
            "bw7mIPqfCyOMJ56LQm9alEoFn2Bq4JHYajLm+C0k2YIraFB4vzmD7mYGrTfYm9s3\n" +
            "ZINIbZotXNTiBwrRkRgb7UEVdfP/iAiFzuJk+ahADdivxol6QuW/9i7biMNpNOa0\n" +
            "e/dC9nYsQthKZ+nxjEjQ2CECgYEAgECnLjoIjM3vnAEi+/tMoqvmsKoaUaMZx9mY\n" +
            "FwRIUawhsdZdFhZr8YjsrBLoSmZVtGQuBgj0UTCdWHfFBnZuX0ceyegTS/+wYgpO\n" +
            "yPYV3g2JH5wmxpt9SY9aqFPHsEBSqwdKkLM2EBVpTdYpuC5lEuQPRpncLWXNoeI3\n" +
            "tAiK+HsCgYBDZADtk4Lu2PUXEfWNdrSn3/i3Nu1EeIhkpQp1ixDfbfsQ5vvmy7Eu\n" +
            "mQitzjXJmJuKOyYa+ujGCPc4lkS8V3V6oW/RojymjXHXXxpRGfo3n5ahDjEQz5E9\n" +
            "PI9xzVLpHT39/CTd20kMHMuhZrw4se9N9VJ9LqZkIYfqrBUAXe65YQ==\n" +
            "-----END RSA PRIVATE KEY-----\n";

}