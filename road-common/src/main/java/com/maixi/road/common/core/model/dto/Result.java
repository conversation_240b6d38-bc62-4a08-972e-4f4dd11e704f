package com.maixi.road.common.core.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class Result<T> implements Serializable {
    /**
     * 序列ID
     */
    @Serial
    private static final long serialVersionUID = -171900173982346605L;

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 本次服务调用是否成功
     */
    private boolean success;

    /**
     * 响应码, 默认为0，表示接口未出现任何异常
     */
    private int code = 999;

    /**
     * 如果失败，返回失败原因
     */
    private String msg;

    /**
     * 错误上下文
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ErrorContext errorContext;

    /**
     * 结果对象
     */
    private T data;

    /**
     * 默认构造方法
     */
    public Result() {
    }

    /**
     * 全参数构造方法
     *
     * @param success      返回状态
     * @param code         返回code
     * @param msg          返回消息，通常异常情况下使用
     * @param errorContext 异常信息
     * @param data         返回结果对象
     */
    public Result(final boolean success, final int code, final String msg,
                  final ErrorContext errorContext, final T data) {
        this.success = success;
        this.code = code;
        this.msg = msg;
        this.errorContext = errorContext;
        this.data = data;
    }

    /**
     * 部分参数构造方法
     *
     * @param success      返回状态
     * @param errorContext 异常信息
     * @param data         返回对象
     */
    public Result(final boolean success, final ErrorContext errorContext, final T data) {
        this.success = success;
        this.errorContext = errorContext;
        this.data = data;
    }

    public static <T> Result<T> fail(final String errorMsg) {
        return fail(ErrorCodeEnum.BIZ_ERROR.getCode(), errorMsg);
    }

    public static <T> Result<T> fail(int code, final String errorMsg) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMsg(errorMsg);
        result.setSuccess(Boolean.FALSE);
        return result;
    }

    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(ErrorCodeEnum.RESPONSE_SUCCESS.getCode());
        result.setSuccess(Boolean.TRUE);
        result.setData(data);
        return result;
    }


}
