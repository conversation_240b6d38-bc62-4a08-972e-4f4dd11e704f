package com.maixi.road.common.service.redis.constant;


import com.maixi.road.common.core.utils.UrlUtils;

public class RedisKeys {

    /**
     * 图片转换过程标志
     */
    private static final String pic_trans_flag_key = "transform_picture_has_finished_%s";
    /**
     * 文章解析过程标记
     */
    private static final String page_resolve_flag_key = "resolveCacheFlagSet_%s_%s";
    /**
     * 用户同步文章次数
     */
    private static final String sum_article_key = "articleSum:%s";
    /**
     * 用户同步消息次数
     */
    private static final String sum_text_key = "textSum:%s";
    /**
     * 用户分享次数
     */
    private static final String sum_share_key = "shareSum:%s";
    /**
     * 文章正文内容缓存
     */
    private static final String article_blocks_key = "article:%s";

    /**
     * 微信助手绑定的 secret 对应的 secret
     */
    private static final String wechat_helper_bind_unionId_key = "wx_user_bind_unionId@%s";
    /**
     * 微信助手绑定的 secret 对应的 unionId
     */
    private static final String wechat_helper_bind_secret_key = "wx_user_bind_secret@%s";


    public static String getPicTransFlagKey(String unionId) {
        return String.format(pic_trans_flag_key, unionId);
    }

    public static String getPageResolveFlagKey(String unionId, String link) {
        return String.format(page_resolve_flag_key, unionId, link);
    }



    public static String getSumArticleKey(String unionId) {
        return String.format(sum_article_key, unionId);
    }

    public static String getSumTextKey(String unionId) {
        return String.format(sum_text_key, unionId);
    }

    public static String getSumShareKey(String unionId) {
        return String.format(sum_share_key, unionId);
    }

    /**
     * 给 link 做一下 SHA256 编码
     *
     * @param link 网页链接
     */
    public static String getArticleBlocksKey(String link) {
        return String.format(article_blocks_key, UrlUtils.generateMD5HexString(link));
    }


    public static String getWechatBindUnionIdKey(String unionId) {
        return String.format(wechat_helper_bind_unionId_key, unionId);
    }

    public static String getWechatBindSecretKey(String secretKey) {
        return String.format(wechat_helper_bind_secret_key, secretKey);
    }
}
