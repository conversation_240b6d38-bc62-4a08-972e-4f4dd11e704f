package com.maixi.road.common.core.model.dto;

import lombok.Data;

@Data
public class GloablConfig {

    private int blockSizeLimit;
    private int maxImageSizeLimit;
    private boolean send2NotionUseCloudFunction;
    private boolean append2NotionUseCloudFunction;
    private boolean update2NotionUseCloudFunction;
    private boolean searchNotionUseCloudFunction;
    private boolean createTokenUseCloudFunction;
}
