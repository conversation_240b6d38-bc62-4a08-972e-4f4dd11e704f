# Road Server 项目规范

## 代码风格

1. **Java 代码风格**
   - 使用 Java 21 特性，包括虚拟线程、记录模式等
   - 类名使用 PascalCase，方法名和变量名使用 camelCase
   - 常量使用大写下划线命名法 (UPPER_SNAKE_CASE)
   - 包名全部小写，使用点分隔符
   - 接口名使用 "I" 前缀
   - 实现类使用 "Impl" 后缀

2. **注释规范**
   - 类、接口、枚举必须有文档注释，说明用途
   - 公共方法必须有文档注释，说明功能、参数和返回值
   - 复杂逻辑必须有行内注释
   - 使用 TODO、FIXME 等标记未完成或需要修复的代码

3. **代码格式**
   - 缩进使用 4 个空格
   - 行宽不超过 120 字符
   - 花括号使用 K&R 风格（左花括号不换行）
   - 方法之间空一行
   - 相关的变量声明放在一起
   - import 语句按字母顺序排序，不使用通配符导入

## 架构规范

1. **模块化**
   - 严格遵守模块边界，避免循环依赖
   - 模块间通过接口通信，不直接依赖实现类
   - 公共代码放在 road-common 模块中
   - 模块内部实现对外不可见

2. **分层架构**
   - 遵循经典三层架构：控制层、服务层、数据访问层
   - 控制层 (Controller) 负责请求处理和响应
   - 服务层 (Service) 负责业务逻辑
   - 数据访问层 (DAO/Repository) 负责数据操作
   - DTO 用于层间数据传输，避免直接暴露实体类

3. **依赖注入**
   - 优先使用构造器注入，其次使用字段注入
   - 使用 @Resource 注解进行依赖注入
   - 避免在构造函数中执行复杂逻辑

## 异常处理

1. **异常设计**
   - 使用自定义异常类表达业务异常
   - 异常类应当包含错误码和错误信息
   - 不捕获异常后不处理（空 catch 块）
   - 避免吞噬异常，确保异常信息被记录

2. **全局异常处理**
   - 使用 @ControllerAdvice 和 @ExceptionHandler 进行全局异常处理
   - 区分业务异常和系统异常
   - 统一异常响应格式

## 日志规范

1. **日志使用**
   - 使用 SLF4J + Logback 作为日志框架
   - 使用 @Slf4j 注解自动创建日志对象
   - 不在生产代码中使用 System.out 或 e.printStackTrace()

2. **日志级别**
   - ERROR：影响系统正常运行的错误
   - WARN：潜在的问题或即将出现的错误
   - INFO：重要业务流程节点信息
   - DEBUG：调试信息，仅在开发环境使用
   - TRACE：最详细的跟踪信息，一般不使用

3. **日志内容**
   - 包含上下文信息，如用户ID、请求ID等
   - 敏感信息（密码、token等）不记录或脱敏后记录
   - 异常日志需包含完整堆栈信息

## 安全规范

1. **输入验证**
   - 所有外部输入必须验证
   - 使用 Bean Validation (JSR 380) 进行参数验证
   - 防止 SQL 注入、XSS 攻击等安全问题

2. **认证与授权**
   - 敏感操作必须进行权限检查
   - 使用 Spring Security 或自定义拦截器进行权限控制
   - 遵循最小权限原则

3. **数据安全**
   - 敏感数据传输使用 HTTPS
   - 敏感数据存储时加密
   - 用户密码使用不可逆加密算法存储

## 数据库规范

1. **表设计**
   - 表名使用小写下划线命名法 (snake_case)
   - 主键使用 id，类型为 bigint
   - 包含创建时间 (create_time) 和更新时间 (update_time) 字段
   - 使用软删除 (is_deleted) 而非物理删除

2. **SQL 编写**
   - 使用参数化查询，避免拼接 SQL
   - 大写 SQL 关键字，小写表名和字段名
   - 复杂查询添加注释
   - 避免使用 * 查询所有字段

3. **ORM 使用**
   - 使用 MyBatis-Plus 进行 ORM 操作
   - 遵循 MyBatis-Plus 的最佳实践
   - 复杂查询使用 XML 配置，简单查询使用注解

## 测试规范

1. **单元测试**
   - 核心业务逻辑必须有单元测试
   - 使用 JUnit 5 + Mockito 进行测试
   - 测试方法名清晰表达测试意图
   - 一个测试方法只测试一个场景

2. **集成测试**
   - 关键接口必须有集成测试
   - 使用 Spring Boot Test 进行集成测试
   - 测试数据库使用 H2 或 TestContainers

3. **测试覆盖率**
   - 核心业务代码测试覆盖率不低于 70%
   - 使用 JaCoCo 进行测试覆盖率统计

## 版本控制

1. **Git 使用**
   - 遵循 Git Flow 工作流
   - feature 分支命名：feature/功能名
   - bugfix 分支命名：bugfix/问题描述
   - release 分支命名：release/版本号

2. **提交规范**
   - 提交信息使用中文
   - 遵循以下格式：
     ```
     类型: 简要描述
     - 详细点1
     - 详细点2
     ```
   - 类型包括：feat（新功能）、fix（修复）、docs（文档）、style（格式）、refactor（重构）、perf（性能）、test（测试）、chore（构建/工具）

3. **代码审查**
   - 所有代码必须经过至少一人审查才能合并
   - 审查重点：功能正确性、代码质量、安全性、性能

## 剪藏模块特定规范

1. **解析器实现**
   - 新增网站解析器必须继承 AbstractParser
   - 实现 supports 方法判断是否支持该 URL
   - 必须处理异常情况，确保解析失败时有友好提示

2. **图片处理**
   - 图片处理应当异步进行
   - 支持多种图床服务
   - 图片 URL 替换必须保证原始图片可访问时才进行替换

3. **标签解析**
   - 标签解析器必须实现 ContentTagResolver 接口
   - 解析结果必须符合 Notion Block 格式
   - 处理特殊标签时必须考虑嵌套情况

## 性能优化

1. **并发处理**
   - I/O 密集型任务使用虚拟线程
   - CPU 密集型任务使用传统线程池
   - 避免线程饥饿和死锁

2. **缓存使用**
   - 合理使用缓存减少重复计算和网络请求
   - 缓存数据设置合理的过期时间
   - 缓存更新策略：定时更新、写时更新或读时更新

3. **资源释放**
   - 使用 try-with-resources 自动关闭资源
   - 显式关闭不再使用的连接和流
   - 避免内存泄漏