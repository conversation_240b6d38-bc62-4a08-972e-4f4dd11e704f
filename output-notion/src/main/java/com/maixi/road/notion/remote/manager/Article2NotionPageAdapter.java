package com.maixi.road.notion.remote.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.maixi.road.common.core.model.dto.ResolveFormRQ;
import com.maixi.road.common.integration.notion.constants.NotionConstants;
import com.maixi.road.common.integration.notion.enums.BlockTypeEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.Callout;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.block.Table;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.integration.notion.model.common.Text;
import com.maixi.road.common.integration.notion.model.page.Page;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class Article2NotionPageAdapter {

    @Resource
    private RedissonClient redissonClient;

    public List<Block> handleBlocks(ResolveFormRQ resolveVo, Page page, int blockSizeLimit) {

        // 如果
        if (StringUtils.isNoneBlank(resolveVo.getCover()) && page.getCover() != null) {
            page.getCover().getExternal().setUrl(resolveVo.getCover());
        }

        // 少于 100 个 block，直接一次写入
        List<Block> blocks = resolveVo.getBlocks();
        if (blocks.size() < 100) {
            page.setChildren(blocks);
            return null;
        }

        // 超过 max block size limit，写入提示
        if (blocks.size() > blockSizeLimit) {
            page.setChildren(blockSizeTooLarge(resolveVo.getLink()));
            return null;
        }

        // 分成两拨，首次的 100 个 block，先通过 createPage 写入，剩余的通过继续拆分 append block 写入
        List<Block> first = blocks.subList(0, 100);
        List<Block> second = blocks.subList(100, blocks.size());
        page.setChildren(first);
        return second;
    }

    private List<Block> blockSizeTooLarge(String url) {
        return Collections.singletonList(Block.paragraph(RichTexts.blockSizeTooLarge(url)));
    }

    public RequestBody toBody(String bodyJson) {
        return RequestBody.create(bodyJson, MediaType.parse("application/json"));
    }

    public Request.Builder commonRequestBuilder(String notionApiKey) {
        return new Request.Builder()
                .addHeader("Authorization", "Bearer " + notionApiKey)
                .addHeader("Notion-Version", NotionConstants.NOTION_VERSION)
                .addHeader("Content-Type", "application/json");
    }

    public String toJson(Object object) {
        return JSON.toJSONString(object, SerializerFeature.DisableCircularReferenceDetect);
    }

    public String toJsonWithNull(Object object) {
        return JSON.toJSONString(object, SerializerFeature.WriteMapNullValue);
    }

    public String join(String... strings) {
        return Joiner.on("").skipNulls().join(strings);
    }

    /**
     * 将输入字符串按照指定长度拆分为多个子字符串。
     *
     * @param input 要拆分的字符串。
     * @param length 每个子字符串的长度。
     * @return 一个包含所有子字符串的列表。
     */
    private static List<String> splitStringByLength(String input, int length) {
        List<String> result = new ArrayList<>();

        // 使用substring方法拆分字符串
        for (int start = 0; start < input.length(); start += length) {
            // 确保不会因为超出字符串长度而抛出异常
            int end = Math.min(start + length, input.length());
            result.add(input.substring(start, end));
        }

        return result;
    }

    public void processOverLengthCase(List<Block> originBlocks) {
        ListIterator<Block> iterator = originBlocks.listIterator();
        // 处理单个 richText 超长问题
        while (iterator.hasNext()) {
            Block block = iterator.next();

            // paragraph 单个 richText 不能超过 2000 个字符
            if (BlockTypeEnum.PARAGRAPH.getType().equals(block.getType())) {
                List<RichText> originRichTexts = block.getParagraph().getRich_text();
                Map<Integer, List<RichText>> indexAppendRichTextsMap = Maps.newHashMap();
                for (int i = 0; i < originRichTexts.size(); i++) {
                    RichText richText = originRichTexts.get(i);
                    if (richText.getText().getContent().length() > 2000) {
                        List<String> strings = splitStringByLength(richText.getText().getContent(), 2000);
                        List<RichText> remainRichText = strings.stream().map(RichText::simpleText)
                                .collect(Collectors.toList());
                        indexAppendRichTextsMap.put(i, remainRichText);
                    }
                }
                int addCount = 0;
                int removeCount = 0;
                // (2,2),(3,3),(4,1)
                // [1, 2, 3, 4, 5, 6, 7]  2,2
                // [1, 2, 3.1, 3.2, 4, 5, 6, 7]  4,3
                // [1, 2, 3.1, 3.2, 4.1, 4.2, 4.3, 5.1, 6, 7]  7
                List<Integer> keys = indexAppendRichTextsMap.keySet().stream().sorted()
                        .collect(Collectors.toList());
                for (Integer key : keys) {
                    originRichTexts.remove(key.intValue());
                    originRichTexts.addAll(key + addCount - removeCount, indexAppendRichTextsMap.get(key));
                    removeCount++;
                    addCount += indexAppendRichTextsMap.get(key).size();
                }
            }

            if (BlockTypeEnum.CODE.getType().equals(block.getType())) {
                List<RichText> richTexts = block.getCode().getRich_text();
                if (richTexts.size() > 100) {
                    block.setObject("block");
                    block.setType(BlockTypeEnum.CALLOUT.getType());
                    block.setCode(null);
                    block.setCallout(Callout.buildTip("代码行数超出 block limit:100. 请手动补充该部分内容！"));
                }
                boolean codeOverLength = richTexts.stream()
                        .filter(r -> r.getText().getContent().length() > 1000).findAny().isPresent();
                if (codeOverLength) {
                    block.setObject("block");
                    block.setType(BlockTypeEnum.CALLOUT.getType());
                    block.setCode(null);
                    block.setCallout(Callout.buildTip("代码内容长度超出Notion限制. 请手动补充该部分内容！"));
                }
            }

            // quote 单个 richText 不能超过 100 个字符
            if (BlockTypeEnum.QUOTE.getType().equals(block.getType())) {
                List<RichText> richTexts = block.getQuote().getRich_text();
                int totalCharCount = richTexts.stream().map(RichText::getText).map(Text::getContent)
                        .mapToInt(String::length) // 将每个字符串映射到其长度
                        .sum(); // 将所有长度相加得到总和
                if (totalCharCount > 100) {
                    if (richTexts.size() < 5) {
                        block.setObject("block");
                        block.setType(BlockTypeEnum.CALLOUT.getType());
                        block.setQuote(null);
                        block.setCallout(Callout.build(richTexts));
                    } else {
                        block.setObject("block");
                        block.setType(BlockTypeEnum.PARAGRAPH.getType());
                        block.setQuote(null);
                        block.setParagraph(ParagraphBlockBuilder.singleParagraph(richTexts).getParagraph());
                    }
                }
            }
            if (BlockTypeEnum.TABLE.getType().equals(block.getType())) {
                Table table = block.getTable();
                if (table.getChildren().size() > 100) {
                    List<JSONObject> subChildren = table.getChildren().subList(0, 100);
                    List<List<JSONObject>> partition = Lists.partition(table.getChildren().subList(100, table.getChildren().size()), 100);
                    // 重置当前 table 长度
                    table.setChildren(subChildren);
                    for (List<JSONObject> jsonObjects : partition) {
                        // 补充拆分的 table
                        Block appendBlock = Block.table(Table.builder().table_width(table.getTable_width()).has_column_header(table.getHas_column_header()).has_row_header(table.getHas_row_header()).children(jsonObjects).build());
                        iterator.add(appendBlock);
                    }
                }
            }
        }

        // 处理 paragraph 的 rich_text 个数超出 100 个的问题
        Map<Integer, List<Block>> indexAppendBlocksMap = Maps.newHashMap();
        for (int i = 0; i < originBlocks.size(); i++) {
            Block block = originBlocks.get(i);
            if (BlockTypeEnum.PARAGRAPH.getType().equals(block.getType())) {
                List<RichText> richTexts = block.getParagraph().getRich_text();
                int richTextCount = richTexts.size();
                if (richTextCount > 100) {
                    List<List<RichText>> partition = Lists.partition(richTexts, 100);
                    List<Block> remainBlocks = partition.stream()
                            .map(s -> Block.paragraph(RichTexts.build(s))).collect(Collectors.toList());
                    indexAppendBlocksMap.put(i, remainBlocks);
                }
            }
        }
        int addCount = 0;
        int removeCount = 0;
        // (2,2),(3,3),(4,1)
        // [1, 2, 3, 4, 5, 6, 7]  2,2
        // [1, 2, 3.1, 3.2, 4, 5, 6, 7]  4,3
        // [1, 2, 3.1, 3.2, 4.1, 4.2, 4.3, 5.1, 6, 7]  7
        List<Integer> keys = indexAppendBlocksMap.keySet().stream().sorted()
                .collect(Collectors.toList());
        for (Integer key : keys) {
            originBlocks.remove(key.intValue());
            originBlocks.addAll(key + addCount - removeCount, indexAppendBlocksMap.get(key));
            removeCount++;
            addCount += indexAppendBlocksMap.get(key).size();
        }
    }


}
