package com.maixi.road.notion.remote.impl.local;

import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.maixi.road.cloudfunc.notion.NotionCloudFunctionApi;
import com.maixi.road.notion.remote.api.BlockApi;
import com.maixi.road.notion.remote.config.NotionClientConfig;
import com.maixi.road.notion.remote.manager.Article2NotionPageAdapter;
import com.maixi.road.cloudfunc.notion.dto.rq.BlockAppendRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.NotionBaseRS;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.IOException;

@Slf4j
@Component("blockLocalClient")
public class BlockLocalClient implements BlockApi {

    @Resource
    private Article2NotionPageAdapter article2NotionPageAdapter;
    @Resource
    private NotionCloudFunctionApi notionClientApi;
    @Resource
    private BlockApi blockApi;

    public NotionBaseRS appendBlockChildren(String notionApiKey,
                                            String blockId,
                                            BlockAppendRQ appendRequest,
                                            String unionId) throws IOException {

        String requestUrl = StrUtil.format("https://api.notion.com/v1/blocks/{}/children", blockId);
        article2NotionPageAdapter.processOverLengthCase(appendRequest.getChildren());
        Request request = article2NotionPageAdapter.commonRequestBuilder(notionApiKey)
                .url(requestUrl)
                .method("PATCH", article2NotionPageAdapter.toBody(article2NotionPageAdapter.toJson(appendRequest)))
                .build();
        NotionBaseRS result;
        try (Response response = NotionClientConfig.CLIENT.newCall(request).execute()) {
            Assert.notNull(response.body(), "创建页面失败, response 为空");
            String bodyString = response.body().string();
            result = JSON.parseObject(UnicodeUtil.toString(bodyString), NotionBaseRS.class);
        }
        if (result == null || result.getObject() == null || "error".equals(result.getObject()) || !"list".equals(result.getObject())) {
            log.error("追加页面内容失败, unionId={},  result={}", unionId, JSON.toJSONString(result));
        }
        return result;
    }
}
