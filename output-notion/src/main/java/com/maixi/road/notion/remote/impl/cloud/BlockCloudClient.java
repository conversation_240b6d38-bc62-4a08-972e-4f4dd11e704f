package com.maixi.road.notion.remote.impl.cloud;

import com.maixi.road.cloudfunc.notion.NotionCloudFunctionApi;
import com.maixi.road.cloudfunc.notion.dto.rq.BlockAppendRQ;
import com.maixi.road.cloudfunc.notion.dto.rq.ToNotionRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.NotionBaseRS;
import com.maixi.road.notion.remote.api.BlockApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.IOException;

@Slf4j
@Component("blockCloudClient")
public class BlockCloudClient implements BlockApi {

    @Resource
    private NotionCloudFunctionApi notionClientApi;

    public NotionBaseRS appendBlockChildren(String notionApiKey,
                                            String blockId,
                                            BlockAppendRQ appendRequest,
                                            String unionId) throws IOException {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("blockCloudClient.appendBlockChildren");
        try {
            return notionClientApi.sendToNotion(new ToNotionRQ(notionApiKey, blockId, unionId, appendRequest));
        } finally {
            stopWatch.stop();
            log.info("[{}], rt={} seconds", stopWatch.lastTaskInfo().getTaskName(), stopWatch.getTotalTimeSeconds());
        }

    }
}
