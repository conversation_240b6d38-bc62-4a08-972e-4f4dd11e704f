package com.maixi.road.notion.remote.impl.cloud;

import com.maixi.road.cloudfunc.notion.NotionCloudFunctionApi;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.notion.remote.api.AuthenticationApi;
import com.maixi.road.notion.remote.dto.response.AuthenticationRS;
import com.maixi.road.cloudfunc.notion.dto.rq.ToNotionRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.IOException;

@Slf4j
@Component("authenticationCloudClient")
public class AuthenticationCloudClient implements AuthenticationApi {

    @Resource
    private NotionCloudFunctionApi notionClientApi;

    @Override
    public AuthenticationRS createToken(String code) throws IOException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("authenticationCloudClient.createToken");
        try {
            PageCreateRS createResult = notionClientApi.sendToNotion(new ToNotionRQ(code, true));
            if (createResult != null && StringUtils.isNotBlank(createResult.getAccessToken())) {
                return new AuthenticationRS(createResult.getAccessToken());
            }
            throw RoadException.create(ErrorCodeEnum.INTERNAL_ERROR, "createToken error");
        } finally {
            stopWatch.stop();
            log.info("[{}], rt={}", stopWatch.lastTaskInfo().getTaskName(), stopWatch.getTotalTimeSeconds());
        }
    }
}
