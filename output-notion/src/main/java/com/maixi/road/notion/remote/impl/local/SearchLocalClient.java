package com.maixi.road.notion.remote.impl.local;

import java.io.IOException;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.notion.remote.api.SearchApi;
import com.maixi.road.notion.remote.config.NotionClientConfig;
import com.maixi.road.notion.remote.dto.request.QueryRQ;
import com.maixi.road.notion.remote.dto.response.DatabaseRS;
import com.maixi.road.notion.remote.dto.response.ListRS;
import com.maixi.road.notion.remote.manager.Article2NotionPageAdapter;

import cn.hutool.core.text.UnicodeUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;

@Slf4j
@Component("searchLocalClient")
public class SearchLocalClient implements SearchApi {

    private static final String SEARCH_URL = "https://api.notion.com/v1/search";

    @Resource
    private Article2NotionPageAdapter article2NotionPageAdapter;

    @Override
    public Pair<String, String> searchDb(String unionId, String accessToken, Integer relationId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("SearchLocalClient.searchDb");
        try {
            // 查询用于存储公众号文章的数据库 ID
            ListRS<DatabaseRS> favorDbRes;
            try {
                favorDbRes = this.searchDbSortByLastEditedTimeDesc(accessToken, "文章数据库");
            } catch (IOException e) {
                log.error("查询文章数据库异常, unionId={}, relationId={}, errorMsg={}", unionId, relationId, e.getMessage());
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "文章数据库创建验证失败");
            }
            List<DatabaseRS> favorDbResults = favorDbRes.getResults();
            if (CollectionUtils.isEmpty(favorDbResults) || favorDbResults.getFirst() == null || StringUtils.isBlank(favorDbResults.get(0).getId())) {
                log.error("查询文章数据库失败：查询结果为空, unionId={}, relationId={}", unionId, relationId);
                return Pair.of(null, null);
            }
            String articleDbId = null;
            for (DatabaseRS resp : favorDbResults) {
                if ("文章数据库".equals(resp.getTitle().getFirst().getPlain_text())) {
                    articleDbId = resp.getId();
                    break;
                }
            }

            // 查询用于存储公众号文章的数据库 ID
            ListRS<DatabaseRS> msgDbRes;
            try {
                msgDbRes = this.searchDbSortByLastEditedTimeDesc(accessToken, "消息数据库");
            } catch (IOException e) {
                log.error("查询消息数据库异常, unionId={}, relationId={}, errorMsg={}", unionId, relationId, e.getMessage());
                throw RoadException.create(ErrorCodeEnum.FALLBACK, "消息数据库创建验证失败");
            }
            List<DatabaseRS> msgDbResults = msgDbRes.getResults();
            if (CollectionUtils.isEmpty(msgDbResults) || msgDbResults.getFirst() == null || StringUtils.isBlank(msgDbResults.get(0).getId())) {
                log.error("查询用户消息数据库失败：查询结果为空, unionId={}, relationId={}", unionId, relationId);
                return Pair.of(null, null);
            }
            String msgDbId = null;
            for (DatabaseRS resp : msgDbResults) {
                if ("消息数据库".equals(resp.getTitle().getFirst().getPlain_text())) {
                    msgDbId = resp.getId();
                    break;
                }
            }
            return Pair.of(articleDbId, msgDbId);
        } finally {
            stopWatch.stop();
            log.info("[{}], rt={} seconds", stopWatch.lastTaskInfo().getTaskName(), stopWatch.getTotalTimeSeconds());
        }
    }


    public ListRS<DatabaseRS> searchDbSortByLastEditedTimeDesc(String notionApiKey, String title)
            throws IOException {

        QueryRQ queryRequest = QueryRQ.builder()
                .query(title)
                .filter(QueryRQ.Filter.onlyDatabase())
                .sort(QueryRQ.Sort.byLastEditedTime("descending"))
                .build();
        return searchByTitle(notionApiKey, queryRequest);
    }


    public ListRS<DatabaseRS> searchByTitle(String notionApiKey, QueryRQ queryRequest) throws IOException {

        Request request = article2NotionPageAdapter.commonRequestBuilder(notionApiKey)
                .url(SEARCH_URL)
                .method("POST", article2NotionPageAdapter.toBody(article2NotionPageAdapter.toJson(queryRequest)))
                .build();
        ListRS<DatabaseRS> result;
        try (Response response = NotionClientConfig.CLIENT.newCall(request).execute()) {
            Assert.notNull(response.body(), "response body is null");
            String bodyString = response.body().string();
            result = JSON.parseObject(UnicodeUtil.toString(bodyString), new TypeReference<ListRS<DatabaseRS>>() {
            });
        }
        return result;
    }
}
