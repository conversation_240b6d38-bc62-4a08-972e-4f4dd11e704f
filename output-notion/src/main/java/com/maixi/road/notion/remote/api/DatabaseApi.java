package com.maixi.road.notion.remote.api;

import java.io.IOException;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.cloudfunc.notion.dto.rs.NotionBaseRS;

public interface DatabaseApi {

    /**
     * 更新数据库
     * @param accessToken
     * @param databaseId
     * @param bodyParam
     * @return
     * @throws IOException
     */
    NotionBaseRS updateDatabase(String accessToken, String databaseId, JSONObject bodyParam) throws IOException;
}