package com.maixi.road.notion.remote;

import java.io.IOException;
import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.cloudfunc.notion.dto.rq.BlockAppendRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.NotionBaseRS;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import com.maixi.road.common.integration.notion.model.page.Page;
import com.maixi.road.common.service.config.ConfigQueryApi;
import com.maixi.road.notion.remote.api.AuthenticationApi;
import com.maixi.road.notion.remote.api.BlockApi;
import com.maixi.road.notion.remote.api.DatabaseApi;
import com.maixi.road.notion.remote.api.PageApi;
import com.maixi.road.notion.remote.api.SearchApi;
import com.maixi.road.notion.remote.dto.response.AuthenticationRS;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Notion远程客户端
 * <p>
 * 该类负责与Notion API进行交互，提供了创建Token、追加块内容、更新数据库、搜索数据库、创建页面等功能
 * 支持本地客户端和云函数客户端两种实现方式，根据配置动态选择
 * </p>
 */
@Slf4j
@Component
public class NotionClient {

    /**
     * 认证API映射，包含本地客户端和云函数客户端
     */
    @Resource
    private Map<String, AuthenticationApi> authenticationApiMap;

    /**
     * 块API映射，包含本地客户端和云函数客户端
     */
    @Resource
    private Map<String, BlockApi> blockApiMap;

    /**
     * 页面API映射，包含本地客户端和云函数客户端
     */
    @Resource
    private Map<String, PageApi> pageApiMap;

    /**
     * 搜索API映射，包含本地客户端和云函数客户端
     */
    @Resource
    private Map<String, SearchApi> searchApiMap;

    /**
     * 数据库API映射，包含本地客户端和云函数客户端
     */
    @Resource
    private Map<String, DatabaseApi> databaseApiMap;

    /**
     * 配置查询API，用于获取全局配置和用户文章字段配置
     */
    @Resource
    private ConfigQueryApi configQueryApi;

    

    /**
     * 创建Notion访问令牌
     * <p>
     * 根据授权码创建Notion访问令牌，支持本地客户端和云函数客户端两种实现方式
     * </p>
     *
     * @param code Notion授权码
     * @return AuthenticationRS 认证响应对象，包含访问令牌等信息
     * @throws IOException 当API调用失败时抛出
     */
    public AuthenticationRS createToken(String code) throws IOException {
        // 从配置中心获取是否使用云函数实现
        boolean tokenUseCloudFunction = configQueryApi.gloablConfig().isCreateTokenUseCloudFunction();
        if (tokenUseCloudFunction) {
            // 使用云函数客户端实现
            return authenticationApiMap.get("authenticationCloudClient").createToken(code);
        }
        // 使用本地客户端实现
        return authenticationApiMap.get("authenticationLocalClient").createToken(code);
    }

    /**
     * 向指定块添加子块
     * <p>
     * 向Notion中的指定块添加子块内容，支持本地客户端和云函数客户端两种实现方式
     * </p>
     *
     * @param notionApiKey  Notion API密钥
     * @param blockId       目标块ID
     * @param appendRequest 追加请求对象，包含要追加的子块内容
     * @param unionId       用户唯一标识
     * @return NotionBaseRS Notion基础响应对象
     * @throws IOException 当API调用失败时抛出
     */
    public NotionBaseRS appendBlockChildren(String notionApiKey,
            String blockId,
            BlockAppendRQ appendRequest,
            String unionId) throws IOException {
        // 从配置中心获取是否使用云函数实现
        boolean useCloudFunction = configQueryApi.gloablConfig().isAppend2NotionUseCloudFunction();
        if (useCloudFunction) {
            // 使用云函数客户端实现
            return blockApiMap.get("blockCloudClient")
                    .appendBlockChildren(notionApiKey, blockId, appendRequest, unionId);
        }
        // 使用本地客户端实现
        return blockApiMap.get("blockLocalClient")
                .appendBlockChildren(notionApiKey, blockId, appendRequest, unionId);
    }

    /**
     * 更新Notion数据库
     * <p>
     * 更新Notion中的指定数据库，支持本地客户端和云函数客户端两种实现方式
     * </p>
     *
     * @param accessToken Notion访问令牌
     * @param databaseId  数据库ID
     * @param bodyParam   更新参数，JSON格式
     * @return NotionBaseRS Notion基础响应对象
     * @throws IOException 当API调用失败时抛出
     */
    public NotionBaseRS updateDatabase(String accessToken, String databaseId, JSONObject bodyParam) throws IOException {
        // 从配置中心获取是否使用云函数实现
        boolean useCloudFunction = configQueryApi.gloablConfig().isUpdate2NotionUseCloudFunction();
        if (useCloudFunction) {
            // 使用云函数客户端实现
            return databaseApiMap.get("databaseCloudClient").updateDatabase(accessToken, databaseId, bodyParam);
        }
        // 使用本地客户端实现
        return databaseApiMap.get("databaseLocalClient").updateDatabase(accessToken, databaseId, bodyParam);
    }

    /**
     * 搜索Notion数据库
     * <p>
     * 根据用户ID和关联ID搜索Notion数据库，支持本地客户端和云函数客户端两种实现方式
     * </p>
     *
     * @param unionId     用户唯一标识
     * @param accessToken Notion访问令牌
     * @param relationId  关联ID
     * @return Pair<String, String> 键值对，可能包含数据库ID和名称
     */
    public Pair<String, String> searchDb(String unionId, String accessToken, Integer relationId) {
        // 从配置中心获取是否使用云函数实现
        boolean useCloudFunction = configQueryApi.gloablConfig().isSearchNotionUseCloudFunction();
        if (useCloudFunction) {
            // 使用云函数客户端实现
            return searchApiMap.get("searchCloudClient").searchDb(unionId, accessToken, relationId);
        }
        // 使用本地客户端实现
        return searchApiMap.get("searchLocalClient").searchDb(unionId, accessToken, relationId);
    }

    /**
     * 创建Notion页面
     * <p>
     * 在Notion中创建页面，支持本地客户端和云函数客户端两种实现方式
     * </p>
     *
     * @param notionApiKey Notion API密钥
     * @param page         要创建的页面对象
     * @param unionId      用户唯一标识
     * @return PageCreateRS 页面创建响应对象
     * @throws IOException 当API调用失败时抛出
     */
    public PageCreateRS createPage(String notionApiKey, Page page, String unionId) throws IOException {
        // 从配置中心获取是否使用云函数实现
        boolean useCloudFunction = configQueryApi.gloablConfig().isSend2NotionUseCloudFunction();
        if (useCloudFunction) {
            // 使用云函数客户端实现
            return pageApiMap.get("pageCloudClient").createPage(notionApiKey, page, unionId);
        }
        // 使用本地客户端实现
        return pageApiMap.get("pageLocalClient").createPage(notionApiKey, page, unionId);
    }

    /**
     * 带重试机制的页面创建
     * <p>
     * 在创建页面时添加重试机制，最多重试10次，每次失败后等待300ms
     * 增强了系统的容错性，避免因临时网络问题导致失败
     * </p>
     *
     * @param notionApiKey Notion API密钥
     * @param page         要创建的页面对象
     * @param unionId      用户唯一标识
     * @return PageCreateRS 页面创建响应对象，如果所有重试都失败则返回null
     * @throws InterruptedException 当线程睡眠被中断时抛出
     */
    public PageCreateRS createPageWithRetry(String notionApiKey, Page page, String unionId)
            throws InterruptedException {
        // 最多重试10次
        for (int i = 0; i < 10; i++) {
            try {
                // 尝试创建页面
                return createPage(notionApiKey, page, unionId);
            } catch (Exception e) {
                // 记录失败日志及当前重试次数
                log.error("创建页面失败, unionId={}, 当前重试次数:{}", unionId, i, e);
            }
            // 等待300ms再进行下一次重试，避免对 Notion API 造成压力
            Thread.sleep(300);
        }
        // 所有重试都失败后返回null
        return null;
    }

}
