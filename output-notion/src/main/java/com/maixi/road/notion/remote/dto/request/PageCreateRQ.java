package com.maixi.road.notion.remote.dto.request;

import com.maixi.road.common.core.model.dto.ResolveFormRQ;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PageCreateRQ {

    private String unionId;

    private ResolveFormRQ userForm;

    private boolean noIcon;

    private boolean noCover;

    private int blockSizeLimit;
}
