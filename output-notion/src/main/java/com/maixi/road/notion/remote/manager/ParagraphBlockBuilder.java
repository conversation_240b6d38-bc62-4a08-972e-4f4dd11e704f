package com.maixi.road.notion.remote.manager;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import com.maixi.road.common.integration.notion.enums.ColorEnum;
import com.maixi.road.common.integration.notion.enums.RichTextEnum;
import com.maixi.road.common.integration.notion.model.block.Block;
import com.maixi.road.common.integration.notion.model.block.RichTexts;
import com.maixi.road.common.integration.notion.model.common.Annotations;
import com.maixi.road.common.integration.notion.model.common.Link;
import com.maixi.road.common.integration.notion.model.common.RichText;
import com.maixi.road.common.integration.notion.model.common.Text;

import java.util.Collections;
import java.util.List;

@Slf4j
public class ParagraphBlockBuilder {

    public static Block singleParagraphWithSingleDefaultRichText(String content){
        return singleParagraph(Collections.singletonList(defaultRichText(content)));
    }

    public static Block singleParagraph(List<RichText> richTexts){
        RichTexts paragraph = new RichTexts();
        paragraph.setColor(ColorEnum._default.getColor());
        paragraph.setRich_text(richTexts);
        return Block.paragraph(paragraph);
    }

    public static RichText linkRichText(String content,String href){
        if (StringUtils.isBlank(href)) {
            return defaultRichText(content);
        }
        RichText richText = new RichText();
        richText.setType(RichTextEnum.text.getType());
        Text text = new Text();
        text.setContent(content);
        text.setLink(new Link(href));
        richText.setText(text);
        richText.setAnnotations(Annotations.defaultAnnotations());
        richText.setPlain_text(content);
        richText.setHref(href);
        return richText;
    }

    public static RichText defaultRichText(String content){
        RichText richText = new RichText();
        richText.setType(RichTextEnum.text.getType());
        Text text = new Text();
        text.setContent(content);
        text.setLink(null);
        richText.setText(text);
        richText.setAnnotations(Annotations.defaultAnnotations());
        richText.setPlain_text(content);
        richText.setHref(null);
        return richText;
    }
}
