package com.maixi.road.notion.remote.config;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

@Slf4j(topic = "NotionClientConfig")
public class NotionClientConfig {

    public static final OkHttpClient CLIENT;
    public static final OkHttpClient PICTURE_CLIENT;
    private static final String BASE_URL = "https://api.notion.com/v1";

    static {
        // 配置 OkHttpClient
        CLIENT = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS) // 连接超时时间
                .readTimeout(30, TimeUnit.SECONDS)    // 读取超时时间
                .writeTimeout(10, TimeUnit.SECONDS)   // 写入超时时间
                .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES)) // 连接池配置
                .build();

        // 预热连接
        warmUpConnection();
    }

    static {
        // 配置 OkHttpClient
        PICTURE_CLIENT = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS) // 连接超时时间
                .readTimeout(30, TimeUnit.SECONDS)    // 读取超时时间
                .writeTimeout(10, TimeUnit.SECONDS)   // 写入超时时间
                .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES)) // 连接池配置
                .build();
    }

    public static void warmUpConnection() {
        Request request = new Request.Builder()
                .url(BASE_URL)
                .head() // 发送一个简单的 HEAD 请求以预热连接
                .build();
        try {
            CLIENT.newCall(request).execute();
        } catch (IOException e) {
            log.error("OkHttp 预热连接  Warm-up error: {}", e.getMessage());
        }
    }
}
