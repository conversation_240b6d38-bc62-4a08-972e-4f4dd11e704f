package com.maixi.road.notion.remote.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthenticationRQ {

    @JSO<PERSON>ield(name = "grant_type")
    private String grantType;
    private String code;
    @JSONField(name = "redirect_uri")
    private String redirectUri;
}
