package com.maixi.road.notion.remote.impl.local;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.maixi.road.notion.remote.api.DatabaseApi;
import com.maixi.road.notion.remote.config.NotionClientConfig;
import com.maixi.road.notion.remote.dto.response.DatabaseRS;
import com.maixi.road.cloudfunc.notion.dto.rs.NotionBaseRS;
import com.maixi.road.common.integration.notion.constants.NotionConstants;

import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;

import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Slf4j
@Service("databaseLocalClient")
public class DatabaseLocalClient implements DatabaseApi {

    private static final String DATABASE_URL = "https://api.notion.com/v1/databases";

    @Override
    public NotionBaseRS updateDatabase(String accessToken, String databaseId, JSONObject bodyParam) throws IOException {
        String requestUrl = StrUtil.format(DATABASE_URL + "/{}", databaseId);
        Request request = commonRequestBuilder(accessToken)
                .url(requestUrl)
                .method("PATCH", toBody(toJsonWithNull(bodyParam)))
                .build();
        NotionBaseRS result;
        try (Response response = NotionClientConfig.CLIENT.newCall(request).execute()) {
            Assert.notNull(response.body(), "response body is null");
            String bodyString = response.body().string();
            result = JSON.parseObject(UnicodeUtil.toString(bodyString), NotionBaseRS.class);
        }
        log.info("DATABASE操作  updateDatabase, result = {}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 获取数据库详细信息
     * 调用 Notion API GET /databases/{database_id} 获取数据库结构和属性
     * 
     * @param accessToken Notion访问令牌
     * @param databaseId  数据库ID
     * @return DatabaseRS 数据库详细信息，包含所有字段的属性和选项
     * @throws IOException 当API调用失败时抛出
     */
    public DatabaseRS retrieveDatabase(String accessToken, String databaseId) throws IOException {
        String requestUrl = StrUtil.format(DATABASE_URL + "/{}", databaseId);
        Request request = commonRequestBuilder(accessToken)
                .url(requestUrl)
                .method("GET", null)
                .build();
        DatabaseRS result;
        try (Response response = NotionClientConfig.CLIENT.newCall(request).execute()) {
            Assert.notNull(response.body(), "response body is null");
            String bodyString = response.body().string();
            result = JSON.parseObject(UnicodeUtil.toString(bodyString), DatabaseRS.class);
            log.info("DATABASE操作  retrieveDatabase, databaseId={}, success", databaseId);
        }
        return result;
    }

    protected Request.Builder commonRequestBuilder(String notionApiKey) {
        return new Request.Builder()
                .addHeader("Authorization", "Bearer " + notionApiKey)
                .addHeader("Notion-Version", NotionConstants.NOTION_VERSION)
                .addHeader("Content-Type", "application/json");
    }

    protected String toJsonWithNull(Object object) {
        return JSON.toJSONString(object, SerializerFeature.WriteMapNullValue);
    }

    protected String toJson(Object object) {
        return JSON.toJSONString(object, SerializerFeature.DisableCircularReferenceDetect);
    }

    protected RequestBody toBody(String bodyJson) {
        return RequestBody.create(bodyJson, MediaType.parse("application/json"));
    }

}
