package com.maixi.road.notion.remote.api;

import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import com.maixi.road.common.integration.notion.model.page.Page;

import java.io.IOException;

public interface PageApi {
    
    /**
     * 创建页面
     *
     * @param notionApiKey API密钥
     * @param page 页面内容
     * @param unionId 用户标识
     * @return 创建结果
     * @throws IOException IO异常
     */
    PageCreateRS createPage(String notionApiKey, Page page, String unionId) throws IOException;
}