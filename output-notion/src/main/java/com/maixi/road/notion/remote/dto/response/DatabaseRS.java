package com.maixi.road.notion.remote.dto.response;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.common.integration.notion.model.page.Parent;
import com.maixi.road.common.integration.notion.model.page.TitleObject;
import com.maixi.road.common.integration.notion.model.page.User;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/27
 **/
@Data
public class DatabaseRS {

    private String id;
    private String cover;
    private String icon;
    private String object;
    private String url;
    private String public_url;
    private Boolean archived;
    private Parent parent;
    private Boolean is_inline;
    private List<Object> description;
    private LocalDateTime last_edited_time;
    private LocalDateTime created_time;
    private User created_by;
    private User last_edited_by;
    private JSONObject properties;
    private List<TitleObject> title;

}
