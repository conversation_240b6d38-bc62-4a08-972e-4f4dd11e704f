package com.maixi.road.notion.remote.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryRQ {
    private String query;
    private Filter filter;
    private Sort sort;
    /**
     * A cursor value returned in a previous response that If supplied, limits the response to results starting after the cursor.
     * If not supplied, then the first page of results is returned.
     */
    private Integer start_cursor;
    /**
     * The number of items from the full list to include in the response. Maximum: 100.
     */
    private Integer page_size;


    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class Filter {

        /**
         * Possible value values are "page" or "database".
         */
        private String value;
        /**
         * The only supported property value is "object".
         */
        private String property;

        public static Filter onlyPage() {
            return new Filter("page", "object");
        }

        public static Filter onlyDatabase() {
            return new Filter("database", "object");
        }
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class Sort {

        /**
         * The direction to sort.
         * Possible values include <code>ascending</code> and <code>descending</code>.
         * Supported direction values are "ascending" and "descending".
         * If sort is not provided, then the most recently edited results are returned first.
         */
        private String direction;
        /**
         * The name of the timestamp to sort against.
         * The only supported timestamp value is "last_edited_time"
         */
        private String timestamp;

        public static Sort byLastEditedTime(String direction) {
            return new Sort(direction, "last_edited_time");
        }
    }


}
