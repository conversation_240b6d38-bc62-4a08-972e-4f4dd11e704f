package com.maixi.road.notion.remote.impl.cloud;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.cloudfunc.notion.NotionCloudFunctionApi;
import com.maixi.road.cloudfunc.notion.dto.rq.ToNotionRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.NotionBaseRS;
import com.maixi.road.notion.remote.api.DatabaseApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.IOException;

@Slf4j
@Component("databaseCloudClient")
public class DatabaseCloudClient implements DatabaseApi {

    @Resource
    private NotionCloudFunctionApi notionClientApi;


    @Override
    public NotionBaseRS updateDatabase(String accessToken, String databaseId, JSONObject bodyParam) throws IOException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("databaseCloudClient.updateDatabase");
        try {
            return notionClientApi.sendToNotion(new ToNotionRQ(accessToken, databaseId, bodyParam));
        } finally {
            stopWatch.stop();
            log.info("[{}]. rt={}", stopWatch.lastTaskInfo().getTaskName(), stopWatch.getTotalTimeSeconds());
        }
    }
}
