package com.maixi.road.notion.remote.impl.local;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.maixi.road.common.core.enums.error.ErrorCodeEnum;
import com.maixi.road.common.core.exception.RoadException;
import com.maixi.road.common.core.utils.AssertUtils;
import com.maixi.road.common.core.utils.JsonUtil;
import com.maixi.road.common.integration.notion.constants.NotionIntegrations;
import com.maixi.road.notion.remote.api.AuthenticationApi;
import com.maixi.road.notion.remote.config.NotionClientConfig;
import com.maixi.road.notion.remote.dto.request.AuthenticationRQ;
import com.maixi.road.notion.remote.dto.response.AuthenticationRS;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.text.UnicodeUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Slf4j
@Component("authenticationLocalClient")
public class AuthenticationLocalClient implements AuthenticationApi {

    private static final String HEADER_AUTHORIZATION = "Basic " + Base64.encodeBase64String(NotionIntegrations.CLIENT_ID_AND_SECRET.getBytes(StandardCharsets.UTF_8));
    private static final String AUTH_BASE_URL = "https://api.notion.com/v1/oauth/token";
    private static final String CONTENT_TYPE = "application/json";

    private static final Map<String, String> HEADERS_MAP;
    static {
        HEADERS_MAP = new HashMap<>();
        HEADERS_MAP.put("Authorization", HEADER_AUTHORIZATION);
        HEADERS_MAP.put("Content-Type", CONTENT_TYPE);
        HEADERS_MAP.put("Notion-Version", NotionIntegrations.NOTION_VERSION);
    }

    @Override
    public AuthenticationRS createToken(String code) throws IOException {
        // param
        AuthenticationRQ authenticationRequest = new AuthenticationRQ(NotionIntegrations.GRANT_TYPE, code,
                NotionIntegrations.REDIRECT_URI);
        String bodyString = JSONObject.toJSONString(authenticationRequest);

        // headers
        Headers headers = Headers.of(HEADERS_MAP);
        // body
        RequestBody postBody = RequestBody.create(bodyString, MediaType.parse(CONTENT_TYPE));
        // build request
        Request request = new Request.Builder().headers(headers).url(AUTH_BASE_URL).post(postBody).build();

        // send request
        Pair<Integer, String> result = this.createToken(request);

        // process response
        if (result.getKey() == 200) {
            // Unicode字符串转为普通字符串
            String unicodeBodyString;
            try {
                unicodeBodyString = UnicodeUtil.toString(result.getValue());
            } catch (Exception e) {
                log.error("[EXCEPTION] createToken UnicodeUtil.toString fail", e);
                unicodeBodyString = result.getValue();
            }
            // 校验是否为json格式
            AssertUtils.notTrueWithBizExp(JsonUtil.isJson(unicodeBodyString), ErrorCodeEnum.RESP_IS_NOT_JSON);
            // 解析json
            return JSONObject.parseObject(unicodeBodyString, AuthenticationRS.class);
        } else {
            log.error("[FAIL] createToken httpCode={}, respBody={}", result.getKey(), result.getValue());
            throw RoadException.create(ErrorCodeEnum.INTERNAL_ERROR, "createToken error");
        }
    }


    public Pair<Integer, String> createToken(Request request) throws IOException {

        try (Response response = NotionClientConfig.CLIENT.newCall(request).execute()) {
            String bodyString = Objects.requireNonNull(response.body()).string();
            log.info("createToken code={},bodyString={}", response.code(), bodyString);
            return Pair.of(response.code(), bodyString);
        } catch (Exception e) {
            log.error("[EXCEPTION] createToken", e);
            throw e;
        }
    }
}
