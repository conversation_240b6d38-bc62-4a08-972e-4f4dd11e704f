package com.maixi.road.notion.remote.impl.cloud;

import com.maixi.road.cloudfunc.notion.NotionCloudFunctionApi;
import com.maixi.road.notion.remote.api.PageApi;
import com.maixi.road.cloudfunc.notion.dto.rq.ToNotionRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import com.maixi.road.common.integration.notion.model.page.Page;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.IOException;

@Slf4j
@Service("pageCloudClient")
public class PageCloudClient implements PageApi {

    @Resource
    private NotionCloudFunctionApi notionClientApi;

    public PageCreateRS createPage(String notionApiKey, Page page, String unionId)
            throws IOException {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("PageCloudClient.createPage");
        try {
            return notionClientApi.sendToNotion(new ToNotionRQ(notionApiKey, page, unionId));
        } finally {
            stopWatch.stop();
            log.info("[{}]. rt={}", stopWatch.lastTaskInfo().getTaskName(), stopWatch.getTotalTimeSeconds());
        }
    }
}
