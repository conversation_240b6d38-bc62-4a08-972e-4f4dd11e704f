package com.maixi.road.notion.remote.impl.cloud;

import com.maixi.road.cloudfunc.notion.NotionCloudFunctionApi;
import com.maixi.road.cloudfunc.notion.dto.rq.ToNotionRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import com.maixi.road.notion.remote.api.SearchApi;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

@Slf4j
@Component("searchCloudClient")
public class SearchCloudClient implements SearchApi {

    @Resource
    private NotionCloudFunctionApi notionClientApi;

    @Override
    public Pair<String, String> searchDb(String unionId, String accessToken, Integer relationId) {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("searchCloudClient.searchDb");
        try {
            PageCreateRS createResult = notionClientApi.sendToNotion(new ToNotionRQ(unionId, accessToken, relationId));
            if (createResult != null && StringUtils.isNoneBlank(createResult.getArticleDbId(), createResult.getMsgDbId())) {
                return Pair.of(createResult.getArticleDbId(), createResult.getMsgDbId());
            }
            return Pair.of(null, null);
        } finally {
            stopWatch.stop();
            log.info("[{}]. rt={}", stopWatch.lastTaskInfo().getTaskName(), stopWatch.getTotalTimeSeconds());
        }
    }


}
