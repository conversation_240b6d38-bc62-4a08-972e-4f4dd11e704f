package com.maixi.road.notion.remote.impl.local;

import cn.hutool.core.text.UnicodeUtil;
import com.alibaba.fastjson.JSON;
import com.maixi.road.common.core.utils.DateUtils;
import com.maixi.road.common.integration.notion.model.page.Page;
import com.maixi.road.notion.remote.api.PageApi;
import com.maixi.road.notion.remote.config.NotionClientConfig;
import com.maixi.road.notion.remote.manager.Article2NotionPageAdapter;
import com.maixi.road.cloudfunc.notion.dto.rs.PageCreateRS;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Objects;

@Slf4j
@Service("pageLocalClient")
public class PageLocalClient implements PageApi {

    private static final String PAGE_URL = "https://api.notion.com/v1/pages";
    @Resource
    private Article2NotionPageAdapter article2NotionPageAdapter;

    /**
     * 创建 page
     */
    public PageCreateRS createPage(String notionApiKey, Page page, String unionId)
            throws IOException {
        log.info("[createPage]. startTime={}", DateUtils.formatLocalDateTime(LocalDateTime.now()));
        article2NotionPageAdapter.processOverLengthCase(page.getChildren());
        Request request = article2NotionPageAdapter.commonRequestBuilder(notionApiKey)
                .url(PAGE_URL)
                .method("POST", article2NotionPageAdapter.toBody(article2NotionPageAdapter.toJson(page)))
                .build();

        PageCreateRS result;
        try (Response response = NotionClientConfig.CLIENT.newCall(request).execute()) {
            String bodyString = Objects.requireNonNull(response.body()).string();
            result = JSON.parseObject(UnicodeUtil.toString(bodyString), PageCreateRS.class);
            int code = response.code();
            if (code == 200) {
                log.info("[createPage]. 创建页面成功, code={}, unionId={}, pageId={}", 200, unionId, result.getId());
            } else {
                log.error("[createPage]. 创建页面失败, code={}, unionId={}, result={}", code, unionId, bodyString);
            }
        }
        log.info("[createPage]. endTime={}", DateUtils.formatLocalDateTime(LocalDateTime.now()));
        return result;
    }
}
