package com.maixi.road.notion.remote.api;

import com.maixi.road.cloudfunc.notion.dto.rq.BlockAppendRQ;
import com.maixi.road.cloudfunc.notion.dto.rs.NotionBaseRS;

import java.io.IOException;

public interface BlockApi {

    /**
     * 追加页面内容
     * @param notionApiKey
     * @param blockId
     * @param appendRequest
     * @param unionId
     * @return
     * @throws IOException
     */
    NotionBaseRS appendBlockChildren(String notionApiKey,
                                     String blockId,
                                     BlockAppendRQ appendRequest,
                                     String unionId) throws IOException;
}